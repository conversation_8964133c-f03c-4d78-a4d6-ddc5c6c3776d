{"id": "dJ1ecr7gA5oVqndGv8cjUC", "title": "Application_380", "pages": [{"id": "pzeUim2FmnxYtZz23Shino", "title": "Page01", "chartMap": {"ww1N2jQXXnXgHKtmy4jDxd": {"id": "ww1N2jQXXnXgHKtmy4jDxd", "graphName": "MyGraph", "title": "GraphWidget", "type": "internal-graph", "refreshRate": 0, "queryType": "pattern", "searchPattern": [{"id": "b3d6abe7-b92a-4df4-8ec6-367120dc1746", "data": "City", "type": "vertex", "alias": "City", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.city"}, "label": "City.city"}]}, {"id": "6c5a2cba-84b7-467e-8800-f0b16cee147e", "data": "2", "type": "PARAM", "paramName": "vertex_limit", "paramType": "INT", "paramOperator": "eq", "paramGlobalInput": ""}], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {}}, "czLgQtx8eyDPN8NfrCbdeu": {"id": "czLgQtx8eyDPN8NfrCbdeu", "graphName": "MyGraph", "title": "BarWidget", "type": "bar", "refreshRate": 0, "queryType": "pattern", "searchPattern": [{"id": "3929998f-850a-4c67-91e3-b3c8a51b2f38", "data": "City", "type": "vertex", "alias": "City", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.city"}, "label": "City.city"}]}, {"id": "d97e09e3-7604-4036-8c82-93023912e7d2", "data": "2", "type": "PARAM", "paramName": "vertex_limit", "paramType": "INT", "paramOperator": "eq", "paramGlobalInput": ""}], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {}}, "jCpg1izBrxdn5HoWvTKNir": {"id": "jCpg1izBrxdn5HoWvTKNir", "graphName": "MyGraph", "title": "TableWidget", "type": "table", "refreshRate": 0, "queryType": "pattern", "searchPattern": [{"id": "80c4eeb1-4e97-452b-8883-253d469dc003", "data": "City", "type": "vertex", "alias": "City", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.city"}, "label": "City.city"}]}, {"id": "bac6744b-9c94-44b3-9cf5-a4071fdb6e2e", "data": "3", "type": "PARAM", "paramName": "vertex_limit", "paramType": "INT", "paramOperator": "eq", "paramGlobalInput": ""}], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {}}, "eNxWPhgRLAYPLxqfDALbGM": {"id": "eNxWPhgRLAYPLxqfDALbGM", "graphName": "MyGraph", "title": "New Widget", "type": "map", "refreshRate": 0, "queryType": "pattern", "searchPattern": [{"id": "53daf65d-5eea-4176-9ea0-48e28d84b719", "data": "City", "type": "vertex", "alias": "City", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.city"}, "label": "City.city"}]}, {"id": "1b93ee44-2adf-45ef-a41e-c1095482d552", "data": "12", "type": "PARAM", "paramName": "vertex_limit", "paramType": "INT", "paramOperator": "eq", "paramGlobalInput": ""}], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {}}}, "layouts": {"md": [{"w": 4, "h": 12, "x": 0, "y": 0, "i": "ww1N2jQXXnXgHKtmy4jDxd", "moved": false, "static": false}, {"w": 5, "h": 12, "x": 4, "y": 0, "i": "czLgQtx8eyDPN8NfrCbdeu", "moved": false, "static": false}, {"w": 4, "h": 12, "x": 0, "y": 12, "i": "jCpg1izBrxdn5HoWvTKNir", "moved": false, "static": false}, {"w": 5, "h": 16, "x": 4, "y": 12, "i": "eNxWPhgRLAYPLxqfDALbGM", "moved": false, "static": false}]}, "globalParameters": {}, "isNew": false, "iconURL": "/studio/assets/gvis/icons/builtin/64/196-apple.png"}, {"id": "9onqxDaSTb9KpSGB3LRMUQ", "title": "Page00", "chartMap": {"wFziFRJja7WJ7tfJz1iqkb": {"id": "wFziFRJja7WJ7tfJz1iqkb", "graphName": "MyGraph", "title": "SankeyWidget", "type": "sankey", "refreshRate": 0, "queryType": "pattern", "searchPattern": [{"id": "59d37550-0854-4606-9803-e2f30459673e", "data": "City", "type": "vertex", "alias": "City", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.city"}, "label": "City.city"}]}, {"id": "06f0449e-bcf0-425e-9f71-3f24e1311a0c", "data": "CASE_IN_CITY", "type": "edge", "from": "InfectionCase", "to": "City", "edgePairs": [], "direction": 0, "alias": "CASE_IN_CITY"}, {"id": "8810d613-d7f7-4adc-9409-b1de678fdd23", "data": "InfectionCase", "type": "vertex", "alias": "InfectionCase"}, {"id": "0c9b5bae-84db-4d14-b569-07a3f0d0979d", "data": "variable2", "type": "PARAM", "paramName": "vertex_limit", "paramType": "INT", "paramOperator": "eq", "paramGlobalInput": "variable2"}], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {}}, "9s6u6duocgXm6jXps2irkm": {"id": "9s6u6duocgXm6jXps2irkm", "graphName": "MyGraph", "title": "InputWidget", "type": "Inputs", "refreshRate": 0, "queryType": "pattern", "searchPattern": [], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {"inputStates": [{"id": "input_aJKSAFqGJs8TKFsUxMp3aT", "name": "variable1", "dataType": "String", "widgetType": "Input", "settings": {}}, {"id": "input_iXNEK462f8BHqkKTWq8nuv", "name": "variable2", "dataType": "Number", "widgetType": "Input", "settings": {"min": 0, "max": 100, "step": 1}, "value": ""}, {"id": "input_gi88wnrz22ahTGCRER3FRg", "name": "variable3", "dataType": "String", "widgetType": "Dropdown", "settings": {}, "value": ""}]}}, "q3BH41EmGqVmewBvyqBcMy": {"id": "q3BH41EmGqVmewBvyqBcMy", "graphName": "MyGraph", "title": "LineWidget", "type": "line", "refreshRate": 0, "queryType": "pattern", "searchPattern": [{"id": "a29b6784-66ef-4a1b-9e7e-3998098cf956", "data": "City", "type": "vertex", "alias": "City", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.city"}, "label": "City.city"}]}, {"id": "491b2323-e434-4943-b00c-956f09776e30", "data": "variable2", "type": "PARAM", "paramName": "vertex_limit", "paramType": "INT", "paramOperator": "eq", "paramGlobalInput": "variable2"}], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {}}}, "layouts": {"md": [{"w": 4, "h": 12, "x": 0, "y": 0, "i": "wFziFRJja7WJ7tfJz1iqkb", "moved": false, "static": false}, {"w": 4, "h": 12, "x": 4, "y": 0, "i": "9s6u6duocgXm6jXps2irkm", "moved": false, "static": false}, {"w": 4, "h": 12, "x": 0, "y": 12, "i": "q3BH41EmGqVmewBvyqBcMy", "moved": false, "static": false}]}, "globalParameters": {"variable1": {"value": "", "type": "STRING", "name": "variable1", "id": "input_aJKSAFqGJs8TKFsUxMp3aT"}, "variable2": {"value": 5, "type": "NUMBER", "name": "variable2", "id": "input_iXNEK462f8BHqkKTWq8nuv"}, "variable3": {"value": "", "type": "STRING", "name": "variable3", "id": "input_gi88wnrz22ahTGCRER3FRg"}}, "isNew": false, "iconURL": "/studio/assets/gvis/icons/builtin/64/076-brain.png"}, {"id": "hDrq9bnBPDNfpLAxnYLpUx", "title": "Page03", "chartMap": {"o4amsg1aNReEiEhZJUZU9R": {"id": "o4amsg1aNReEiEhZJUZU9R", "graphName": "MyGraph", "title": "PieWidget", "type": "pie", "refreshRate": 0, "queryType": "pattern", "searchPattern": [{"id": "93a8785c-eb35-43ca-881f-49e0d9271c6f", "data": "City", "type": "vertex", "alias": "City", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.city"}, "label": "City.city"}]}, {"id": "fb82b130-8b2e-4775-b6f8-38bf4aa62a1f", "data": "5", "type": "PARAM", "paramName": "vertex_limit", "paramType": "INT", "paramOperator": "eq", "paramGlobalInput": ""}], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {"tableIndex": 0, "tableHeaders": ["Matched pattern", "City"], "category": [{"id": "City.city_id", "type": "string"}], "value": [{"id": "City.academy_ratio", "type": "number"}]}}, "xyYSct1Kk1rjqmRs1USPVz": {"id": "xyYSct1Kk1rjqmRs1USPVz", "graphName": "MyGraph", "title": "SingleWidget", "type": "value", "refreshRate": 0, "queryType": "pattern", "searchPattern": [{"id": "05ed9389-4287-4eb7-b197-5bf9add9c693", "data": "City", "type": "vertex", "alias": "City", "orderBy": [{"asc": true, "expression": {"type": "AttrVariable", "value": "alias_schema_City_0.city"}, "label": "City.city"}]}, {"id": "eb2192cc-ee35-489f-a4c1-1ac3398a1643", "data": "2", "type": "PARAM", "paramName": "vertex_limit", "paramType": "INT", "paramOperator": "eq", "paramGlobalInput": ""}], "query": "", "staticData": "{}", "patternLimit": 300, "chartSettings": {}}}, "layouts": {"md": [{"w": 4, "h": 12, "x": 0, "y": 0, "i": "o4amsg1aNReEiEhZJUZU9R", "moved": false, "static": false}, {"w": 4, "h": 12, "x": 4, "y": 0, "i": "xyYSct1Kk1rjqmRs1USPVz", "moved": false, "static": false}]}, "globalParameters": {}, "isNew": false, "iconURL": "https://c7bc9cea13ef4c9a93c65b4cbe8ce53.i.tgcloud-dev.com/assets/img/user-uploaded-icons/hongyan_li-1668050383218.png"}], "version": "1673425142808551503", "iconURL": "https://c7bc9cea13ef4c9a93c65b4cbe8ce5.i.tgcloud-dev.com/assets/img/user-uploaded-icons/export.png", "defaultGraph": "MyGraph", "owner": "<EMAIL>"}