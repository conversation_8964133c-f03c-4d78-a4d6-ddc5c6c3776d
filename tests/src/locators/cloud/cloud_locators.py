"""
The locators for Cloud elements, update the elements
when we have any major or minor UI changes.
"""


class LogInLocators:
    sign_up_btn = "//button[text()='Sign up']"
    login_btn = "//button[text()='Log in']"
    org_input = "#organizationName"
    continue_btn = "//button[text()='Continue']"
    user_input = "#username"
    password_input = "#password"
    google_continue_btn = "//button[contains(.,'Continue with Google')]"
    google_signin_div = "#headingText"
    google_user_input = "#username"
    google_next_btn = "//button[contains(.,'Next')]"
    google_pwd_input = "//input[@name='password']"
    # new UI
    login_with_org_btn = "//a[text()='Login with organization']"
    org_title_h1 = "//h1[text()='Enter Organization']"
    continue_login_btn = "//button[@data-action-button-primary='true' and text()='Continue']"


class LogOutLocators:
    account_icon = "//img[@src='../../assets/icons/account']"
    logout_span = "//span[contains(.,'Sign out')]"
    login_page_div = "//div[text()='Log in to TigerGraph Cloud']"

class CloudHomeLocators:
    home_endpoint="/app/dashboard"
    tools_endpoint="/app/tools"
    clusters_endpoint="/app/clusters"
    users_endpoint="/app/users"
    activities_endpoint="/app/activities"
    network_endpoint="/app/network/vpc-peering"
    billing_endpoint="/app/billing"
    admin_endpoint="/app/admin/usage"
    tgcloud_icon="//img[contains(@src, '/assets/logo.')]"

class CloudClusterLocators:
    clusters_btn = "//div[@data-baseweb='typo-headingmenu' and text()='Clusters']"
    create_cluster_btn = "//button[text()='Create Cluster']"
    cluster_container = "//div[@id='ClustersContainer']"
    cluster_name_link = "//div[@data-baseweb='typo-headingsmall' and contains(.,'{}')]"
    pause_cluster_div = "//div[text()='Pause cluster']"
    resize_cluster_div = "//div[text()='Resize cluster']"
    resume_cluster_div = "//div[text()='Resume cluster']"
    terminate_cluster_div = "//div[text()='Terminate cluster']"
    cluster_name_in_termiante_alert = "//span[@data-baseweb='typo-Subtitle']"
    enter_cluster_name_input = "//input[@placeholder='enter cluster name']"
    terminate_btn = "//button[text()='Terminate']"
    pause_btn = "//button[text()='Pause']"
    resume_btn = "//button[text()='Resume']"
    access_management_btn = "//button[text()='Access Management']"
    cluster_active_div = "//div[text()='Active']"
    cluster_paused_div = "//div[text()='Paused']"
    cluster_loader_div = "//div[@testid='loader']"
												   


class CloudClusterCreateLocators:
    create_cluster_btn = "//button[text()='Create Cluster']"
    navigator = "//nav"
    nav_cluster_name_and_version = (
            navigator + "//div[text()='Cluster name and version']"
    )
    nav_cloud_provider_and_region = (
            navigator + "//div[text()='Cloud provider and region']"
    )
    nav_instance_settings = navigator + "//div[text()='Instance settings']"
    nav_advanced_settings = navigator + "//div[text()='Advanced settings']"
    create_cluster_form = "#createClusterForm"
    cluster_name_and_version_form = "//div[@id='cluster-name']"
    cloud_provider_and_region_form = "//div[@id='cloud-provider']"
    instance_settings_form = "//div[@id='instance-settings']"
    advanced_settings_form = "//div[@id='advanced-settings']"
    tg_version_selector = (
            cluster_name_and_version_form + "//div[@data-baseweb='select']"
    )
    cluster_name_input = "input[name=Name]"
    description_input = "input[name=Description]"
    aws_platform_block = cloud_provider_and_region_form + "//img[@alt='AWS']"
    azure_platform_block = cloud_provider_and_region_form + "//img[@alt='Azure']"
    gcp_platform_block = cloud_provider_and_region_form + "//img[@alt='GCP']"
    region_selector = cloud_provider_and_region_form + "//div[@data-baseweb='select']"
    cluster_page_dropdown_list = "//ul[@role='listbox']/li/div"
    cluster_name_input = "input[name='Name']"
    description_name_input = "input[name='Description']"
    region_selector = cloud_provider_and_region_form + "//div[@data-baseweb='select']"
    instance_settings_selectors = (
            "(" + instance_settings_form + "//div[@data-baseweb='select'])"
    )
    instance_type_selector = instance_settings_selectors + "[1]"
    disk_size_selector = instance_settings_selectors + "[2]"
    partition_factor_selector = instance_settings_selectors + "[3]"
    replication_factor_selector = instance_settings_selectors + "[4]"
    enabled_dropdown_items = "//ul[@role='listbox']//li/div[not(@aria-readonly)]"
    enabled_mlwb = "//label[contains(.,'Enable Machine Learning Workbench')]//div[2]"


class CloudAccessManagementLocators:
    add_users_div = "//div[@role='dialog' and contains(.,'Add Users To')]"
    add_dbuser_div = "//div[@role='dialog' and contains(.,'Database User')]"
    remove_users_confirm_div = "//div[@role='dialog' and contains(.,'Remove User From This Cluster')]"
    remove_dbuser_confirm_div = "//div[@role='dialog' and contains(.,'Remove Database User')]"
    tools_btn = "//button[text()='Tools']"
    attach_users_btn = "//button[text()='Add Users']"
    database_access_btn = "//button[text()='Database Access']"
    role_management_btn = "//button[text()='Role Management']"
    add_users_confirm_btn = add_users_div + "//button[text()='Add Users']"
    add_users_cancel_btm = add_users_div + "//button[text()='Cancel']"
    add_users_input = add_users_div + "//input"
    delete_users_confirm_btn = remove_users_confirm_div + "//button[text()='Remove']"
    delete_users_cancel_btn = remove_users_confirm_div + "//button[text()='Cancel']"
    add_dbuser_btn = "//button[contains(.,'Add Database User')]"
    user_list_div = "//div[contains(.,'Email Address')]"
    dbuser_name_input = add_dbuser_div + "//input[@autocomplete='on']"
    dbuser_pwd_input = add_dbuser_div + "//input[@autocomplete='new-password']"
    add_dbuser_confirm_btn = add_dbuser_div + "//button[text()='Add User']"
    add_dbuser_cancel_btn = add_dbuser_div + "//button[text()='Cancel']"
    auto_generate_pwd_btn = "//button[text()='Auto Generate Password']"
    update_dbuser_btn = "//button[text()='Update User']"
    delete_dbuser_confirm_btn = remove_dbuser_confirm_div + "//button[text()='Remove']"
    delete_dbuser_cancel_btn = remove_dbuser_confirm_div + "//button[text()='Cancel']"
    role_management_iframe = "/html/body/app-root/app-role-management-cloud"
    graph_selector = role_management_iframe + "//mat-select[@id='mat-select-0']"
    role_selector = role_management_iframe + "//mat-select[@id='mat-select-1']"
    graph_option = "//mat-option[2]"
    view_role_details_btn = role_management_iframe + "//button[contains(.,'View role details')]"
    role_details_div = "//div[contains(.,'Role details')]"
    view_privileges_mat = role_details_div + "//mat-expansion-panel-header"
    close_role_details_btn = role_details_div + "//button[contains(.,'CLOSE')]"
    role_save_btn = role_management_iframe + "//button[contains(.,'SAVE')]"
    role_discard_btn = role_management_iframe + "//button[contains(.,'DISCARD')]"


class CloudClusterResizeLocators:
    cluster_name_resized_div = "//div[@data-baseweb='typo-headinglarge' and contains(.,'{}')]"
    resize_instance_btn = "//button[text()='Resize Instance']"
    resize_instance_size_btn = "//button[text()='Resize instance size']"
    resize_disk_size_btn = "//button[text()='Resize disk size']"
    resize_disk_btn = "//button[text()='Resize Disk']"
    resize_confirm_btn = "//button[text()='Confirm']"
    cluster_being_resized_div = "//div[@data-baseweb='typo-headingsmall' and contains(.,'Your cluster is being resized')]"
    clusters_page_instance_type_div = "//section[contains(.,'{}')]//div[contains(text(), '{} vCPU, {}GB Memory')]"
    clusters_page_storage_size_div = "//section[contains(.,'{}')]//div[text()='{}GB']"
    resize_option_selector = "//div[@data-baseweb='select']"


class CloudClusterAccessToolsLocators:
    # tool name keys
    gst = 'GraphStudio'
    ins = 'TigerGraph Insights'
    gsql = 'GSQL Shell'
    adp = 'Admin Portal'
    gql = 'GraphQL'
    mlwb = 'Machine Learning Workbench'

    # tools convert to cloud url
    cloud_tools_url = {
        "studio": gst,
        "admin": adp,
        "gsql": gsql,
        "insights": ins,
        "graphql": gql
    }

    # tools naming convert dict
    convert = {
        gst: "graphstudio",
        ins: "insights",
        gsql: "gsqlshell",
        adp: "adminportal",
        gql: "graphql"
    }
    # tools naming dict
    tool_div_selector_for = "//div[text()='{}']"
    view_more_tools_div = "//div[text()='View more tools']"
    cluster_name_tools_div = "//div[@data-baseweb='typo-headinglarge' and contains(.,'{}')]"

    # tool card divs
    accessable_tools_card_div_selector_for = "//div[@class='{}']//section[@data-baseweb='card']//div[@data-baseweb='typo-headingmenu' and contains(.,'{}')]"
    # tool card div match patterns for according tool name
    match_pattern_tools = {
        gst: "Design, develop, map and load",
        ins: "applications",
        gsql: "GSQL",
        adp: "system",
        gql: "GraphQL",
        mlwb: "machine learning"
    }

    # logo svg
    tool_logo_svg_selector_for = "//img[@alt='{}']"
    mlwb_logo_svg = "//div[@id='jp-MainLogo']//*[local-name()='svg' and @data-icon='ui-components:jupyter']"
    cloud_head_menu = ".cloud-headerap.cloud-headeraq"
    cloud_head_new_menu = ".cloud-headerau.cloud-headerav.cloud-headeraw.cloud-headerax"
    cloud_cluster_menu = '//div[normalize-space(text())="Clusters"]'

    # menu
    menu_svg = "//img[contains(@src,'/assets/menu.')]"
    menu_nav = "//nav[@role='navigation']"
    menu_panel_div = "//div[@cdkscrollable]"
    menu_panel_p_selector_for = menu_panel_div + "//p[text()='{}']"
    menu_clusters_p = menu_panel_p_selector_for.format("Cluster")

    # graphstudio
    gst_home_design_schema_btn = "//a[text()=' Design Schema ']"
    gst_menu_panel_design_schema_p = "//p[text()='Design Schema']"
    gst_menu_panel_load_data_p = "//p[text()='Load Data']"
    gst_menu_panel_write_queries_p = "//p[text()='Write Queries']"
    gst_switch_graph_btn = "//button[@aria-label='Switch graph']"
    imported_query_p_selector_for = "//div[@class='mat-list-text']//p[@class='mat-line' and contains(text(), '{}')]"
    imported_graph_name_span_selector_for = "//*[@id='one-single-graph']/div[1]/div/span[contains(text(), '{}')]"

    # Insights
    ins_my_apps_tab_btn = "//*[contains(text(), 'My Application')]"  # 3.8.0
    ins_other_apps_tab_btn = "//*[contains(text(), 'Applications Shared with Me')]"  # 3.8.0
    ins_new_app_span = "//span[text()='New Application']"

    # admin portal
    # monitor
    adp_menu_panel_monitor_p = "//p[text()='Monitor']"
    adp_menu_panel_monitor_queries_p = "//p[text()='Queries']"  # only for 3.8.0
    adp_monitor_queries_overview_div = "//div[text()='Overview']"
    adp_monitor_queries_overview_current_running_queries_p = "//p[text()='Current running queries']"
    # management
    adp_menu_panel_management_p = "//p[text()='Management']"
    adp_menu_panel_management_components_p = "//p[text()='Components']"
    # backup and restore
    adp_menu_panel_backup_restore_p = "//p[text()='Backup & Restore']"

    # graphql
    gql_exec_query_btn = "//button[contains(@aria-label, 'Execute query')]"
    gql_Prettify_btn = "//button[contains(@aria-label, 'Prettify query')]"
    gql_Merge_btn = "//button[contains(@aria-label, 'Merge')]"
    gql_Copy_btn = "//button[contains(@aria-label, 'Copy')]"
    gql_History_btn = "//button[contains(@aria-label, 'History')]"
    gql_no_existing_graph_msg_p = "//p[contains(text(), 'There is no existing graphs')]"

    # gsql shell
    gsql_help_shortcuts_h3 = "//h3[contains(., 'Editor Shortcuts')]"

    # MlWB
    mlwb_readme_span = "//li[contains(@title,'Name: README.md') and contains(@title,'GraphML')]//span[text()='README.md']"
    mlwb_main_dock_pannel = "//div[@id='jp-main-dock-panel']"
    mlwb_title_h1 = "//h1[@id='TigerGraph-ML-Workbench:-Graph-ML-as-a-Service']"

    # tools img in cloud header
    header_app_icon = "//img[contains(@src,'application')]"
    header_tool_icon = "//a[contains(@href,'{}')]"
    tools_icon_in_header = {
        gst: "studio",
        ins: "insights",
        gsql: "gsql",
        adp: "admin",
        gql: "graphql",
        mlwb: "mlwb"
    }

    
class CloudClusterDetailLocators:
    detail_btn = "//button[text()='Details']"
    cost_btn = "//button[text()='Cost']"
    activities_btn = "//button[text()='Activities']"

    # detail page
    instance_info_div = "//div[text()='Instance Information']"
    detail_body_value_div = "//div[text()='{}']"
    detail_body_key_div = "//div[text()='{}']/ancestor::div[@style='justify-content: space-between; gap: 16px;']/div[1]"

    # cost page
    more_info_div = "//div[@aria-haspopup='true']"
    estimated_cost_div = "//div[text()='Estimated monthly Cost']"
    hourly_cost_span = "//div[@data-baseweb='typo-Label' and contains(.,'hour')]/span[1]"
    start_time_td = "//div[@data-baseweb='table-builder-semantic']//td[2]"
    end_time_td = "//div[@data-baseweb='table-builder-semantic']//td[3]"
    totally_cost = "//div[@data-baseweb='table-builder-semantic']//td[4]"

    # activities page
    events_div = "//div[@data-baseweb='typo-Body1' and contains(.,'{}')]"
