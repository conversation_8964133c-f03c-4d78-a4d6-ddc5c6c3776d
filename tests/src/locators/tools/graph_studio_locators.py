class GSTHomeLocators:
    cloud_version_header = ".cloud-headerdr.cloud-headerds"
    cloud_version_new_header = "//span[contains(.,'version')]"
    nav_home = "//p[@id='Home icon']"
    nav_design_schema = "//p[text()='Design Schema']"
    nav_map_data_to_graph = "//p[text()='Map Data To Graph']"
    nav_load_data = "//p[text()='Load Data']"
    nav_explore_graph = "//p[text()='Explore Graph']"
    nav_build_graph_patterns = "//p[text()='Build Graph Patterns']"
    nav_write_queries = "//p[text()='Write Queries']"
    nav_actions = "//p[text()='Actions']"
    import_solution_btn = "//a[normalize-space(text())='Import An Existing Solution']"
    export_solution_btn = "//a[normalize-space(text())='Export Current Solution']"
    export_solution_dialog = "div.export-options-dialog"
    export_solution_checkbox_on_dialog = "div.mat-checkbox-inner-container"
    export_solution_confirm_btn = "//span[normalize-space(text())='CONFIRM']"
    import_solution_input = "//input[@aria-label='Import An Existing Solution']"
    dialog_window = "//mat-dialog-actions"
    overwrite = ".mat-dialog-actions > .ng-star-inserted"
    #  Migrate From Relational Database (alpha)
    migrateDatabaseIcon = "#start-rdbms-migration-title"
    mySQLbutton = 'button[aria-label="MySQL"]'
    postgreSQLbutton = 'button[aria-label="PostgreSQL"]'
    rdbmsServer = "#rdbms-server"
    rdbmsPort = "#rdbms-port"
    rdbmsDatabase = "#rdbms-database"
    rdbmsUsername = "#rdbms-username"
    rdbmsPassword = "#rdbms-password"
    nextButtonCommon = '//span[text()=" NEXT "]'
    migrateButton = '//span[text()=" MIGRATE "]'
    verifyButton = '//span[text()=" VERIFY "]'

    # global view
    globalGraphButton = "button.graph-menu-btn"
    graphButtons = "span.graph-name-in-list"
    getAllGraphNameList = "span.graph-name-in-list"
    getAllDeleteButtonList = "button.drop-graph-icon"
    userRole = ".user-role > span"

    # design schema
    add_graph_button = '#add-graph-btn'
    input_graph_name = 'input[placeholder="Graph name"]'
    create_graph_button = 'mat-dialog-actions > .mat-primary'
    hint_container = '.hint-container'
    publish_schema_button = 'button[aria-label="Save schema"]'
    add_vertex_button = 'button[aria-label="Add local vertex type"]'
    add_edge_button = 'button[aria-label="Add local edge type"]'
    input_vertex_name = 'input[placeholder="Vertex type name"]'
    delete_item_button = 'button[aria-label="Delete"]'
    publishButton = 'button[aria-label="Save schema"]'
    add_edge_button = 'button[aria-label="Add local edge type"]'
    delete_edge_button = 'button[aria-label="Delete"]'
    input_edge_name = 'input[placeholder="Edge type name"]'
    directed_and_other_checkboxs = 'div > mat-checkbox.mat-checkbox'
    save_item_in_panel = 'button.panel-btn.mat-icon-button.mat-button-base.mat-primary'
    save_edge_in_panel = 'button.panel-btn.mat-icon-button.mat-button-base.mat-primary'
    cancel_edge_in_panel = 'button[aria-label="CANCEL"]'
    # add_attributes_in_panel = 'button[aria-label="Add"]'
    add_attributes_in_panel = 'div.attribute-box-container button[aria-label="Add"]'
    select_icon_button = '//button/span[normalize-space(text())="Select icon"]'
    icon_filter = 'input[placeholder="Filter"]'
    icon_buttons = '.select-button'
    string_option = '//mat-option/span[normalize-space(text())="STRING"]'
    int_option = '//mat-option/span[normalize-space(text())="INT"]'
    schema_graph = '.schemaGraphWrapper'
    data_graph = '.dataGraphWrapper'
    # add attribute
    input_attribute_names = 'input[placeholder="Attribute name"]'
    select_type = 'mat-select[aria-label="Attribute type"]'
    int_type = '//span[normalize-space(text())="INT"]'
    uint_type = '//span[normalize-space(text())="UINT"]'
    double_type = '//span[normalize-space(text())="DOUBLE"]'
    string_type = '//span[normalize-space(text())="STRING"]'
    datetime_type = '//span[normalize-space(text())="DATETIME"]'
    map_type = '//span[normalize-space(text())="MAP"]'
    verify_button = '//span[normalize-space(text())="VERIFY"]'


    # map data to graph
    # 1:publish, 2:add data source ....
    toolbars = ".toolbar > .ng-star-inserted"
    add_data_file_button = 'button[aria-label="Add data file"]'
    auto_mapping_button = 'button[aria-label="Auto mapping"]'
    # 1:localfile, 2:S3.....
    add_local_file_button = 'button[aria-label="Local File"]'
    add_s3_button = 'button[aria-label="Amazon S3"]'
    add_gcs_button = 'button[aria-label="Google Cloud Storage"]'
    add_abs_button = 'button[aria-label="Azure Blob Storage"]'
    people_csv_file = 'a[aria-label^="people.csv"]'
    people_json_file = 'a[aria-label^="people.json"]'
    friendship_csv_file = 'a[aria-label^="friendship.csv"]'
    add_button = 'button.add-btn'
    back_button = 'button.back-btn'
    dataSourceButtons = "button.data-source-type-button"
    deleteIcons = ".list-container .mat-icon-button"
    table_has_header_check = '.mat-checkbox'
    table_container = '.table-container'
    table_header_cells = '.sample-data-table .mat-header-row .mat-header-cell'
    s3_inputs = '#s3-datasource-input .mat-input-element'
    file_path_input = '#datasource-file-path-input'
    ui_e2e_trigger_container = "//div[contains(@class,'ui-e2e-trigger-container')]"
    gcs_service_account_key_input = '.service-account-key-input-e2e-trigger'
    gcs_datasource_input = '#gcs-datasource-input input'
    gcs_enable_button = '.enable-next-button-e2e-trigger'
    abs_connection_string = 'input[formcontrolname="absConnectionString"]'
    abs_connection_alias = 'input[formcontrolname="dataSourceName"]'

    data_source_connect_btn = '#datasource-connect-button'
    csv_button = 'button[aria-label="csv"]'
    json_button = 'button[aria-label="json"]'
    publishMapButton = 'button[aria-label="Save data mapping"]'
    publish_data_mapping_button = 'button[aria-label="Save data mapping"]'
    map_data_file_button = 'button[aria-label="Map data file to vertex or edge"]'

    # load data
    pauseButton = 'button[aria-label="Pause loading"]'
    imported_msg = "//span[normalize-space(text())='Solution is imported']"
    graph_menu_btn = ".graph-menu-btn"
    graph_list = "one-single-graph"
    delete_graph_button = "button.drop-graph-icon"
    current_graph = "//div[contains(@class,'graph-name')]"
    circle_progressBar = 'mat-progress-spinner[role="progressbar"]'
    run_install_query_circle_progressBar = 'div.query-running-dialog'
    importing_msg = "//p[normalize-space(text())='Importing existing solution ...']"
    statistics_result = "mat-row>mat-cell.cdk-column-number.mat-column-number"

    # explore graph
    pick_vertices_button = '.mat-raised-button.mat-button-base.mat-primary'

    # build graph patterns
    console = 'button[aria-label="Console"]'
    createNewPatternButton = "#add-new-pattern-btn"
    inputNewPatternName = 'input[placeholder="Graph pattern name"]'
    patternList = "#pattern-list-item"
    deletePatterns = "div.mat-line > .mat-icon-button.mat-button-base.ng-star-inserted"
    addVertexButton = 'button[aria-label="Add a vertex pattern"]'
    addEdgeButton = 'button[aria-label="Add an edge pattern"]'
    basicInfoButton = 'button[aria-labelledby="basic-info-label"]'
    outputButton = 'mat-checkbox[formcontrolname="output"]'
    vertexListButtons = 'mat-select[role="listbox"]'
    updateButtons = 'button[aria-label="UPDATE"]'
    limitButton = 'button[aria-labelledby="limit-label"]'
    # useLimitBox = 'mat-checkbox.use-limit-checkbox'
    useLimitBox = "input.mat-checkbox-input"
    savePatternButton = 'button[aria-label="Save the graph pattern"]'
    runPatternButton = 'button[aria-label="Run"]'
    view_graph_result_button = 'button[aria-label="Visualize graph result"]'
    input_exploration_name = 'input[aria-label="File name"]'
    exploration_description_area = 'textarea[formcontrolname="explorationDescription"]'

    # graph result tools button
    open_exploration_button = 'button[aria-label="Open exploration history"]'
    select_exploration_div = '//div[text()="saveExploration"]'
    select_open_exploration_div = '//div[text()="openExploration"]'
    exploration_title = 'div.file-title'
    delete_exploration_button = 'button[aria-label="Delete exploration"]'
    save_exploration_button = 'button[aria-label="Save exploration"]'
    export_graph_button = 'button[aria-label="Export"]'
    export_PNG_button = 'button[aria-label="Export PNG"]'
    export_CSV_button = 'button[aria-label="Export CSV"]'

    viewJsonResultButton = 'button[aria-label="View JSON result"]'
    viewTableResultButton = 'button[aria-label="View table result"]'
    assert_table_view_result_div = "//div[text()='assert_table_view']"
    table_view_results_button = "div.mat-tab-label-content"
    viewProblemsButton = 'button[aria-label="Problems"]'
    gridcell = "//td[@role='gridcell' and @class='ng-star-inserted']"
    result_count_div = '//div[@class="mat-paginator-range-label" and text()="{}"]'
    # select city
    cityVertex = '//span[text()=" City "]'
    provinceVertex = '//span[text()=" Province "]'

    # write queries
    downloadQuery = 'button[aria-label="Download query"]'
    searchButton = 'input[aria-label="search"]'
    queryList = "#query-list-item > div > mat-icon"
    query_list_number = "#query-list-item"
    deleteQueryButton = 'button[aria-label="Delete query and draft"]'
    createNewQueryButton = "#add-new-query-btn"
    openCypherLabel = '//label[@class="mat-radio-label" and contains(.,"openCypher")]'
    openCypherMark = '//div[@class="query-list-container"]//span[text()="OPENCYPHER"]'
    inputNewQueryName = 'input[aria-label="New query name"]'
    discardDraftButton = 'button[aria-label="Discard draft"]'
    installCurrentQueryButton = 'button[aria-label="Install query"]'
    downloadCurrentQueryButton = 'button[aria-label="Download"]'
    downloadAllQueryButton = 'button[aria-label="Download query"]'
    console_button = 'button[aria-label="Console"]'
    save_query_button = 'button[aria-label="Save query draft"]'
    run_query_button = 'button[aria-label="Run query in interpreted mode"]'
    run_installed_query_button = 'button[aria-label="Run query"]'
    input_parameter = 'input[placeholder="Vertex id"]'
    input_para_opencypher = '//div[@class="query-param-panel-params"]//input'
    run_query_button_below_input_parameter = 'button[type="submit"]'
    view_log_button = 'button[aria-label="View logs"]'

    # action
    clear_all_graph_button = "button.clear-all-graph-data-button"
    input_clear_all_graph = 'input[placeholder="clear all graph data"]'
    dialog_delete_permanently_button = "button.dialog-delete-permanently-button"
    cancel_delete_permanently_button = "button.dialog-button"
    rebuild_button = "button.rebuild-graph-data-button"


    # header
    # tools_header_icon_JS = 'document.querySelector("tools-head").shadowRoot.querySelector("div").querySelectorAll("img")[1].click()'
    # logout_icon_JS = 'document.querySelector("tools-head").shadowRoot.querySelector("div").querySelector(".logout").click()'
    # close_security_icon_JS = 'document.querySelector("tools-head").shadowRoot.querySelector("div").querySelector("#close-btn").click()'

    tools_header_icon_JS = 'document.querySelector("img.tools-head-icon").click()'
    logout_icon_JS = 'document.querySelector("div.logout").click()'
    close_security_icon_JS = 'document.querySelector("button#close-btn").click()'


    # horizontal progressbar
    progressBar = "mat-progress-bar.mat-progress-bar"
    confirm_button_on_import = "//span[normalize-space(text())='CONFIRM']"
    overwrite_button_on_import = "//span[normalize-space(text())='OVERWRITE']"
    # tools left
    # toolsIcon = "tools-head::shadow" " .tools_apps-btn"
    # adminIcon = "tools-head::shadow" " a[href='/admin/']"
    # insightsIcon = "tools-head::shadow" " a[href='/insights/']"
    # gshellIcon = "tools-head::shadow" " a[href='/gsql/']"
    # graphQLIcon = "tools-head::shadow" " a[href='/graphql/']"
    # studioIcon = "tools-head::shadow" " a[href='/studio/']"
    toolsIcon = "svg.tools_apps-btn"
    adminIcon = "a[href='/admin/']"
    insightsIcon = "a[href='/insights/']"
    gshellIcon = "a[href='/gsql/']"
    graphQLIcon = "a[href='/graphql/']"
    studioIcon = "a[href='/studio/']"

    # tools right
    # userIcon = "tools-head::shadow" " .tools-head-icon"
    # logoutButton = "tools-head::shadow" " .logout"
    # languageButton = "tools-head::shadow" " .language-btn"
    # englishButton = "tools-head::shadow" " .english"
    # chineseButton = "tools-head::shadow" " .chinese"
    # japaneseButton = "tools-head::shadow" " .japanese"
    # languageSpan = "tools-head::shadow" " .language-btn > div > span"
    # userNameSpan = "tools-head::shadow" " .tools_right-item > div > .userName"
    # licenseButton = "tools-head::shadow" " .license-btn"
    # switchThemeButton = "tools-head::shadow" " .switch-theme-btn"
    # themeSpan = "tools-head::shadow" " .switch-theme-btn > span"
    userIcon = "img.tools-head-icon"
    logoutButton = ".logout"
    languageButton = ".language-btn"
    englishButton = ".english"
    chineseButton = ".chinese"
    japaneseButton = ".japanese"
    languageSpan = ".language-btn > div > span"
    userNameSpan = ".tools_right-item > div > .userName"
    licenseButton = "div.license-btn"
    switchThemeButton = ".switch-theme-btn"
    themeSpan = ".switch-theme-btn > span"

    # helpButton = (
    #     "tools-head::shadow"
    #     " //span[text()='Help']"
    # )
    # GSTHelpButton = (
    #     "tools-head::shadow"
    #     " a[href='https://docs.tigergraph.com/gui/current/graphstudio/overview']"
    # )
    # GAPHelpButton = (
    #     "tools-head::shadow"
    #     " a[href='https://docs.tigergraph.com/gui/current/admin-portal/overview']"
    # )
    # insightsHelpButton = (
    #     "tools-head::shadow" " a[href='https://docs.tigergraph.com/insights/current']"
    # )
    # gShellHelpButton = (
    #     "tools-head::shadow"
    #     " a[href='https://docs.tigergraph.com/tigergraph-server/current/gsql-shell/web']"
    # )
    # graphQLHelpButton = (
    #     "tools-head::shadow" " a[href='https://docs.tigergraph.com/graphql/current/']"
    # )
    # thirdPartyNoticeButton = "tools-head::shadow" " .third-party-notice"
    GSTHelpButton = (
        "a[href='https://docs.tigergraph.com/gui/current/graphstudio/overview']"
    )
    GAPHelpButton = (
        "a[href='https://docs.tigergraph.com/gui/current/admin-portal/overview']"
    )
    insightsHelpButton = (
        "a[href='https://docs.tigergraph.com/insights/current']"
    )
    gShellHelpButton = (
        "a[href='https://docs.tigergraph.com/tigergraph-server/current/gsql-shell/web']"
    )
    graphQLHelpButton = (
        "a[href='https://docs.tigergraph.com/graphql/current/']"
    )
    thirdPartyNoticeButton = ".third-party-notice"


class DialogLocators:
    dialog_window = "//mat-dialog-actions"
    warning_head = "//h2[normalize-space(text())='Warning']"
    confirm_head = "//h2[normalize-space(text())='Confirm']"
    dialog_content = "mat-dialog-content"
    cancel_btn = "//span[normalize-space(text())='CANCEL']"
    overwrite_btn = "//span[normalize-space(text())='OVERWRITE']"
    continue_btn = "//span[normalize-space(text())='CONTINUE']"
    create_btn = "//span[normalize-space(text())='CREATE']"
    add_btn = "//span[normalize-space(text())='ADD']"
    save_btn = "//span[normalize-space(text())='SAVE']"
    open_btn = "//span[normalize-space(text())='OPEN']"


class GSTLoadDataLocators:
    nav_load_data = "//p[text()='Load Data']"
    start_resume_load_btn = "//button[@aria-label='Start/Resume loading']"
    pause_load_btn = "//button[@aria-label='Pause loading']"
    stop_load_btn = "//button[@aria-label='Stop loading']"
    show_left_chart_visual_info = 'button[aria-label*="visual information"]'
    close_left_chart_visual_info = '//button[contains(., "CLOSE ")]'
    # statistics_rows = ".statistics-table .mat-row"
    statistics_rows = ".info-container .statistics-table .mat-row"
    statistics_cell = ".mat-cell"

class GSTExploreGraphLocators:
    vertex_type_select = 'mat-select[aria-label="Vertex type"]'
    people_option = '//mat-option/span[normalize-space(text())="people"]'
    movie_option = '//mat-option/span[normalize-space(text())="movie"]'
    vertex_id_input = 'input[placeholder="Vertex id"]'
    search_button = 'button[aria-label="Search vertex by type and id"]'
    limit_number_input = 'input[placeholder="Enter a number"]'
    pick_vertices_btn = '//button[contains(., "Pick vertices")]'
    check_boxes = '.box-container mat-checkbox'

    hide_button = 'button[aria-label="Hide"]'

    expand_from_vertices_btn = 'button[aria-label="Expand from vertices"]'
    expand_btn = '//button[contains(., "Expand")]'

class GSTWriteQueriesLocators:
    """The locators on Write Queries page on Graph Studio"""

    # Duplicates: GSTHomeLocators.nav_write_queries
    nav_write_queries = "//p[text()='Write Queries']"
    header_name = (
        "//div[@class='panel-header']/div[normalize-space(text())='Graph queries']"
    )
    # query
    query_list_container = ".query-list-container"
    query_list_hint = f"{query_list_container} .query-list-hint"
    query_list_item = f"{query_list_container} #query-list-item"
    query_search_input = "input.input-box"
    installed_mark = "//mat-icon[text()='publish']"
    # install query
    download_query_btn = 'button[aria-label="Download query"]'
    install_all_queries_btn = "button[aria-label='Install all queries']"
    installed_query_icon = 'mat-icon[alt="Query installed"]'
    not_installed_query_icon = 'mat-icon[alt="Query not installed"]'
    install_current_query_btn = "button[aria-label='Install query']"
    install_btn_in_popup = "//button/span[normalize-space(text())='INSTALL']"
    cancel_btn_in_popup = "//button/span[normalize-space(text())='CANCEL']"
    # run query
    run_installed_query_btn = "button[aria-label='Run query']"
    run_interpreted_query_btn = "button[aria-label='Run query in interpreted mode']"
    run_query_btn = "button[type='submit']"
    # refresh query
    refresh_query_btn = "//mat-icon[text()='present_to_all']/../../../button/span/mat-icon[text()='refresh']"

    # parameters
    parameter_panel = ".query-param-panel"
    parameter_input = f"{parameter_panel} input"
    parameter_hint = f"{parameter_panel} .mat-hint-for-param"
    # result
    query_editor_expand_btn = ".horizontal-toolbar button[aria-label='Expand']"
    result_expand_btn = ".vertical-toolbar button[aria-label='Expand']"
    collapse_btn = "button[aria-label='Collapse']"
    view_logs_btn = "button[aria-label='View logs']"
    view_json_result_btn = "button[aria-label='View JSON result']"
    visualize_graph_result__btn = "button[aria-label='Visualize graph result']"
    view_schema_btn = "button[aria-label='View schema']"
    view_logs_btn = "button[aria-label='View logs']"
    log_container = ".log-container"


class GSTActionsLocators:
    """The locators on Actions page on Graph Studio"""

    nav_actions = "//p[text()='Actions']"
    actions_header = "//h1[text()='Actions']"
    clear_all_graph_data_btn = "#clear-graph-data-button"
    force_option = "label[for='force-rebuild-switch-input']"
    snapshot_option = "label[for='snapshot-rebuild-switch-input']"
    snapshot_only_option = "label[for='snapshotonly-rebuild-switch-input']"
    rebuild_btn = "#rebuild-graph-data-button"
    clear_all_graph_data_input = "//input[@placeholder='clear all graph data']"
    permanently_clear_all_graph_data_btn = (
        "//button/span[normalize-space(text())='PERMANENTLY CLEAR ALL GRAPH DATA']"
    )
    cancel_btn_in_popup = "//button/span[normalize-space(text())='CANCEL']"
    ok_btn_in_popup = "//button/span[normalize-space(text())='OK']"
    clear_process_dialog = "//mat-dialog-container[@id='app-global-progress']"


class SnackBarLocators:
    span = "//simple-snack-bar/span"
    dismiss_btn = "//simple-snack-bar//button"
