class GAPHomeLocators:
    # Dashboard
    dashboard = "//p[contains(.,'Dashboard')]"
    tabs = 'div[role="tab"]'
    dependency_view = "//button[contains(.,'Dependency view')]"
    table_view = "//button[contains(.,'Table view')]"
    start_all = "//span[contains(.,'Start all')]"
    restart_all = "//span[contains(.,'Restart all')]"
    confirm_button = "//button[contains(.,'Confirm')]"
    online_tag = 'span[title="Online"]'
    m1_tag = "//span[contains(.,'m1')]"
    confirm_mess = "//div[@data-baseweb='toaster']/div[@data-baseweb='toast']/div"
    # Monitor
    monitor = "//p[contains(.,'Monitor')]"
    logmanagement = "//p[contains(.,'Logs')]"
    management = "//p[contains(.,'Management')]"
    others = "//p[contains(.,'Others')]"
    gsqloutput = "//p[contains(.,'GSQL Output File')]"
    components = "//p[contains(.,'Components')]"
    gsqlconfig = "//p[contains(.,'GSQL')]"
    upgradeTGsuite = "//p[contains(.,'Upgrade TigerGraph Suite')]"
    wlm = "//p[contains(.,'Queries')]"
    users = "//p[contains(.,'Users')]"
    license = "//p[contains(.,'License')]"
    # Security menu
    security = "//p[contains(.,'Security')]"
    LDAP = "//p[contains(.,'LDAP')]"
    enable_button = "div.mat-slide-toggle-bar"
    checkbox_status = 'input[type="checkbox"]'
    select_button = 'span.mat-radio-inner-circle'
    disable_button = 'input[aria-checked="false"]'
    upload_file_button = 'button.file-button.mat-icon-button'
    self_signed_button = 'button.mat-menu-item.ng-star-inserted'
    self_signed_windows = "//span[contains(.,'Self-sign')]"
    self_signed_input = "input.mat-form-field-autofill-control.ng-pristine.ng-invalid.cdk-text-field-autofill-monitored"
    self_signed_apply = 'button[type="submit"]'
    ID_certificate = '#mat-input-5'
    input_parameter = 'input.mat-input-element'
    apply_button = 'button#apply'
    ok_button = "//span[contains(.,'OK')]"
    SSO = "//p[contains(.,'SSO')]"
    # set OKTA page
    okta_account = '#okta-signin-username'
    okta_psd = '#okta-signin-password'
    okta_submit = '#okta-signin-submit'
    okta_next = "//input[@value='Next']"
    okta_acs = "//input[@name='postBackURL']"
    okta_meta = "//input[@name='audienceRestriction']"
    okta_finish = "//input[@value='Finish']"
    # set AZURE page
    azure_account = 'input[type="email"]'
    azure_submit = 'input[type="submit"]'
    azure_psd = 'input[type="password"]'
    azure_back = "#idBtn_Back"
    edit_saml = 'div[aria-label="Edit basic SAML configuration"]'
    meta_cells = "div.fxc-gc-cell"
    meta_inputs = "input.azc-input.azc-formControl"
    azure_save_button = 'div[data-telemetryname="Command-Save"]'


    # components
    Nginx = "//p[contains(.,'Nginx')]"
    RESTPP = "//p[contains(.,'RESTPP')]"
    GSQL = "//p[contains(.,'GSQL')]"
    GPE = "//p[contains(.,'GPE')]"
    GUI = "//p[contains(.,'GUI')]"
    system = "//p[contains(.,'System')]"
    Kafka = "//p[contains(.,'Kafka')]"
    #gui
    input_widgets = "div.mat-form-field-infix > input"
    # nginx
    nginx_icon = ".notranslate.title-icon"



class GAPLogLocators:
    allnodecheckbox = "//span[text()='Nodes :']/../../../div[2]/mat-grid-list[1]/div/mat-grid-tile[1]/figure/mat-checkbox/label/span"
    m1node = "//span[text()='Nodes :']/../../../div[2]/mat-grid-list[1]/div/mat-grid-tile[2]/figure/mat-checkbox/label/span"
    logpattern = "//input[@aria-label='Search pattern']"
    m1folder = "//li[@id='single-host']/div/button/span/mat-icon"
    admincomponent = "//li[@id='single-component']/div/button/span/span"
    logfolderdrag = ".log-dir:nth-child(1)"
    logveiewerdrag = ".xterm-viewport"
    searchlistdrag = "//div/div[2]/div[3]"
    logveiewer = ".xterm-cursor-layer"
    adminlog = "//span[contains(.,'ADMIN.INFO')]"
    downloadlog = "//mat-icon[contains(.,'get_app')]"
    search = "//button[contains(.,'center_focus_weakSEARCH')]"
    allcomponent = "//span[text()='Components :']/../../../div[2]/mat-grid-list[1]/div/mat-grid-tile[1]/figure/mat-checkbox/label/span"
    componentGsql = "//span[contains(.,'GSQL')]"


class GAPOutputLocators:
    selectnode = "//span[contains(@class,'mat-select-min-line') and text()='All']"
    m1node = "//span[contains(@class,'mat-option-text') and text()=' m1 ']"
    filepath = "//input[@data-placeholder='File path']"
    preview = "//div[2]/div/button/span"
    download = "//span[contains(.,'file_download Download')]"


class GAPComponentsLocators:
    gsqlresponsesize = "//input[@data-placeholder='Maximum response size (Bytes)']"
    apply = "//span[contains(.,'APPLY')]"
    confirm = "//mat-dialog-actions/button[2]"
    Kafka_title = "//span[text()='Kafka']"


class GshellLocators:
    graph = "//div[@id='root']/div/div/div/div/div/div/div/div"
    mygraph = "//li[@id='bui18val-1']/div"
    # commandline = '.CodeMirror-line'
    commandline = "div:nth-child(1) > textarea"
    runcommand = ".dt:nth-child(1) > svg"


class WLMLocator:
    refresh = ".refresh-button .mat-icon"


class GAPUsersLocator:
    roletab = "//div[contains(text(),'User-defined Roles')]"
    usertab = "//div[contains(text(),'All Users')]"
    granttab = "//div[contains(text(),'Role Management')]"
    
    # user-defined role
    create_role_button = "//button/span[contains(text(),'Create role')]"
    rolename = "//input[@type='text']"
    role_type_global = "//input[@value='Global']"
    role_type_local = "//input[@value='Local']"
    select_a_graph = "//mat-select[@role='combobox']/div"
    rolesubmit = "//button[@type='submit']"
    yes = "//mat-dialog-actions/button[2]"
    GReadschema = "//span[contains(.,'READ_SCHEMA')]"
    GReadquery = "//span[contains(.,'READ_QUERY')]"
    GReaddata = "//label[contains(.,'READ_DATA')]"
    back = "//span[contains(.,'BACK')]"
    saveGrant = "//span[contains(.,'SAVE')]"
    create_button = "//span[contains(.,'CREATE')]"
    cancel_button = "//span[contains(.,'CANCEL')]"
    no_button = "//span[contains(.,'No')]"
    yes_button = "//span[contains(.,'Yes')]"
    ok_button = "//span[contains(.,'OK')]"
    span_text = "//span[contains(.,'{}')]"
    input_text = span_text + "/../span/input"
    read_data_text = "//tr[@id='{}']/td[2]/mat-checkbox/label/span"
    create_data_text = "//tr[@id='{}']/td[3]/mat-checkbox/label/span"
    update_data_text = "//tr[@id='{}']/td[4]/mat-checkbox/label/span"
    read_data_input = read_data_text + "/input"
    edit_rolename = "//span[contains(.,'{}')]/../../../td[3]/mat-cell/button[1]" 
    delete_rolename = "//span[contains(.,'{}')]/../../../td[3]/mat-cell/button[2]" 

    # add user
    adduser = "#add-user"
    add_group_button = "#add-group"
    proxy_group_button = "//p[contains(.,'Proxy Group')]"
    addrole = ".mat-flat-button .mat-icon"
    inputusername = "#username-input"
    inputpw = "#password-input"
    input_group_name = "#group-name-input"
    input_name_key = "#attr-name-input"
    input_name_value = "#attr-value-input"
    grant_group = 'mat-button-toggle[role="presentation"]'
    drop_list_buttons = "div.mat-select-arrow-wrapper"
    checkbox = 'span.mat-checkbox-inner-container'

    cofirmpw = "#confirm-password-input"
    usersubmit = ".mat-primary > .mat-button-wrapper > .ng-star-inserted"
    grantprompt = ".mat-simple-snackbar"
    userlabel = "span.mat-checkbox-label"
    tguser = "#mat-checkbox-4 .mat-checkbox-label"
    selectgraph = '//*[@id="mat-select-3"]'
    MyGraph = "//span[contains(.,'MyGraph')]"
    selectrole = '//*[@id="mat-select-4"]'
    RAdmin = "//span[contains(.,'admin')]"
    switchuser = "//button[@id='mat-button-toggle-4-button']/div"


class GAPProfileLocator:
    changepw = ".mat-subheading-1 .mat-icon"
    dialogpw = "//span[text()='New password']/../../../input"
    confirmpw = "//span[text()='Confirm password']/../../../input"
    change = "//span[contains(.,'CHANGE')]"
    headshort = "/html/body/app-root/app-page-layout/div/tools-head//div/div[3]/div[1]"
    logout = "/html/body/app-root/app-page-layout/div/tools-head//div/div[3]/div[2]/div/div[2]/div/span"
    loginname = "//input[@name='']"
    loginpw = "(//input[@name=''])[2]"
    login = "//button[@type='submit']"


class GAPLicenseLocator:
    selectfile = "//span[contains(.,'SELECT FILE')]"
    filepath = "//input[@type='file']"
    update = "//span[contains(.,'UPDATE')]"
    ok = "//span[contains(.,'OK')]"
    
class GAPSecretLocator:
    newsecret = "//input[@id='new-secret-alias']"
    addbtn = "//button[@id='add-secret']/span/mat-icon"
    secretvalue = "//td[@id='secret-value']"
    delsecret = "//button[@id='delete-secret']"
    ok = "//span[contains(.,'OK')]"    
    testsecret = "//td[contains(.,'test_secret')]"
    items_per_page = "div.mat-select-arrow-wrapper"
    items_per_page_20 = "//span[contains(.,'20')]"

class GAPCDCLocator:
    enablebtn = "//span[contains(text(),'Enable change data capture')]/../div"
    enableinput = "//input"
    uploadicon = "//mat-icon[contains(.,'cloud_upload')]"
    uploadbtn = "//button[contains(.,'Upload file')]"
    filepath = "//input[@type='file']"
    second_input_file = 'input[data-testid="System.CDC.ProducerConfig"]'
    apply = "//span[contains(.,'APPLY')]"
    ok = "//span[contains(.,'OK')]"

class BackupRestoreLocators:
    nav_backup_restore = "//p[text()='Backup & Restore']"
    # sections
    scheduled_backup_section = "//section[@aria-label='Schedule backup']"
    backup_status_section = "//section[@aria-label='Scheduled backup running status']"
    backup_restore_section = "//section[@aria-label='Back up and restore']"
    manage_backups_section = "//section[@aria-label='Manage backups']"
    # scheduled backup
    scheduled_backup_tag = "//span[normalize-space(text())='Scheduled Backup']"
    # backup_restore
    backup_input = "//input[@data-placeholder='Backup tag']"
    backup_btn = "//button[@aria-label='Run backup']"
    restore_btn = "//button[@aria-label='Restore backup']"
    backup_restore_log = f"{backup_restore_section}//pre"
    # manage backup
    this_cluster_label = "//div[text()='This instance/cluster']"
    other_cluster_label = "//div[text()='Other instances/clusters']"
    active_tab = "//mat-tab-body[contains(@class,'mat-tab-body-active')]"
    remove_backup_log = f"{manage_backups_section}//pre"


class PopupLocators:
    popup = ".cdk-overlay-pane"
    head = f"{popup} h1"
    failed_head="//div[@class='cdk-overlay-pane']//h1[starts-with(text(),'Failed')]"
    content = f"{popup} mat-dialog-content p"
    cancel_btn = "//span[normalize-space(text())='CANCEL']"
    ok_btn = "//span[normalize-space(text())='OK']"

class SnackBarLocators:
    span = "//simple-snack-bar/span"
    dismiss_btn = "//simple-snack-bar//button"

class RestPPLocators:
    nav_restpp="//p[text()='RESTPP']"
    header="//span[text()='RESTPP']"
    default_query_timeout="//input[@placeholder='Default query timeout (seconds)']"
    max_concurrent_heavy_queries="//input[@placeholder='Maximum concurrent running \"heavy\" built-in queries']"
    max_concurrent_queries="//input[@placeholder='Maximum concurrent running queries']"

class UpgradeToolsLocators:
    upgrade_tools_span = "//span[contains(.,'Upgrade TigerGraph Suite')]"
    manual_upgrade_input = "//button[contains(.,'Manual Upgrade')]/preceding-sibling::input"
    online_upgrade_btn = "//span[contains(.,'Online Check for Upgrade')]"
    checking_available_version_p = "//p[contains(.,'Checking for the available upgrades for TigerGraph Suite')]"
    latest_version_p = "//p[contains(.,'You are currently using the most recent version of TigerGraph Suite.')]"
    cancel_btn = "//button[contains(.,'CANCEL')]"
    uploading_package_p = "//p[contains(.,'Uploading TigerGraph Suite Package')]"
    current_ver_p = "//p[contains(.,'Current Version')]"
    old_ver_p = "//p[contains(.,'Old Version')]"
    downgrade_btn = "//button[contains(.,'Downgrade')]"

    # invalid process
    invalid_package_error_p = "//p[contains(.,'Please upload a valid package')]"
    sign_missing_error_p = "//p[contains(.,'tools-all.tar.gz.minisig: no such file or directory.')]"
    close_error_window = "//button[contains(.,'OK')]"

    