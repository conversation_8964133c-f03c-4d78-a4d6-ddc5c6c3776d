import pytest
import allure
import os
import time
import shutil
from collections import OrderedDict
from pages.cloud.clusters.access_cluster_tools_page import ClusterToolsPage
from base.tools_basecase import ToolsBaseCase
from pages.tools.gshell.gshell_page import GshellPage
from utils.data_util.gshell_resolver import GshellTestDataResolver
from pages.tools.gshell.gshell_switchpage import GshellSwitchPage
from pages.tools.graphstudio import GSTHomePage
from utils.data_util.login import LoginUtils


class TestAttributes(ToolsBaseCase):
    gshell_test_data = GshellTestDataResolver("gshell_test_data.json").gshell_test_data


    @allure.title("Gshell Attributes uploadGSTFile - smoke test")
    @allure.description("Check gshell attributes uploadGSTFile")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.offline
    @pytest.mark.gshellAttributesUploadGSTFile
    @pytest.mark.preexec
    @pytest.mark.run(order=110)  # affect test_attributes case, so run in advance
    def test_attributes_uploadGSTFile(self):
        #define tescase data
        upload_to_GST_path = self.gshell_test_data['gshell_paths']['upload_toGST_path']

        #upload the gshell to GST for perpare
        gst = GSTHomePage(self)
        gst.GST_login()
        gshellSwitchPage = GshellSwitchPage(self)
        gshellSwitchPage.inGST_uploadLocalFiles(filePath=upload_to_GST_path)

    @allure.title("Gshell multiple create/drop vertex(Graph_test_udt.csv) operation - smoke test")
    @allure.description("Check gshell multiple create/drop vertex(Graph_test_udt.csv) operation ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.gshellAttributes
    @pytest.mark.lock_catalog
    @pytest.mark.preexec  # has schema change, will affect run query
    @pytest.mark.onprem
    @pytest.mark.run(order=198)  # multiple create/drop vertex(Graph_test_udt.csv) operation, so last to run
    @pytest.mark.offline
    # the file path on k8s changed "/home/<USER>/tigergraph/data/gui/loading_data/"
    def test_gshell_create_drop_vertex_with_files(self):
        #define tescase data
        testcase_attributes = OrderedDict(self.gshell_test_data.get("testcase_attributes"))
        
        #execute tescase
        gshellPage = GshellPage(self)
        gshellPage.login_gshell()
        gshellPage.execute_OrderedDictQueries(testcase_attributes)

    @allure.title("Gshell create query and check table view on GST - smoke test")
    @allure.description("Gshell create query and check table view on GST ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.gshellAttributes
    @pytest.mark.lock_catalog
    @pytest.mark.preexec
    @pytest.mark.run(order=50)
    @pytest.mark.offline
    def test_create_query_and_check_table_view_on_GST(self):
        #define tescase data
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_create_queries_check_on_GST"))

        #execute tescase
        gshellPage = GshellPage(self)
        gshellPage.login_gshell()
        gshellPage.execute_OrderedDictQueries(testcase_queries)

        # check result on GST write queries
        gst = GSTHomePage(self)
        loginUtil = LoginUtils(self)
        # user only need switch tools if it's cloud cluster
        if loginUtil.is_oncloud():
            loginUtil.switch_to_other_tools()
        else:
            gst.GST_login()
        gst.run_queries_and_check_table_results(testcase_queries)
