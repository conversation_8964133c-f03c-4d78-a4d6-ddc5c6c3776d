import logging
import unittest
import pytest
import allure
import os
import time
import shutil
from collections import OrderedDict
import logging

from base.tools_basecase import ToolsBaseCase
from pages.tools.gshell.gshell_page import GshellPage
from pages.tools.graphstudio.graph_studio_home_page import GSTHomePage
from utils.data_util.gshell_resolver import GshellTestDataResolver
LOGGER = logging.getLogger(__name__)


class TestQueries(ToolsBaseCase):
    gshell_test_data = GshellTestDataResolver("gshell_test_data.json").gshell_test_data


    @allure.title("Gshell Queries Options - smoke test")
    @allure.description("Check gshell queries ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.preexec  # create & install & run query, so move preexec
    @pytest.mark.gshell_upload
    @pytest.mark.lock_catalog
    @pytest.mark.opencypher
    @pytest.mark.run(order=96)
    def test_queries(self):
        #define tescase data
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_queries"))

        #execute tescase
        gshellPage = GshellPage(self)
        gstPage = GSTHomePage(self)
        gshellPage.login_gshell()  
        gshellPage.execute_OrderedDictQueries(testcase_queries, gstPage.is_cluster_available(393))

        
    @allure.title("Gshell Download CSVfile  - smoke test")
    @allure.description("Download CSVfile ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gshell
    @pytest.mark.gshellDownloadCSV
    @pytest.mark.opencypher_cloud
    @pytest.mark.lock_catalog
    @pytest.mark.preexec  # create & install & run query, so move preexec
    @pytest.mark.run(order=57)
    def test_downloadJson(self):
        #define tescase data
        testcase_queries = OrderedDict(self.gshell_test_data.get("testcase_queries"))
        specifyDownPath = "testcase_downloadCSV-" + time.strftime('%Y-%m-%d', time.localtime())
        specifyDownPath = os.path.join(self.gshell_test_data["gshell_paths"]["download_data_path"], specifyDownPath)
        #execute tescase
        gshellPage = GshellPage(self)
        gstPage = GSTHomePage(self)
        path = gshellPage.setChromeDownloadPath(specifyDownPath)
        gshellPage.login_gshell()
        for _, queryInfo in testcase_queries.items():
            #define different setting according to queryOption
            isJsonType = True if 'jsonOutput' in queryInfo.keys() else False
            isSkip = True if not gstPage.is_cluster_available(393) and 'opencypher' in queryInfo["gsqlfile"] else False

            if not isSkip:
                #run gsqlfile and verify results according to queryInfo
                gshellPage.run_gsqlfile_verifyText(queryInfo["gsqlfile"], queryInfo["output"], queryInfo["assertTimeout"], isJsonType)
                LOGGER.info("run gsql file done ----------- " + path)
                if isJsonType:
                    specifyFilename = os.path.split(queryInfo["gsqlfile"])[-1].split('.')[0] + '-download.csv'
                    gshellPage.download_lastJsonFile(path, specifyFilename)
            else:
                LOGGER.info("gstPage.is_cluster_available(393) and 'opencypher', so skip " + path)
        #remove downloadfile to void data accumulation
        shutil.rmtree(path)