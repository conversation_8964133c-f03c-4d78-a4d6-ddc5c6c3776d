import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase

class TestLogManagement(ToolsBaseCase):

    @allure.title("GAP GSQL Output file preview and download - smoke test")
    @allure.description("GSQL Output file preview and download")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gapoutput
    @pytest.mark.gshell_upload
    @pytest.mark.run(order=1000)
    @pytest.mark.offline
    # the file path on k8s changed "/home/<USER>/tigergraph/data/gui/loading_data/"
    def test_GSQL_output_file_preview(self):
        super().setUp()
        gap = GAPPage()
        gap.gsql_output(self)




