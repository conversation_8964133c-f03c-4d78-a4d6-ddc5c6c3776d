import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
from locators.tools.admin_portal_locators import GAPProfileLocator, GAPUsersLocator
import logging
from utils.data_util.login import LoginUtils
from locators.tools.graph_studio_locators import GSTHomeLocators
from locators.tools.admin_portal_locators import GAPHomeLocators

LOGGER = logging.getLogger(__name__)

class TestMyProfile(ToolsBaseCase):

    @allure.title("GAP MyProfile password change- smoke test")
    @allure.description("MyProfile password change")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.gapchangepw
    @pytest.mark.preexec
    @pytest.mark.offline  # failed in wip/mit job but succeed in dailyrun, so disable in tools_e2e
    @pytest.mark.run(order=1)
    def test_profile_password(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        username = "u1"
        oldpw = "userpw"
        newpw = "changepw"
        gap.createUser(self, username, oldpw)

        # login by new user
        self.execute_script(GSTHomeLocators.tools_header_icon_JS)
        self.sleep(2)
        self.execute_script(GSTHomeLocators.logout_icon_JS)
        self.sleep(2)
        self.wait_for_ready_state_complete()

        self.type(GAPProfileLocator.loginname, username)
        self.type(GAPProfileLocator.loginpw, oldpw)
        self.click(GAPProfileLocator.login)
        self.wait_for_ready_state_complete()

        # change password
        gap.intoUserManegement(self)
        LoginUtils(self).close_security_float_windows()
        self.attach_allure_screenshot("u1 login result screenshot")
        gap.changeMypw(self, newpw)

        # relogin with new password
        self.execute_script(GSTHomeLocators.tools_header_icon_JS)
        self.sleep(2)
        self.execute_script(GSTHomeLocators.logout_icon_JS)
        self.sleep(2)
        self.wait_for_ready_state_complete()

        self.type(GAPProfileLocator.loginname, username)
        self.type(GAPProfileLocator.loginpw, newpw)
        self.click(GAPProfileLocator.login)
        self.wait_for_ready_state_complete()

        # check login success with new password
        self.assert_element_visible(GAPHomeLocators.management, timeout=30)
        self.attach_allure_screenshot("change password screenshot")

    def tearDown(self):
        # login by tigergraph and remove the created user
        gap = GAPPage()
        gap.GapLogin(self)
        self.wait_for_ready_state_complete()
        self.sleep(3)
        LoginUtils(self).close_security_float_windows()
        gap.intoUserManegement(self)
        self.find_text("Secret Alias", timeout=30)
        # self.refresh_page()
        self.click(GAPUsersLocator.usertab)
        self.wait_for_ready_state_complete()
        self.wait_for_element_clickable(GAPUsersLocator.adduser)
        # if user is exists, will remove first
        userlist = self.find_elements("//tr")  # get row of table
        for u in range(len(userlist)):
            # must get element list again, or report error
            userlist = self.find_elements("//tr")
            if "u1" in userlist[u].text:
                LOGGER.info("user u1 exits, will remove the user")
                self.click("(//button[@id='delete-user']/span/mat-icon)[{}]".format(u))
                self.click("//button[contains(.,'OK')]")
                break

        self.attach_allure_screenshot("remove u1 user done screenshot")
        super().tearDown()



