import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
from locators.tools.admin_portal_locators import GAPUsersLocator


class TestRoleManagement(ToolsBaseCase):

    @allure.title("Create user-defined global role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the user-defined global role can be created via admin portal \n"
        "Description: Create user-defined global role in admin portal \n"
        "TestDesigner: <PERSON><PERSON> \n"
        "Date: 2023-08-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.basecases
    @pytest.mark.run(order=0.31)
    def test_add_globalrole(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.createRole(self, "g_role_default", True)

    @allure.title("Create user-defined local role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the user-defined local role can be created via admin portal \n"
        "Description: Create user-defined local role in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510 \n"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.basecases
    @pytest.mark.run(order=0.32)
    def test_add_localrole(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.createRole(self, "l_role_default", False, "MyGraph")

    @allure.title("Delete user-defined global role")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the user-defined global role can be delete via admin portal \n"
        "Description: Delete user-defined global role in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.run(order=10)
    def test_delete_globalrole(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.createRole(self, "g_role_delete", True)
        gap.deleteRole(self, "g_role_delete")

    @allure.title("Delete user-defined local role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the user-defined local role can be delete via admin portal \n"
        "Description: Delete user-defined local role in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.run(order=11)
    def test_delete_localrole(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.createRole(self, "l_role_delete", False, "MyGraph")
        gap.deleteRole(self, "l_role_delete")

    @allure.title("Grant privilege to user-defined global role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the privilege can be grant to user-defined global role via admin portal \n"
        "Description: Grant privilege to user-defined global role in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.basecases
    @pytest.mark.UDFrole
    @pytest.mark.run(order=41)
    def test_grant_privilege_to_globalrole(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.grantGlobalPrivilege(self, "g_role_default", ["READ_SCHEMA","READ_QUERY","READ_DATA"])

    @allure.title("DeGrant privilege to user-defined local role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the user-defined local role can be grant via admin portal \n"
        "Description: Grant privilege to user-defined local role in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.basecases
    @pytest.mark.UDFrole
    @pytest.mark.UDFrole3
    @pytest.mark.order(after="test_grant_privilege_to_globalrole")
    @pytest.mark.run(order=42)
    def test_grant_privilege_to_localrole(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        gap.grantLocalPrivilegeWithReadData(self, "l_role_default", ["READ_SCHEMA","READ_QUERY"],
                ["Patient-patient_id", "Patient-global_num","Patient-infection_case"])

    @allure.title("Grant user-defined global role to user")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check grant user-defined global role to user \n"
        "Description: Grant user-defined global role to user in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.basecases
    @pytest.mark.UDFrole
    @pytest.mark.run(order=5009)
    def test_grant_globalrole_user(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        # Grant role
        status, index = gap.grantRole(self, "u_globalUDF", "g_role_default", True, "MyGraph")
        if status:
            # Wait save finish  
            self.assert_text("Successfully granted g_role_default on global to users u_globalUDF", timeout=20)
            self.wait_for_element_not_visible(GAPUsersLocator.grantprompt)
            self.attach_allure_screenshot("grant_globalrole_user")
            el = self.find_element(GAPUsersLocator.saveGrant)
            self.assert_equal("false", el.get_attribute("draggable"), "Check save button status")

    @allure.title("Grant user-defined local role to user")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check grant user-defined local role to user \n"
        "Description: Grant user-defined local role to user in admin portal \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-25 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.basecases
    @pytest.mark.UDFrole
    @pytest.mark.order(after="test_grant_globalrole_user")
    @pytest.mark.run(order=5101)
    def test_grant_localrole_user(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.intoUserManegement(self)
        # Grant role
        status, index = gap.grantRole(self, "u_localUDF", "l_role_default", False, "MyGraph")
        if status:
            # Wait save finish
            self.assert_text("Successfully granted l_role_default on graph MyGraph to users u_localUDF", timeout=20)
            self.wait_for_element_not_visible(GAPUsersLocator.grantprompt)
            self.attach_allure_screenshot("grant_localrole_user")
            el = self.find_element(GAPUsersLocator.saveGrant)
            self.assert_equal("false", el.get_attribute("draggable"), "Check save button status")