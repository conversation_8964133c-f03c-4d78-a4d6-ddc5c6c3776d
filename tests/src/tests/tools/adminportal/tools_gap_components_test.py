import pytest
import allure
from pages.tools.adminportal.adminportal_page import GAPPage
from base.tools_basecase import ToolsBaseCase
import logging

LOGGER = logging.getLogger(__name__)


class TestGapSecurity(ToolsBaseCase):


    @allure.title("GAP components RESTPP config - smoke test")
    @allure.description("GAP components RESTPP config")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.preexec
    @pytest.mark.onprem_benchmark_setup
    @pytest.mark.run(order=105)
    def test_components_RESTPP_config(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.RESTPP_config(self)

    @allure.title("GAP components RESTPP config set DefaultQueryTimeoutSec 1000 - smoke test")
    @allure.description("GAP components RESTPP config set DefaultQueryTimeoutSec 1000")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.teardown
    @pytest.mark.run(order=105)
    def test_components_RESTPP_set_DefaultQueryTimeoutSec_1000_config(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.RESTPP_config(self, restore_flag=True)

    @allure.title("GAP enable SSL config - smoke test")
    @allure.description("GAP  enable SSL config")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.preexec
    @pytest.mark.enable_SSL
    @pytest.mark.https
    @pytest.mark.run(order=0)
    def test_components_enable_SSL_config(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.SSL_config(self)


    @allure.title("GAP disable SSL config - smoke test")
    @allure.description("GAP disable SSL config")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.teardown
    @pytest.mark.disable_SSL
    @pytest.mark.https
    @pytest.mark.run(order=99999)  # make sure the last to run
    def test_components_disable_SSL_config(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.disable_SSL_config(self)



    @allure.title("GAP components Kafka config - smoke test")
    @allure.description("GAP components Kafka config")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.preexec
    @pytest.mark.onprem_benchmark_setup
    @pytest.mark.run(order=106)
    def test_components_Kafka_config(self):
        super().setUp()
        gap = GAPPage()
        gap.GapLogin(self)
        gap.Kafka_config(self)