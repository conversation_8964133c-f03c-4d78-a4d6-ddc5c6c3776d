import time
import pytest
import allure

from pages.tools.graphstudio import GSTHomePage
from base.tools_basecase import ToolsBaseCase
from actions.tools.graph_studio.write_queries_action import WriteQueriesAction
from parameterized import parameterized
from utils.data_util.data_resolver import read_test_data


class TestGST_WriteQueries(ToolsBaseCase):

    read_data = read_test_data(file="tools_test_data.json")
    login_role_test_data = read_data.get("login_onprem")
    test_data = []
    test_data_without_local_role = []
    for i in login_role_test_data:
        if i.get("type") != "local_UDF_role":
            test_data_without_local_role.append((i.get("type")))
        test_data.append((i.get("type") ))

    @allure.title("discard draft  - smoke test")
    @allure.description("discard draft in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query
    @pytest.mark.run(order=6009)
    def test_discard_draft_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.discardDraft()



    @allure.title("delete query and draft  - smoke test")
    @allure.description("delete query and draft in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query
    @pytest.mark.run(order=1034)
    def test_delete_query_and_draft_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.deleteQueryAndDraft()


    @allure.title("download current query  - smoke test")
    @allure.description("download current query in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query
    @pytest.mark.run(order=6002)
    def test_download_current_query_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.downloadCurrentQuery()

    @allure.title("download all queries  - smoke test")
    @allure.description("download  all queries in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query
    @pytest.mark.run(order=6034)  # mix into insights
    def test_download_all_queries_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.downloadAllQueries()

    @allure.title("install all queries  - smoke test")
    @allure.description("install all queries in GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query_install
    @pytest.mark.basecases
    @pytest.mark.run(order=15.03)  # seperate with GAP wlm case
    def test_install_all_queries_GST(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.install_all_queries()

    @allure.title("run all queries  - smoke test")
    @allure.description("run all queries in GST")
    @pytest.mark.high
    @pytest.mark.tools
    @pytest.mark.accessGST
    @pytest.mark.query_run
    @pytest.mark.release
    @pytest.mark.run(order=12)  # run in the front, it took long times about 8 mins
    def test_run_all_queries_GST(self):
        starterkitsConfigFile = "./config/starter_kit/starter_kits.json"
        solution = "covid-19"
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login()
        writeQueriesAction.run_query_with_parameter_file(starterkitsConfigFile, solution)

    """
        disable the case because dev engineer refactor the OpenCypher,
        will enable and update the cases after OpenCypher mit
    """
    # @allure.title("Run opencypher queries  - smoke test")
    # @allure.description(
    #     "TestType: Positive"
    #     "Target: Check the opencyper query can be created/run in GST"
    #     "Description: Write opencyper query and Run queries in interpret mode"
    #     "TestDesigner: Jing Yang"
    #     "Date: 2023-08-29"
    #     "Link: https://graphsql.atlassian.net/browse/QA-5454")
    # @pytest.mark.high
    # @pytest.mark.smoke
    # @pytest.mark.tools
    # @pytest.mark.query_run
    # @pytest.mark.opencypher
    # @pytest.mark.run(order=12)
    # def test_run_opencypher_queries_interpret_mode(self):
    #     super().setUp()
    #     # self.execute_script("window.localStorage.clear();")
    #     gst = GSTHomePage(self)
    #     gst.GST_login()
    #     if gst.is_cluster_available(393):
    #         gst.run_OpenCypher_query_interpret_mode()

    # @allure.title("Run opencypher querie  - smoke test")
    # @allure.description(
    #     "TestType: Positive"
    #     "Target: Check the opencyper query can be created/installed/run in GST"
    #     "Description: Write opencyper query and Run queries in installed mode"
    #     "TestDesigner: Jing Yang"
    #     "Date: 2023-08-29"
    #     "Link: https://graphsql.atlassian.net/browse/QA-5454")
    # @pytest.mark.high
    # @pytest.mark.smoke
    # @pytest.mark.tools
    # @pytest.mark.query_run
    # @pytest.mark.opencypher
    # @pytest.mark.run(order=1200)
    # def test_run_opencypher_queries_installed_mode(self):
    #     super().setUp()
    #     # self.execute_script("window.localStorage.clear();")
    #     gst = GSTHomePage(self)
    #     gst.GST_login()
    #     if gst.is_cluster_available(393):
    #         gst.run_OpenCypher_query_interpret_mode()

    @allure.title("run query with related privileges under different UDF role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the query can be retrive the data with related privileges under different UDF role \n"
        "Description: Run query with related privileges under different UDF role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-29 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @parameterized.expand(test_data, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=21)
    def test_run_query_with_differentrole_success(self, type):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way=type)
        writeQueriesAction.run_query_with_name(query_name="with_read_data_query", assert_text="\"infection_case\": \"overseas inflow\"")

    @allure.title("run query with related privileges for some attributes under different role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the query can be retrive the data with related privileges for some attributes under different role \n"
        "Description: Run query with related privileges for some attributes under different role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @parameterized.expand(test_data_without_local_role, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=22)
    def test_run_query_for_some_attributes_with_differentrole_success(self, type):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way=type)
        writeQueriesAction.run_query_with_name(query_name="without_read_data_query", assert_text="\"infection_case\": \"overseas inflow\"")

    @allure.title("run query with related privileges for whole vertex under different role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the query can be retrive the data with related privileges for whole vertex under different role \n"
        "Description: Run query with related privileges for whole vertex under different role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @parameterized.expand(test_data_without_local_role, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=23)
    def test_run_query_for_whole_vertex_with_differentrole_success(self, type):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way=type)
        writeQueriesAction.run_query_with_name(query_name="read_whole_vertex_data_query", assert_text="\"infection_case\":")

    @allure.title("run query with related privileges under global UDF role")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the query can be retrive the data with related privileges under global UDF role \n"
        "Description: Run query with related privileges under global UDF role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-08-29 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=24)
    def test_run_query_with_globalrole_success(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way="global_UDF_role")
        writeQueriesAction.run_query_with_name(query_name="with_read_data_query", assert_text="infection_case")

    @allure.title("run query without related privileges for some attributes under local UDF role")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the query can be retrive the data with related privileges for some attributes under local UDF role \n"
        "Description: Run query with related privileges for some attributes under local UDF role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=25)
    def test_run_query_for_some_attributes_with_localrole_failed(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way="local_UDF_role")
        writeQueriesAction.run_query_with_name(query_name="without_read_data_query", assert_text="\"infection_case\": \"overseas inflow\"")

    @allure.title("run query with related privileges for whole vertex under local UDF role")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the query can be retrive the data with related privileges for whole vertex under local UDF role \n"
        "Description: Run query with related privileges for whole vertex under local UDF role \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-01 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2510"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.onprem
    @pytest.mark.UDFrole
    @pytest.mark.query_run
    @pytest.mark.run(order=26)
    def test_run_query_for_whole_vertex_with_localrole_failed(self):
        super().setUp()
        writeQueriesAction = WriteQueriesAction(self)
        gst = GSTHomePage(self)
        gst.GST_login(login_way="local_UDF_role")
        writeQueriesAction.run_query_with_name(query_name="read_whole_vertex_data_query", assert_text="\"infection_case\":")
