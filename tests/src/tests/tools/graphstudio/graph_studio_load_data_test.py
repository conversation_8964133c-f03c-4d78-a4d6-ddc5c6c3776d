import pytest
import allure

from pages.tools.graphstudio import GSTHomePage
from base.tools_basecase import ToolsBaseCase


class TestGST_LoadData(ToolsBaseCase):

    @allure.title("load data GST  - smoke test")
    @allure.description("load local file GST")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.gapnew
    @pytest.mark.preexec
    @pytest.mark.run(order=0.3)
    def test_load_data_GST(self):
        super().setUp()
        # self.execute_script("window.localStorage.clear();")
        gst = GSTHomePage(self)
        gst.GST_login()
        gst.load_data()