import pytest
import allure

from pages.tools.insights.insights_application_page import InsightsAppPage
from base.tools_basecase import ToolsBaseCase
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from locators.tools.insights_locators import InsightLocators


class TestInsightsGraphWidget(ToolsBaseCase):

    application_name = "insights_e2e_app_test_01"
    application_access_token = "insights_e2e_app_test_access_token"
    page_name = "insights_e2e_page_test_01"
    graph_widget_name = "insights_e2e_graph_widget_test_01"
    graph_widget_name_update = "insights_e2e_graph_widget_test_update"

    @allure.title("Insights Create/Edit Page and widget - smoke test")
    @allure.description("Check the page can be created")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.debug0803
    # @pytest.mark.basecases
    @pytest.mark.run(order=3)
    def test_insight_widget_create(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.create_application(self.application_name)

        #create page under application
        apppage.create_page(self.page_name)
        apppage.add_widget_with_type(apppage.widget_type["graph"], self.graph_widget_name)

        #use default graph to show schema
        apppage.add_show_schema()
        self.attach_allure_screenshot("show graph widget")
        apppage.assert_text_exist("Widget type")

        #apply and save
        apppage.apply_save_widget()
        apppage.click_widget_configuration_with_name(self.graph_widget_name)
        self.click(InsightLocators.config_edit)
        self.attach_allure_screenshot("open graph widget")
        apppage.assert_text_exist("Widget type")
        apppage.update_widget_name(self.graph_widget_name_update)

        #query graph data
        self.click(InsightLocators.pattern_input)
        self.send_keys(InsightLocators.pattern_input, Keys.BACK_SPACE)
        #self.click(InsightLocators.pattern_input)
        apppage.add_schema_with_ID("2")
        apppage.add_vertex_limit(10)
        apppage.run_pattern()
        self.attach_allure_screenshot("run pattern result")
        self.assert_text(apppage.run_pattern_confirm, timeout=20)
        # self.assertTrue((apppage.run_pattern_confirm in apppage.getText_if_element_present(
        #     InsightLocators.confirm_mess)) or (apppage.no_data_confirm in apppage.getText_if_element_present(
        #     InsightLocators.confirm_mess)))

        #apply and save
        apppage.apply_save_widget()

        #share the widget
        apppage.share_widget(app_name=self.application_name, widget_name=self.graph_widget_name_update)

        #duplicate the widget
        apppage.duplicate_widget(self.graph_widget_name_update)

        #delete the widget
        apppage.delete_widget(self.graph_widget_name_update)

    @allure.title("Insights Create Default Graph Widget - smoke test")
    @allure.description("Check create default graph widget")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.preexec
    @pytest.mark.run(order=0.6)
    def test_insight_create_default_graph_widget(self):
        apppage = InsightsAppPage(self)
        apppage.create_default_application_with_graph()

    @allure.title("Insights generate access token in default app - smoke test")
    @allure.description("Insights generate access token in default app ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.onprem  # todo wait fix on TG cloud
    @pytest.mark.run(order=6)
    def test_insight_generate_access_token_in_default_app(self):
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.create_application(self.application_access_token)
        apppage.create_page(self.page_name)
        apppage.click_home_button_on_page()
        apppage.generate_access_token_in_default_app(app_name=self.application_access_token)