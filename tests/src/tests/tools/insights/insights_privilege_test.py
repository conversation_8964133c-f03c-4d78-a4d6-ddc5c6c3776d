import pytest
import allure

from pages.tools.insights.insights_application_page import InsightsAppPage
from base.tools_basecase import ToolsBaseCase
from locators.tools.insights_locators import InsightLocators


class TestInsightsPrivilege(ToolsBaseCase):

    application_name = "insights_e2e_nonsuper_test"
    application_origin_name = "insights_e2e_non_super_origin"
    application_update_name = "insights_e2e_non_super_test_update"
    application_name_widget = "insights_e2e_nonsuper_for_widget_test"
    application_name_widget_setting = "insights_e2e_nonsuper_for_widget_setting_test"
    page_name = "insights_e2e_page_test"
    graph_widget_name = "insights_e2e_graph_widget_test"

    @allure.title("Insights Create Application with non-super user - smoke test")
    @allure.description("Check the application can be created with non-super user")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.onprem
    # @pytest.mark.basecases
    @pytest.mark.run(order=10)
    def test_insight_app_create_with_nonsuper(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login(ToolsBaseCase.G_Designer_Login)
        apppage.delete_all_old_apps()
        apppage.create_application(self.application_name)
        self.click(InsightLocators.homepage)
        self.attach_allure_screenshot("go to application homepage")

    @allure.title("Insights Update Application with non-super user - smoke test")
    @allure.description("Check operation(edit/delete/duplicate) of the application")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.onprem
    @pytest.mark.run(order=6025)
    def test_insight_app_update_with_nonsuper(self):
        apppage = InsightsAppPage(self)
        apppage.insight_login(ToolsBaseCase.G_Designer_Login)

        apppage.create_application(self.application_origin_name)
        apppage.click_home_button_on_page()
        # update the application name
        apppage.change_application_name(self.application_origin_name, self.application_update_name)
        self.sleep(8)

        # duplicate the application
        apppage.duplicate_application(self.application_update_name)
        self.sleep(5)

        # search application
        apppage.search_application(self.application_update_name)

        # delete the application
        apppage.delete_application(self.application_update_name)

    @allure.title("Insights widget Page with nonsuper user - smoke test")
    @allure.description("Check the page can be created with nonsuper user")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.onprem
    # @pytest.mark.basecases
    @pytest.mark.debug502
    @pytest.mark.refer_next_case
    @pytest.mark.run(order=6000)
    def test_insight_widget_create_with_nonsuper(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login(ToolsBaseCase.G_Designer_Login)
        apppage.create_application(self.application_name_widget)

        #create page under application
        apppage.create_page(self.page_name)
        apppage.click_page_with_name(self.page_name)
        apppage.add_widget_with_type(apppage.widget_type["graph"], self.graph_widget_name)

        #use default graph to show schema
        apppage.add_show_schema()
        self.attach_allure_screenshot("show graph widget")

        #apply and save
        apppage.apply_save_widget()

    @allure.title("Insights Widget Operation with nonsuper user - smoke test")
    @allure.description("Check share/duplcate/delete the widget with nonsuper user")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.onprem
    @pytest.mark.run(order=6211)
    def test_insight_widget_setting_with_nonsuper(self):
        apppage = InsightsAppPage(self)
        apppage.insight_login(ToolsBaseCase.G_Designer_Login)
        # create own app, seperator from previous case
        apppage.create_application(self.application_name_widget_setting)

        # create page under application
        apppage.create_page(self.page_name)
        apppage.click_page_with_name(self.page_name)
        apppage.add_widget_with_type(apppage.widget_type["graph"], self.graph_widget_name)

        # use default graph to show schema
        apppage.add_show_schema()
        self.attach_allure_screenshot("show graph widget")

        # apply and save
        apppage.apply_save_widget()

        apppage.click_page_with_name(self.page_name)
        self.attach_allure_screenshot("open page screenshot")

        #share the widget
        apppage.share_widget(app_name=self.application_name_widget_setting, widget_name=self.graph_widget_name)
        self.attach_allure_screenshot("share widget screenshot")
        #duplicate the widget
        apppage.duplicate_widget(self.graph_widget_name)

        #delete the widget
        apppage.delete_widget(self.graph_widget_name)
        self.attach_allure_screenshot("delete widget screenshot")


    @allure.title("Insights access other's application - smoke test")
    @allure.description("Check access other's application with nonsuper user")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.onprem
    @pytest.mark.run(order=6012)
    def test_insight_access_others_app_with_nonsuper(self):
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.share_application_with_name(apppage.application_default_name, "u_globaldesigner")
        apppage.insight_login(ToolsBaseCase.G_Designer_Login)
        apppage.open_other_application_panel()
        apppage.open_first_application()
        #self.switch_to_newest_window()
        self.attach_allure_screenshot("open other application")
        self.assert_elements_present(InsightLocators.create_page, timeout=15)
