import time
import pytest
import allure

from pages.tools.insights.insights_application_page import InsightsAppPage
from base.tools_basecase import ToolsBaseCase
from locators.tools.insights_locators import InsightLocators
from parameterized import parameterized
from utils.data_util.data_resolver import read_test_data


class TestInsightsGraphWidget(ToolsBaseCase):

    read_data = read_test_data(file="insights_widget_test_data.json")
    create_widget_test_data = read_data.get("create_widget")
    test_data = []
    for i in create_widget_test_data:
        test_data.append(
            (
                i.get("widget_name"),
                i.get("widget_type")
            )
        )

    @allure.title("Insights Create widget - smoke test")
    @allure.description("[DDT]Check the widget can be created")
    @parameterized.expand(test_data)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.debug502
    @pytest.mark.run(order=6001)
    def test_insight_create_all_widget(self, widget_name, widget_type):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        current_app_name = widget_name + str(int(time.time()))
        apppage.create_application(current_app_name)
      
        #create page under application
        apppage.create_page(widget_name)
        
        #add widget
        apppage.add_widget_with_type(widget_type, widget_name)

        #query graph data
        if(widget_type != 'Inputs' and widget_type !="Markdown"):
            apppage.add_schema_with_ID("2")
            apppage.add_vertex_limit(10)
            if(widget_type == 'Sankey chart'):
                apppage.add_schema_with_ID("2")
            apppage.run_pattern()
            self.attach_allure_screenshot("run pattern result screenshot")
            self.assert_text(apppage.run_pattern_confirm, timeout=30)
            # self.assertTrue((apppage.run_pattern_confirm in apppage.getText_if_element_present(
            #     InsightLocators.confirm_mess)) or (apppage.no_data_confirm in apppage.getText_if_element_present(
            #     InsightLocators.confirm_mess)))

        #apply and save
        apppage.apply_save_widget()

        #delete the application
        # self.sleep(3)
        self.wait_for_element_clickable(InsightLocators.homepage, timeout=30)
        self.click(InsightLocators.homepage)
        #self.refresh_page()
        # self.sleep(6)
        apppage.delete_application(current_app_name)
        

    @allure.title("Insights Table widget conditional styling - smoke test")
    @allure.description("Insights Table widget conditional styling ")
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.tools
    @pytest.mark.insights
    @pytest.mark.run(order=6305)
    def test_insights_table_widget_conditional_styling(self):
        super().setUp()
        apppage = InsightsAppPage(self)
        apppage.insight_login()
        apppage.table_widget_conditional_styling(app_name="table_conditional_styling_test")


