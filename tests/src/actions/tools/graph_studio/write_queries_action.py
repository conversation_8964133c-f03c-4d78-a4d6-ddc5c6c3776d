import allure
import logging

from base.tools_baseaction import ToolsBaseAction
from pages.tools.graphstudio.graph_studio_write_queries_page import GSTWriteQueriesPage
from utils.run_query.query_parameter_filler import QueryParameterFiller
from utils.run_query.query_parameter_parser import QueryParameterParser

from common import tools_settings
from locators.tools.graph_studio_locators import GSTWriteQueriesLocators

LOGGER = logging.getLogger(__name__)
assertTextTimeout = 30
wait_timeout = 60

class WriteQueriesAction(ToolsBaseAction):
    """Write, install and run query actions"""

    def __init__(self, sb):
        """Init all used pages"""
        super().__init__(sb)
        self.query_page = GSTWriteQueriesPage(sb)

    @allure.step("Install all queries")
    def install_all_queries(self, graphName='MyGraph', install_all_query_timeout=1800):
        page = self.query_page
        page.navigate_to_write_queries(graphName)
        self.sb.sleep(3)  # wait queries to render
        # check if already installed successfully
        try:
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.download_query_btn, timeout=tools_settings.CLICK_TIME_OUT)
            el = self.sb.find_element(GSTWriteQueriesLocators.install_all_queries_btn).get_attribute("disabled")
            LOGGER.info("get disable attribute : " + str(el))
            if el:
                LOGGER.info("get disable attribute true, double check the not_installed_query_icon number")
                self.sb.attach_allure_screenshot("install_all_queries_btn disable attribute true screenshot")
                elements = self.sb.find_elements(GSTWriteQueriesLocators.not_installed_query_icon)
                installed_elements = self.sb.find_elements(GSTWriteQueriesLocators.installed_query_icon)
                LOGGER.info("not_installed_query_icon number:" + str(len(elements)))
                LOGGER.info("installed_query_icon number:" + str(len(installed_elements)))
                if len(elements) == 0 and len(installed_elements) > 0:
                    self.sb.attach_allure_screenshot("already installed screenshot")
                    return True
                else:
                    LOGGER.info("not_installed_query_icon exist, continue to install")
            else:
                LOGGER.info("not installed, continue to install")
        except Exception as e:
            LOGGER.info("get install_all_queries_btn disable attribute exception: " + str(e))

        if page.is_ready_install():
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.install_all_queries_btn, timeout=tools_settings.CLICK_TIME_OUT)
            page.click_install_all_queries_btn()
            page.click_install_all_queries_btn_in_popup()
            page.wait_for_query_installation_complete(not_visiable_timeout=install_all_query_timeout)
            page.assert_install_all_queries_result_in_log()
            #page.assert_install_all_queries_btn_disabled()

    @allure.step("Run query based on a query parameter data file")
    def run_query_with_parameter_file(
        self,
        query_parameter_data_file_path,
        solution,
        graphName='MyGraph',
        run_single_query_timeout=3600,
    ):
        """ Run query based on a query parameter data file """
        page = self.query_page
        filler = QueryParameterFiller(self.sb)
        parser = QueryParameterParser(query_parameter_data_file_path)
        page.navigate_to_write_queries(graphName)
        query_count = parser.get_query_count(solution)
        for query_index in range(query_count):
            query_name = parser.get_nth_query_name(solution, query_index)
            page.search_select_query(query_name)
            page.click_run_query_btn_in_toolbar()
            parameter_count = parser.get_parameter_count(solution, query_name)
            if parameter_count > 0:
                LOGGER.info(f"Start to fill parameters for query `{query_name}`")
                for parameter_index in range(parameter_count):
                    filler.fill(
                        parser.get_nth_parameter(solution, query_name, parameter_index)
                    )
                page.click_run_query_btn()
            LOGGER.info(f"Run query `{query_name}`")
            query_execution_time=page.wait_for_run_query_complete(run_single_query_timeout)
            LOGGER.info(f"Query execution time:{query_name}={query_execution_time}")
            page.check_run_query_log()


    @allure.step("Run query based on a query parameter data file")
    def run_query_with_name(
        self,
        query_name="",
        parameters="",
        assert_text="",
        graphName='MyGraph',
        expect_res=""
    ):
        page = self.query_page
        page.navigate_to_write_queries(graphName)
        page.search_select_query(query_name)
        page.click_run_installed_query_btn()

        # wait result
        LOGGER.info(f"Run query `{query_name}`")
        page.wait_for_run_query_complete(run_query_timeout=wait_timeout)
        page.wait_for_install_query_run_complete()

        # assert result
        page.click_view_json_result_btn()
        if assert_text != "":
            self.sb.assert_text(assert_text, timeout=assertTextTimeout)
        page.check_run_query_log(expect_res)
