"""
Access or manipulate elements of the TGCloud Login page
"""
import allure
import logging
import os
import shutil
import time
from locators.tools.gshell_locators import GshellLocators
from base.tools_basecase import ToolsBaseCase
from seleniumbase.common import decorators
from utils.data_util.data_resolver import read_test_data

from selenium.webdriver.remote.file_detector import UselessFileDetector

from utils.data_util.login import LoginUtils


LOGGER = logging.getLogger(__name__)


class GshellPage():

    def __init__(self, sb):
        self.sb = sb

    def getText_if_element_present(self, selector, timeout=20):
        LOGGER.info("selector= " + selector)
        if(self.sb.assert_element_present(selector, timeout)):
            return self.sb.get_text_content(selector)

    def setChromeDownloadPath(self, path="/tmp"):
        """
        set chrome download path, disable downloading popovers
        """
        path = path.rstrip(os.sep)
        Browser = os.getenv("Browser", "")
        # Browser = "chrome"
        LOGGER.info("Browser= " + Browser)
        if Browser == "":
            Browser = "chrome"
        LOGGER.info("previous setChromeDownloadPath= " + path)
        if Browser == "firefox":
            path = self.sb.get_downloads_folder()
        LOGGER.info("final setChromeDownloadPath= " + path)
        if Browser == "chrome":
            self.sb.driver.command_executor._commands["send_command"] = ("POST", '/session/$sessionId/chromium/send_command')
            params = {'cmd': 'Page.setDownloadBehavior',
                      'params': {'behavior': 'allow', 'downloadPath': path}}
            self.sb.driver.execute("send_command", params)
        elif Browser == "firefox":
            if path == "":
                LOGGER.info("file path is null, nothing to do, return")
                return
            if not os.path.exists(path):
                LOGGER.info("file path didn't exist, create")
                os.makedirs(path)
                os.system('chmod 777 ' + path)
            LOGGER.info("skip set download path, download to local folder ~/test/downloaded_files")
        elif Browser == "edge":
            self.sb.driver.command_executor._commands["send_command"] = ("POST", '/session/$sessionId/chromium/send_command')
            params = {'cmd': 'Page.setDownloadBehavior',
                      'params': {'behavior': 'allow', 'downloadPath': path}}
            self.sb.driver.execute("send_command", params)
        else:
            LOGGER.info("Browser is null: " + Browser)
        if not os.path.exists(path):
            os.makedirs(path)
        return path

    def check_downloadFile(self,download_path, file_extension='.gsql', timeout=120):
        #check download compeltely
        LOGGER.info("begin to check_downloadFile ----------- " + download_path)
        if not is_download_completely(download_path, timeout):
            self.sb.fail("check_downloadFile Error: timeout {} s".format(timeout))

        # find the latest file in the downloaded folder
        latestFilename = max(list_absDir(download_path), key=os.path.getctime)
        
        #check latestFile's extension
        latestFile_extension = os.path.splitext(latestFilename)[-1]
        LOGGER.info("latestFile_extension:"+ latestFile_extension +", file_extension=" + file_extension)
        if latestFile_extension != file_extension:
            return self.sb.fail("check_downloadFile Error: except download '{}' but download '{}'".format(latestFile_extension, file_extension))
        else:
            return True

    def rename_latestFile(self, download_path, specifyfilename):
        # find the latest file in the downloaded folder
        latestFilename = max(list_absDir(download_path), key=os.path.getctime)
        shutil.move(latestFilename, os.path.join(download_path, specifyfilename))


    @allure.step("Opening gshell page")
    def login_gshell(self, login_way="undefined"):
        loginUtil = LoginUtils(self.sb)
        loginUtil.login(ToolsBaseCase.GSQLWebShell_URL, login_way)

    @allure.step("Upload gsqlfile with (file_path)")
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def upload_gsqlfile(self, file_path):
        self.sb.click_visible_elements(GshellLocators.load_btn, timeout=30)
        upload_file(self.sb, "xpath", GshellLocators.gsql_upload_input, file_path)
        self.sb.sleep(2)
        file_contains = get_localfile_contains(file_path)
        LOGGER.info("file_contains= " + str(file_contains))
        # current_command_outerText = self.sb.find_element(GshellLocators.current_command).get_attribute('outerText')
        self.sb.assertIn(file_contains, self.getText_if_element_present(GshellLocators.current_command, timeout=200))
        self.sb.attach_allure_screenshot("upload gsqlfile result")

    @allure.step("Run gsqlfile with (file_path, result, assertTimeout, isJsonType)")
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def run_gsqlfile_verifyText(self, file_path, result, assertTimeout, isJsonType=False):
        self.upload_gsqlfile(file_path)
        self.sb.click_visible_elements(GshellLocators.run_btn, timeout=30)
        self.sb.attach_allure_screenshot("Ran gsqlfile screenshot")
        LOGGER.info('expect result parameter:' + result)
        LOGGER.info("Run gsqlfile---<file_path:{},\n result:{}, assertTimeout:{}, isJsonType:{}>---".format(file_path, result,assertTimeout,isJsonType))
        if len(result) > 0:
            if isJsonType:
                self.sb.assertIn(result, self.getText_if_element_present(GshellLocators.lastCell_jsonOutput, timeout=assertTimeout))
            else:
                self.sb.assert_text(result, GshellLocators.lastCell_textoutput, timeout=assertTimeout)
        LOGGER.info('check result pass, ' + result)
        self.sb.attach_allure_screenshot("After run gsqlfile and verify screenshot")

    @allure.step("Download GsqlFile")
    def download_gsqlfile(self, download_path, specifyfilename, timeout=120):
        self.sb.click(GshellLocators.load_btn)
        self.sb.click(GshellLocators.downloadGsql_btn)
        self.sb.sleep(10)
        Browser = os.getenv("Browser", "")
        # Browser = "firefox" tmp
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            download_path = self.sb.get_downloads_folder()
        LOGGER.info("download_gsqlfile download_path= " + download_path)
        self.check_downloadFile(download_path, os.path.splitext(specifyfilename)[-1], timeout)

        # rename downloadfile
        self.rename_latestFile(download_path, specifyfilename)


    @allure.step("Download GsqlFile")
    def download_lastJsonFile(self, download_path, specifyfilename, timeout=120):
        LOGGER.info("begin to download json file ----------- " + download_path)
        self.sb.click(GshellLocators.lastShowCSV_btn)
        self.sb.click(GshellLocators.lastDownCSV_1stbtn)
        self.sb.sleep(2)
        self.sb.click(GshellLocators.lastDownCSV_2ndbtn)

        self.check_downloadFile(download_path, os.path.splitext(specifyfilename)[-1], timeout)

        # rename downloadfile
        self.rename_latestFile(download_path, specifyfilename)

    def execute_OrderedDictQueries(self,testcase_OrderedDictQueries, is_available_cluster=True):
        for _, queryInfo in testcase_OrderedDictQueries.items():
            #define different setting according to queryOption
            isJsonType = True if 'jsonOutput' in queryInfo.keys() else False
            isSkip = True if not is_available_cluster and 'opencypher' in queryInfo["gsqlfile"] else False

            if not isSkip:
                #run gsqlfile and verify results according to queryInfo
                self.run_gsqlfile_verifyText(queryInfo["gsqlfile"], queryInfo["output"], queryInfo["assertTimeout"], isJsonType)



def upload_file(sb, by, input_locator, file_path):
    """ Upload local files to remote server by sending keys to
    a input element. Need to specify the by method and selector
    string. Set the input element to visible and send keys with
    remote file detector. """
    input_elem = sb.driver.find_element(by=by, value=input_locator)
    sb.driver.execute_script(
        """
        arguments[0].removeAttribute('hidden');
        arguments[0].style.visibility = 'visible';
        arguments[0].style.height = '10px';
        arguments[0].style.width = '10px';
        arguments[0].style.opacity = 1
        """,
        input_elem,
    )
    sb.driver.file_detector = UselessFileDetector()
    sb.choose_file(input_locator, file_path)
    LOGGER.info("upload_file file_path:" + file_path)


def get_localfile_contains(file_path):    
    with open(file_path,'r',encoding='utf-8') as f:
        file_content=f.read() 
    file_contains = file_content.split('\n')[0]
    return file_contains


def is_download_completely(directory, timeout=120):
    """
    Wait for downloads to finish with a specified timeout.
    Args
    ----
    directory : str
        The path to the folder where the files will be downloaded.
    timeout : int
      //  How many seconds to wait until timing out.
    nfiles : int, defaults to None
        If provided, also wait for the expected number of files.
    """

    seconds = 0
    dl_wait = True
    while dl_wait and seconds < timeout:
        time.sleep(1)
        dl_wait = False

        if len(os.listdir(directory))==0:
            dl_wait = True

        for fname in os.listdir(directory):
            if fname.endswith('.crdownload'):
                dl_wait = True
        seconds += 1
    if seconds < timeout:
        return True
    else:
        return False

def list_absDir(path):
    abs_dirlist = []
    for filename in os.listdir(path):
        abs_dirlist.append(os.path.join(path,filename))
    return abs_dirlist

