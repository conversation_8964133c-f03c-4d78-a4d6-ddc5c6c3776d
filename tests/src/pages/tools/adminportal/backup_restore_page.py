"""
Access or manipulate elements of the Backup and restore page on tgcloud
"""
import re
import time
import allure
import logging
from seleniumbase import decorators
import humanfriendly
from common import tools_settings
from base.cloud_basepage import CloudBasePage
from locators.tools.admin_portal_locators import (
    BackupRestoreLocators,
    PopupLocators,
    SnackBarLocators,
)
from common import decorator

LOGGER = logging.getLogger(__name__)


class BackupRestorePage(CloudBasePage):
    # main functions
    def navigate_to_page(self, timeout=tools_settings.PAGE_LOADING_TIMEOUT):
        """Navigate to current BackupRestorePage page"""
        self.sb.click_if_visible(BackupRestoreLocators.nav_backup_restore, timeout=timeout)
        self.sb.wait_for_element_visible(BackupRestoreLocators.nav_backup_restore, timeout=timeout)
        self.sb.wait_for_element_visible(BackupRestoreLocators.scheduled_backup_tag)

    # manually backup
    def backup(self, backup=tools_settings.DEFAULT_BACKUP_NAME):
        """Run backup"""
        self.sb.slow_scroll_to(BackupRestoreLocators.backup_restore_section)
        self.sb.update_text(BackupRestoreLocators.backup_input, backup)
        self.sb.click(BackupRestoreLocators.backup_btn)
        self.sb.wait_for_element_visible(PopupLocators.popup)
        self.sb.click(PopupLocators.ok_btn)

    @decorator.log_runtime("Wait for backup process complete")
    def wait_for_backup_succeed(self, timeout=tools_settings.DEFAULT_BACKUP_NAME):
        """Wait for backup complete."""
        start_ms = time.time() * 1000.0
        stop_ms = start_ms + (float(timeout) * 1000.0)
        backup_succeed = False
        self.sb.slow_scroll_to(BackupRestoreLocators.manage_backups_section)
        while time.time() * 1000.0 <= stop_ms:
            if self.is_snackbar_message_success() or self.is_backup_complete():
                self.sb.attach_allure_screenshot(
                    "Screenshot: Backup completed succssfully!"
                )
                self.dismiss_snackbar()
                backup_succeed = True
                break
            if self.is_failed_popup():
                self.sb.attach_allure_screenshot("Screenshot: Backup failed with popup message!")
                backup_succeed = False
                break
            if self.is_backup_restore_failed():
                self.sb.attach_allure_screenshot("Screenshot: Backup failed!")
                backup_succeed = False
                break
            self.sb.sleep(tools_settings.BACKUP_CHECK_DELAY)
        self.sb.assert_true(
            backup_succeed, f"Backup failed: {self.get_backup_restore_log()}"
        )

    def is_snackbar_message_success(self):
        success_message = "success"
        return (
            self.is_snackbar_popup()
            and success_message in self.get_snackbar_message().lower()
        )

    def is_snackbar_popup(self):
        return self.sb.is_element_visible(SnackBarLocators.span)

    def get_snackbar_message(self):
        return self.sb.get_text(SnackBarLocators.span)

    def dismiss_snackbar(self):
        if self.is_snackbar_popup():
            self.sb.click(SnackBarLocators.dismiss_btn)

    def is_backup_restore_log_exist(self):
        return self.sb.is_element_visible(BackupRestoreLocators.backup_restore_log)

    def get_backup_restore_log(self):
        return self.sb.get_text(BackupRestoreLocators.backup_restore_log)

    def is_backup_complete(self):
        success_backup_log = "Backup completes"
        return (
            self.is_backup_restore_log_exist()
            and success_backup_log in self.get_backup_restore_log()
        )

    def is_backup_restore_failed(self):
        fail_messages = ["fail", "error"]
        return self.is_backup_restore_log_exist() and any(
            fail_message in self.get_backup_restore_log()
            for fail_message in fail_messages
        )

    def is_failed_popup(self):
        return self.sb.is_element_visible(PopupLocators.failed_head)

    def get_backup_in_log(self):
        """Get the backup name in the success log"""
        return re.search(
            r"the path: .*\/(.*)\n", self.get_backup_restore_log(), re.IGNORECASE
        ).group(1)

    def restore(self, backup=tools_settings.DEFAULT_BACKUP_NAME):
        """Run restore"""
        self.sb.slow_scroll_to(BackupRestoreLocators.backup_restore_section)
        self.check_backup_info(backup)
        back_full_name = self.get_backup_in_list(backup)
        self.sb.update_text(BackupRestoreLocators.backup_input, back_full_name)
        self.sb.click(BackupRestoreLocators.restore_btn)
        self.sb.wait_for_element_visible(PopupLocators.popup)
        self.sb.click(PopupLocators.ok_btn)

    def is_restore_complete(self):
        success_restore_log = "Restore completes"
        return (
            self.is_backup_restore_log_exist()
            and success_restore_log in self.get_backup_restore_log()
        )

    @decorator.log_runtime("Wait for restore process complete")
    def wait_for_restore_complete(self, timeout=tools_settings.DEFAULT_RESTORE_TIMEOUT):
        """Wait for restore complete"""
        start_ms = time.time() * 1000.0
        stop_ms = start_ms + (float(timeout) * 1000.0)
        restore_succeed = False
        self.sb.slow_scroll_to(BackupRestoreLocators.manage_backups_section)
        while time.time() * 1000.0 <= stop_ms:
            if self.is_snackbar_message_success() or self.is_restore_complete():
                self.sb.attach_allure_screenshot("Screenshot: Restore completed!")
                self.dismiss_snackbar()
                restore_succeed = True
                break
            if self.is_failed_popup():
                self.sb.attach_allure_screenshot("Screenshot: Restore failed!")
                return
            if self.is_backup_restore_failed():
                self.sb.attach_allure_screenshot("Screenshot: Restore failed!")
                restore_succeed = False
                break
            self.sb.sleep(tools_settings.RESTORE_CHECK_DELAY)
        self.sb.assert_true(
            restore_succeed, f"Restore failed: {self.get_backup_restore_log()}"
        )

    # Backup list
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def check_backup_info(
        self, backup=tools_settings.DEFAULT_BACKUP_NAME, mini_size="1KB", timeout=tools_settings.PAGE_LOADING_TIMEOUT
    ):
        """Check backup statistics, Currently there is no function of finding backup by flipping pages"""
        self.sb.wait_for_element_present("//tr//td", timeout=timeout)
        self.sb.assert_true(
            self.is_backup_exists(backup), f"Cannot find the backup `{backup}`."
        )
        self.sb.assert_false(
            self.is_backup_duplicates(backup),
            f"More than 1 backup found by filtering `{backup}`.",
        )
        backup_size = self.get_backup_size(backup)
        self.sb.assert_true(
            humanfriendly.parse_size(mini_size)
            <= humanfriendly.parse_size(backup_size),
            f"The size `{backup_size}` of backup `{backup}` is larger than expected mini size `{mini_size}`.",
        )
        LOGGER.debug(f"Checked the size of backup `{backup}` is `{backup_size}`.")

    def select_this_cluster(self):
        """select this/instance/cluster tab"""
        if self.sb.is_element_enabled(BackupRestoreLocators.this_cluster_label):
            self.sb.click(BackupRestoreLocators.this_cluster_label)

    def is_other_cluster_enabled(self):
        """return true if the other instances/cluster tab enabled"""
        return self.sb.is_element_enabled(BackupRestoreLocators.other_cluster_label)

    def select_other_cluster(self):
        """select the other instances/cluster tab"""
        if self.is_other_cluster_enabled():
            self.sb.click(BackupRestoreLocators.other_cluster_label)

    def backup_in_list_locator(self, backup=tools_settings.DEFAULT_BACKUP_NAME):
        """return the locator for backup list"""
        return f"//tr//td[contains(text(),'{backup}')]"

    def backup_size_locator(self, backup=tools_settings.DEFAULT_BACKUP_NAME):
        """return the locator for backup size"""
        return f"//tr[td[contains(text(),'{backup}')]]/td[2]"

    def get_backup_in_list(self, backup=tools_settings.DEFAULT_BACKUP_NAME):
        """return the full back name in the backup list"""
        return self.sb.get_text(self.backup_in_list_locator(backup)).strip()

    def is_backup_exists(self, backup=tools_settings.DEFAULT_BACKUP_NAME):
        """Return true if backup exists"""
        return self.sb.is_element_present(self.backup_in_list_locator(backup))

    def get_backup_size(self, backup=tools_settings.DEFAULT_BACKUP_NAME):
        """return human-readable backup size"""
        return self.sb.get_text(self.backup_size_locator(backup)).strip()

    def is_backup_duplicates(self, backup=tools_settings.DEFAULT_BACKUP_NAME):
        """Return true if backup duplicated"""
        return len(self.sb.find_elements(self.backup_in_list_locator(backup))) > 1
