import logging
from base.tools_basecase import ToolsBaseCase
from base.tools_basepage import ToolsBasePage
from typing import List

from locators.tools.graph_studio_locators import (
    GSTHomeLocators,
)

from utils.data_util.login import LoginUtils
from utils.data_util.data_resolver import read_test_data

LOGGER = logging.getLogger(__name__)

class GSTUtility(ToolsBasePage): 

    def __init__(self, sb):
        self.test_env = read_test_data(file="tools_test_data.json").get("test_env")
        LOGGER.info("GSTUtility init self.test_env :" + self.test_env)
        self.sb = sb

    def GST_login(self, login_way="undefined"):
        loginUtil = LoginUtils(self.sb)
        loginUtil.login(ToolsBaseCase.GraphStudio_URL, login_way)

    def get_current_graph(self):
        return self.sb.get_text(GSTHomeLocators.current_graph).strip()

    def clear_local_storage(self):
        self.sb.execute_script("window.localStorage.clear();")

    def trigger_graph_event_on_chart_node(self, graphChartVarName: str, node, eventName: str):
        cmd = f"{graphChartVarName}.{eventName}({node});"

        LOGGER.info(f"{eventName} node cmd: " + cmd)
        self.sb.execute_script(cmd)

    def trigger_graph_event_on_chart_link(self, graphChartVarName: str, link, eventName: str):
        cmd = f"{graphChartVarName}.{eventName}({link});"

        LOGGER.info(f"{eventName} link cmd: " + cmd)
        self.sb.execute_script(cmd)

    def trigger_graph_event_on_chart_blank_area(self, graphChartVarName: str, eventName: str):
        cmd = f"{graphChartVarName}.{eventName}(undefined);"

        LOGGER.info(f"{eventName} link cmd: " + cmd)
        self.sb.execute_script(cmd)
    
    def trigger_graph_event(self, graphChartVarName: str, graph, eventName: str):
        cmd = f"{graphChartVarName}.{eventName}({graph});"

        LOGGER.info(f"{eventName} node cmd: " + cmd)
        self.sb.execute_script(cmd)

    def get_nodes(self, graphChartVarName: str):
        cmd = f"return {graphChartVarName}.current.getNodes();"

        LOGGER.info("get nodes cmd: " + cmd)
        return self.sb.execute_script(cmd)

    def get_links(self, graphChartVarName: str):
        cmd = f"return {graphChartVarName}.current.getLinks();"

        LOGGER.info("get links cmd: " + cmd)
        return self.sb.execute_script(cmd)

    def get_nodes_by_types(self, graphChartVarName: str, types: List[str]):
        cmd = f"return {graphChartVarName}.current.getNodesByTypes({types});"

        LOGGER.info("get nodes cmd: " + cmd)
        return self.sb.execute_script(cmd)

    def select_edge_by_name(self, edge_name="multi_edge_6"):
        cmd = f"schemaDesignerGraphRef.current.selectEdgesByTypes(['" + edge_name + "'])"

        LOGGER.info("select_edge_by_name cmd: " + cmd)
        return self.sb.execute_script(cmd)

    def get_links_by_types(self, graphChartVarName: str, types: List[str]):
        cmd = f"return {graphChartVarName}.current.getLinksByTypes({types});"

        LOGGER.info("get links cmd: " + cmd)
        return self.sb.execute_script(cmd)

    def get_node(self, graphChartVarName: str, target_node):
        nodes = self.get_nodes(graphChartVarName)
        res = None
        for node in nodes: 
            if node['type'] == target_node['type'] and node['id'] == target_node['id']: 
                res = node
                break
        LOGGER.info("get node: " + str(res))
        return res

    def get_link(self, graphChartVarName: str, target_link):
        links = self.get_links(graphChartVarName)
        LOGGER.info("get links: "+ str(links))
        res = None
        for link in links: 
            if link['type'] == target_link['type'] and \
                link['source']['type'] == target_link['source']['type'] and \
                link['source']['id'] == target_link['source']['id'] and \
                link['target']['type'] == target_link['target']['type'] and \
                link['target']['id'] == target_link['target']['id']: 
                res = link
                break
        LOGGER.info("get link: "+ str(res))
        return res
