import os
import glob
import logging
import re

from common import decorator
from utils.pop_window.graph_studio_pop_window import GraphStudioWindows
from locators.tools.graph_studio_locators import (
    DialogLocators,
    GSTHomeLocators,
    GSTLoadDataLocators,
    GSTWriteQueriesLocators
)
from base.tools_basecase import ToolsBaseCase
from base.tools_basepage import ToolsBasePage
from common import tools_settings

from os import listdir
from utils.data_util.data_resolver import read_test_data
from utils.data_util.starter_kit_resolver import read_starter_kit_data
import allure
from seleniumbase import decorators
from pages.tools.graphstudio.graph_studio_load_data_page import GSTLoadDataPage
from pages.tools.adminportal.adminportal_page import GAPPage
from random import *

from pages.tools.graphstudio.utils.graph_studio_schema_designer import GSTSchemaDesigner

LOGGER = logging.getLogger(__name__)
assertTextTimeout = 60
clickTimeout = 60
short_clickTimeout = 5
wait_render_time = 120

from utils.data_util.login import LoginUtils

class GSTHomePage(ToolsBasePage):
    """Graph Studio Home page objects"""

    def __init__(self, sb):
        self.test_env = read_test_data(file="tools_test_data.json").get("test_env")
        self.graphName = read_starter_kit_data(file="starter_kit_3.8.json").get("SolutionList")[2].get("GraphName")
        LOGGER.info("GSTHomePage init self.test_env :" + self.test_env)
        LOGGER.info("GSTHomePage init self.graphName :" + self.graphName)
        self.sb = sb
        self.schema_designer = GSTSchemaDesigner(self.sb)

    def GST_login(self, login_way="undefined"):
        loginUtil = LoginUtils(self.sb)
        loginUtil.login(ToolsBaseCase.GraphStudio_URL, login_way)

    def get_current_graph(self, timeout=tools_settings.PAGE_LOADING_TIMEOUT):
        self.sb.wait_for_element_visible(GSTHomeLocators.current_graph, timeout=timeout)
        return self.sb.get_text(GSTHomeLocators.current_graph).strip()

    def verify_load_data(self, total_vertex, total_edge):
        """verify the load data correctness by checking the total vertex and edge count."""
        statistics = GSTLoadDataPage(self.sb).get_statistics()
        self.sb.assert_equal(
            total_vertex,
            statistics["Total Vertex"],
            f"Load data statistics error, vertex count mismatch. Expect {total_vertex}, Actual {statistics['Total Vertex']}",
        )
        self.sb.assert_equal(
            total_edge,
            statistics["Total Edge"],
            f"Load data statistics error, Edge count mismatch. Expect {total_vertex}, Actual {statistics['Total Vertex']}",
        )
        LOGGER.info(f"Verified Total Vertex : {statistics['Total Vertex']}")
        LOGGER.info(f"Verified Total Edge : {statistics['Total Edge']}")


    @decorator.log_runtime(description="Import solution")
    @allure.step("Import solution")
    def import_solution(self, file_path):
        """import solution by file path"""
        if self.sb.is_element_in_an_iframe(GSTHomeLocators.nav_home):
            self.sb.switch_to_frame_of_element(GSTHomeLocators.nav_home)
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.nav_home)
        LOGGER.info(f"Import solution from {file_path}")
        self.sb.choose_file(GSTHomeLocators.import_solution_input, file_path)
        self.sb.assert_element_present(DialogLocators.warning_head, timeout=assertTextTimeout)
        self.sb.assert_element_present(DialogLocators.overwrite_btn, timeout=assertTextTimeout)
        self.sb.click(DialogLocators.overwrite_btn, timeout=clickTimeout)
        self.sb.assert_element_present(GSTHomeLocators.progressBar, timeout=wait_render_time)
        self.sb.assert_element_present(GSTHomeLocators.importing_msg, timeout=wait_render_time)
        self.sb.wait_for_element_absent(GSTHomeLocators.importing_msg, timeout=1500)
        self.sb.assert_element_present(GSTHomeLocators.imported_msg, timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("Solution imported successfully")

        # double check importing successfully
        self.sb.wait_for_element_clickable(GSTHomeLocators.graph_menu_btn)
        self.sb.slow_click(GSTHomeLocators.graph_menu_btn)
        self.sb.attach_allure_screenshot("graph list screenshot")
        if len(self.sb.find_elements(GSTHomeLocators.delete_graph_button)) > 0:
            LOGGER.info("import successfully")
            self.sb.attach_allure_screenshot("graph list screenshot")
        else:
            raise Exception("import failed, graph list number wrong")

    def switch_to_graph(self, graph_name):
        """switch to a graph"""
        self.sb.wait_for_element(GSTHomeLocators.current_graph, timeout=60)
        if (
            self.sb.find_element(GSTHomeLocators.current_graph).text.strip()
            == graph_name
        ):
            return
        self.sb.slow_click(GSTHomeLocators.graph_menu_btn)
        self.sb.attach_allure_screenshot("graph list screenshot")
        self.sb.slow_click(
            "//span[contains(@class,'graph-name-in-list') and normalize-space(text())='"
            + graph_name
            + "']",
            timeout=20,
        )

    def myLogout(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        Browser = os.getenv("Browser", "")
        # Browser = "firefox"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            LOGGER.info("Firefox browser can't get cookie, skip this case temply ")
            return True
        cc = GAPPage().get_tg_cookie(self.sb)
        # self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        # self.sb.click(GSTHomeLocators.logoutButton, timeout=clickTimeout)
        # use JS method
        self.sb.execute_script(GSTHomeLocators.tools_header_icon_JS)
        self.sb.sleep(2)
        self.sb.execute_script(GSTHomeLocators.logout_icon_JS)
        self.sb.sleep(10)
        self.sb.attach_allure_screenshot("logouted 10s screenshot")

        # request by api with previous cookie
        command = "curl  --insecure  --location --request GET '{0}/api/data/user_icons' \
                            --header 'Cookie:  TigerGraphApp={1}'".format(self.test_env, cc)
        LOGGER.info("command= " + command)
        api_ressult_list = os.popen(command).readlines()
        LOGGER.info("api_ressult_list= " + str(api_ressult_list))
        result = api_ressult_list[0].replace('true', 'True').replace('false', 'False').replace("null", "None")
        LOGGER.info("result= " + str(result))

        # assert API results
        dict_res = eval(result)
        if dict_res["error"]:
            self.sb.assert_equal(dict_res["message"], 'You are not authorized to use this API.')
        else:
            raise Exception("cookie not expired after logout: " + str(api_ressult_list))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def importSolution(self):
        gsw = GraphStudioWindows(self.sb)
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        self.sb.click(GSTHomeLocators.nav_home, timeout=clickTimeout)
        fileName = self.get_solution_file()
        LOGGER.info("import solution fileName:" + str(fileName))
        filePath = "./data/solution/" + fileName
        # filePath = "./data/solution/wrongTarball.tar.gz"
        self.sb.choose_file(
            "//input[@aria-label='Import An Existing Solution']", filePath
        )

        # 1: cancle   2:overwrite
        self.sb.click_nth_visible_element(".mat-dialog-actions > .ng-star-inserted", 2)
        self.sb.sleep(2)
        self.sb.attach_allure_screenshot(
            "uploaded, begin to import solution screenshot"
        )
        self.sb.wait_for_element_visible(GSTHomeLocators.progressBar, timeout=wait_render_time)
        self.sb.wait_for_element_absent(GSTHomeLocators.progressBar, timeout=1500)
        title = gsw.getPopWindowContent()
        LOGGER.info("pop window title:" + str(title))
        self.sb.assert_equal("Solution is importedDISMISS", title, msg=None)

        # double check the graph
        self.sb.wait_for_element_clickable(GSTHomeLocators.graph_menu_btn)
        self.sb.slow_click(GSTHomeLocators.graph_menu_btn)
        self.sb.assert_text("MyGraph", timeout=assertTextTimeout)
        self.sb.assert_text("MyMultiEdge", timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("graph list screenshot")

    def importSolution_with_tag(self, tag):
        gsw = GraphStudioWindows(self.sb)
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        self.sb.click(GSTHomeLocators.nav_home, timeout=clickTimeout)
        filePath = "./data/solution/{}".format(tag)
        fileExtension = "*.tar.gz"
        solutionPath = glob.glob(os.path.join(filePath, fileExtension))
        # filePath = "./data/solution/wrongTarball.tar.gz"
        self.sb.choose_file(
            "//input[@aria-label='Import An Existing Solution']", solutionPath[0]
        )

        # 1: cancle   2:overwrite
        self.sb.click_nth_visible_element(".mat-dialog-actions > .ng-star-inserted", 2)
        self.sb.sleep(2)
        self.sb.attach_allure_screenshot(
            "uploaded, begin to import solution screenshot"
        )
        self.sb.wait_for_element_visible(GSTHomeLocators.progressBar, timeout=wait_render_time)
        self.sb.wait_for_element_absent(GSTHomeLocators.progressBar, timeout=1500)
        title = gsw.getPopWindowContent()
        LOGGER.info("pop window title:" + str(title))
        self.sb.assert_equal("Solution is importedDISMISS", title, msg=None)
        # double check the graph
        self.sb.wait_for_element_clickable(GSTHomeLocators.graph_menu_btn)
        self.sb.slow_click(GSTHomeLocators.graph_menu_btn)
        self.sb.attach_allure_screenshot("graph list screenshot")

    '''
    click on canvas method 1:
        x = 240
        y = 85
        # click province
        # click infectionCase edge
        self.sb.click_with_offset(
         ".DVSL-canvas", 677-x, 321-y, by="css selector", mark=None, timeout=None, center=None)
    edgeName begin with multi_edge_6
    '''
    def add_multi_edge(self, edgeName="multi_edge_6"):
        Browser = os.getenv("Browser", "")
        # Browser = "firefox"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            # this case passed on local but failed in docker env, skip temply
            LOGGER.info("this case passed on local but failed in docker env, so skip temply on " + Browser)
            return True
        gsw = GraphStudioWindows(self.sb)
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        # swtich to MyMultiEdge graph
        self.switch_to_graph("MyMultiEdge")
        self.sb.click(GSTHomeLocators.nav_design_schema, timeout=clickTimeout)
        self.wait_progress_bar()
        self.sb.wait_for_element_clickable(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("opened design schema menu screenshot")

        # if multi_edge_6 existed, delete
        try:
            self.schema_designer.select_edge_by_name(edge_name=edgeName)
            self.sb.wait_for_element_clickable(GSTHomeLocators.delete_item_button, timeout=clickTimeout)
            self.sb.click(GSTHomeLocators.delete_item_button, timeout=clickTimeout)
            self.sb.wait_for_element_clickable(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
            self.sb.click(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
            self.sb.wait_for_element_clickable(DialogLocators.continue_btn, timeout=clickTimeout)
            self.sb.click(DialogLocators.continue_btn, timeout=clickTimeout)

            # wait
            self.sb.attach_allure_screenshot("delete multi edge result screenshot")
            self.wait_horizon_progress_bar(wait_present_time=wait_render_time, wait_not_visible_time=wait_render_time)
            self.sb.wait_for_element_clickable(GSTHomeLocators.verify_button, timeout=wait_render_time)
            self.sb.click(GSTHomeLocators.verify_button, timeout=clickTimeout)
        except Exception as e:
            LOGGER.info("delete " + edgeName + " exception:" + str(e))
        finally:
            self.sb.refresh()
            self.sb.sleep(3)  # improve the success rate
            self.is_element_in_an_iframe_and_switch_to_frame(GSTHomeLocators.nav_actions)
            self.sb.wait_for_element_visible(GSTHomeLocators.nav_actions, timeout=clickTimeout)
            self.sb.sleep(3)  # improve the success rate
            # swtich to MyMultiEdge graph
            self.switch_to_graph("MyMultiEdge")
            self.sb.click(GSTHomeLocators.nav_design_schema, timeout=clickTimeout)
            self.sb.wait_for_element_clickable(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
            self.sb.attach_allure_screenshot("after delete multi edge ,opened design schema menu screenshot")
            LOGGER.info("init " + edgeName + " over")


        #add a multi_edge
        self.sb.sleep(3)  # should wait the vertex to render
        self.sb.wait_for_element_clickable(GSTHomeLocators.add_edge_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.add_edge_button, timeout=clickTimeout)
        self.schema_designer.single_click_node_type('City')
        self.schema_designer.single_click_node_type('Province')
        self.sb.sleep(1)
        self.sb.attach_allure_screenshot("added an edge done screenshot")

        # add attribute
        self.sb.wait_for_element_visible(GSTHomeLocators.cancel_edge_in_panel, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.input_edge_name, edgeName)
        for i in range(4):
            self.sb.click(GSTHomeLocators.add_attributes_in_panel, timeout=clickTimeout)
            self.sb.sleep(1) # wait on firefox

        # input attribute name
        input_name_element_list = self.sb.find_elements(GSTHomeLocators.input_attribute_names)
        LOGGER.info("input_name_element_list: " + str(len(input_name_element_list)))
        for i in range(len(input_name_element_list)):
            if i == 0:
                input_name_element_list[i].send_keys("age")
            if i == 1:
                input_name_element_list[i].send_keys("name")
            if i == 2:
                input_name_element_list[i].send_keys("myDate")
            if i == 3:
                input_name_element_list[i].send_keys("myDouble")
        self.sb.attach_allure_screenshot("type attribute names done screenshot")

        # select attribute type
        select_type_element_list = self.sb.find_elements(GSTHomeLocators.select_type)
        LOGGER.info("select_type_element_list: " + str(len(select_type_element_list)))
        for i in range(len(select_type_element_list)):
            select_type_element_list[i].click()
            if i == 0:
                self.sb.click(GSTHomeLocators.int_type, timeout=clickTimeout)
            if i == 1:
                self.sb.click(GSTHomeLocators.string_type, timeout=clickTimeout)
            if i == 2:
                self.sb.click(GSTHomeLocators.datetime_type, timeout=clickTimeout)
            if i == 3:
                self.sb.click(GSTHomeLocators.double_type, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("select attribute type done screenshot")

        # select discriminator attribute type
        checkboxs_element_list = self.sb.find_elements(GSTHomeLocators.directed_and_other_checkboxs)
        LOGGER.info("checkboxs_element_list: " + str(len(checkboxs_element_list)))
        for i in range(len(checkboxs_element_list)):
            if i == 0:
                self.sb.find_elements(GSTHomeLocators.directed_and_other_checkboxs)[i].click()
            if i >= 1:
                self.sb.find_elements(GSTHomeLocators.directed_and_other_checkboxs)[i+1].click()
        self.sb.attach_allure_screenshot("select checkboxs done screenshot")

        # save and publish
        self.sb.wait_for_element_clickable(GSTHomeLocators.save_item_in_panel, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.save_item_in_panel, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
        self.sb.click(DialogLocators.continue_btn, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("save and publish screenshot")

        # assert
        self.wait_horizon_progress_bar(wait_present_time=wait_render_time, wait_not_visible_time=wait_render_time)
        self.sb.attach_allure_screenshot("final result screenshot")
        self.sb.assert_text('GraphStudio has updated the data mapping based on your changes to the schema', timeout=wait_render_time)

    def is_element_in_an_iframe_and_switch_to_frame(self, selector, by="css selector"):
        """ compatible iframe
            if element in an iframe, switch the iframe;
            if not, don't switch
        """
        if self.sb.is_element_present(selector, by=by):
            LOGGER.info('element is present, no need switch iframe')
            return
        soup = self.sb.get_beautiful_soup()
        iframe_list = soup.select("iframe")
        for iframe in iframe_list:
            iframe_identifier = None
            if iframe.has_attr("name") and len(iframe["name"]) > 0:
                iframe_identifier = iframe["name"]
            elif iframe.has_attr("id") and len(iframe["id"]) > 0:
                iframe_identifier = iframe["id"]
            elif iframe.has_attr("class") and len(iframe["class"]) > 0:
                iframe_class = " ".join(iframe["class"])
                iframe_identifier = '[class="%s"]' % iframe_class
            else:
                continue
            try:
                self.sb.switch_to_frame(iframe_identifier)
                break
            except Exception as e:
                LOGGER.info(str(e))
            finally:
                LOGGER.info("switch done")

    def edit_multi_edge(self):
        Browser = os.getenv("Browser", "")
        # Browser = "firefox"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            # this case passed on local but failed in docker env, skip temply
            LOGGER.info("this case passed on local but failed in docker env, so skip temply on " + Browser)
            return True
        numTmp =randint(10, 1000)
        edit_edge_name = "multi_edge_"+str(numTmp)
        # edit_edge_name = "multi_edge_644"
        LOGGER.info("edit_edge_name: " + edit_edge_name)
        self.add_multi_edge(edit_edge_name)
        self.sb.refresh()
        self.is_element_in_an_iframe_and_switch_to_frame(GSTHomeLocators.nav_actions)
        self.sb.wait_for_element_visible(GSTHomeLocators.nav_actions, timeout=60)
        self.sb.sleep(3) # improve the success rate
        # swtich to MultiEdge graph
        self.switch_to_graph("MyMultiEdge")
        self.sb.click(GSTHomeLocators.nav_design_schema, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("opened map data menu screenshot")

        #double click multi_edge to edit
        self.sb.sleep(3)  # improve the success rate
        # self.double_click_edge_on_canvas(edgeType=edit_edge_name, sourceVertexType="City", targetVertexType="Province")
        self.schema_designer.double_click_link_type(edit_edge_name, "City", "Province")
        self.sb.sleep(1)
        self.sb.attach_allure_screenshot("double click edge done screenshot")

        # edit attribute
        self.sb.wait_for_element_visible(GSTHomeLocators.cancel_edge_in_panel, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.input_edge_name, edit_edge_name + "_new")

        # edit attribute name
        input_name_element_list = self.sb.find_elements(GSTHomeLocators.input_attribute_names)
        LOGGER.info("input_name_element_list: " + str(len(input_name_element_list)))
        for i in range(len(input_name_element_list)):
            input_name_element_list[i].clear()
            if i == 0:
                input_name_element_list[i].send_keys("age" + "_new")
            if i == 1:
                input_name_element_list[i].send_keys("name" + "_new")
            if i == 2:
                input_name_element_list[i].send_keys("myDate" + "_new")
            if i == 3:
                input_name_element_list[i].send_keys("myDouble" + "_new")
        self.sb.attach_allure_screenshot("edit attribute names done screenshot")

        # edit attribute type
        select_type_element_list = self.sb.find_elements(GSTHomeLocators.select_type)
        LOGGER.info("select_type_element_list: " + str(len(select_type_element_list)))
        for i in range(len(select_type_element_list)):
            select_type_element_list[i].click()
            self.sb.sleep(0.5)
            if i == 0:
                self.sb.click(GSTHomeLocators.uint_type, timeout=clickTimeout)
            if i == 1:
                self.sb.click(GSTHomeLocators.int_type, timeout=clickTimeout)
            if i == 2:
                self.sb.click(GSTHomeLocators.string_type, timeout=clickTimeout)
            if i == 3:
                self.sb.click(GSTHomeLocators.datetime_type, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("edit attribute type done screenshot")

        # edit discriminator attribute type
        checkboxs_element_list = self.sb.find_elements(GSTHomeLocators.directed_and_other_checkboxs)
        LOGGER.info("checkboxs_element_list: " + str(len(checkboxs_element_list)))
        for i in range(len(checkboxs_element_list)):
            if i < 5:
                self.sb.find_elements(GSTHomeLocators.directed_and_other_checkboxs)[i].click()
        self.sb.attach_allure_screenshot("edit checkboxs done screenshot")

        # save and publish
        self.sb.wait_for_element_clickable(GSTHomeLocators.save_item_in_panel, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.save_item_in_panel, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
        self.sb.click(DialogLocators.continue_btn, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("save and publish screenshot")

        # assert
        self.wait_horizon_progress_bar(wait_present_time=wait_render_time, wait_not_visible_time=wait_render_time)
        self.sb.attach_allure_screenshot("edit final result screenshot")
        self.sb.assert_text('GraphStudio has updated the data mapping based on your changes to the schema', timeout=wait_render_time)


    def click_vertex_on_canvas(self, graphChartVarName="schemaDesignerGraphEvents", eventName="onClick",vertexType=""):
        cmd = f"{graphChartVarName}.{eventName}({{ 'type': '{vertexType}', 'id': '{vertexType}' }})"

        LOGGER.info("click cmd: " + cmd)
        self.sb.execute_script(cmd)

    def double_click_vertex_on_canvas(self, graphChartVarName="schemaDesignerGraphEvents", eventName="onDoubleClick",vertexType=""):
        cmd = f"{graphChartVarName}.{eventName}({{ 'type': '{vertexType}', 'id': '{vertexType}' }})"

        LOGGER.info("double click vertex cmd: " + cmd)
        self.sb.execute_script(cmd)

    def double_click_edge_on_canvas(self, graphChartVarName="schemaDesignerGraphEvents", eventName="onDoubleClick", edgeType="",sourceVertexType ="" ,targetVertexType=""):
        cmd = f"{graphChartVarName}.{eventName}({{ 'type': '{edgeType}', 'source': {{ 'type': '{sourceVertexType}', 'id': '{sourceVertexType}' }}, 'target': {{ 'type': '{targetVertexType}', 'id': '{targetVertexType}' }} }})"

        LOGGER.info("double click edge cmd: " + cmd)
        self.sb.execute_script(cmd)

    def get_nodes(self, graphChartVarName="schemaDesignerGraphRef"):
        cmd = f"return {graphChartVarName}.current.getNodes()"

        LOGGER.info("get nodes cmd: "+ cmd)
        return self.sb.execute_script(cmd)

    def get_links(self, graphChartVarName="schemaDesignerGraphRef"):
        cmd = f"return {graphChartVarName}.current.getLinks()"

        LOGGER.info("get nodes cmd: "+ cmd)
        return self.sb.execute_script(cmd)

    def write_queries_with_multi_edge(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)
        # turn to write queries
        self.switch_to_graph("MyMultiEdge")
        self.sb.click(GSTHomeLocators.nav_write_queries, timeout=clickTimeout)
        self.wait_progress_bar()
        self.sb.wait_for_element_clickable(GSTHomeLocators.console_button, timeout=wait_render_time)
        self.sb.wait_for_element_clickable(GSTHomeLocators.searchButton, timeout=wait_render_time)
        self.delete_exist_QueryAndDraft(query_name="multi_edge")
        self.sb.attach_allure_screenshot("delete query done screenshot")

        # create new gsql query
        self.sb.wait_for_element_clickable(GSTHomeLocators.createNewQueryButton, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.createNewQueryButton, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.inputNewQueryName, "multi_edge", timeout=clickTimeout)
        self.sb.click(DialogLocators.create_btn, timeout=clickTimeout)
        self.wait_progress_bar()

        # input query, save and run
        myGsql = "CREATE QUERY multi_edge(VERTEX<Province> p) FOR GRAPH MyMultiEdge SYNTAX V2 {\n" +\
                  "ListAccum<EDGE> @@edgeList;\n"+\
                  "seed = {p};\n"+\
                  "S1 = SELECT s "+\
                         "FROM seed:s -(:e)-:t "+\
                         "ACCUM @@edgeList += e; \n"+\
                  "PRINT  @@edgeList;"+\
                "}"
        cmd = "queryEditorChart.textEditor.setValue(`"+myGsql+"`)"
        LOGGER.info("create query cmd: " + cmd)
        self.sb.execute_script(cmd)
        self.sb.attach_allure_screenshot("create query done screenshot")
        # save and run
        # before save, check auto_save status
        self.sb.sleep(10)
        self.sb.attach_allure_screenshot("wait auto_save done screenshot")
        self.sb.wait_for_element_clickable(GSTHomeLocators.save_query_button, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.save_query_button, timeout=clickTimeout)
        self.wait_progress_bar(wait_present_time=10)
        self.sb.attach_allure_screenshot("save query done screenshot")
        LOGGER.info("click save_query_button")
        self.sb.click(GSTHomeLocators.run_query_button, timeout=clickTimeout)
        self.wait_progress_bar(wait_present_time=10)
        LOGGER.info("click run_query_button")
        self.sb.type(GSTHomeLocators.input_parameter, "Gyeongsangbuk-do", timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.run_query_button_below_input_parameter, timeout=clickTimeout)

        # wait result
        self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        self.sb.attach_allure_screenshot("run query with multi edge done screenshot")

        # assert result
        self.sb.wait_for_element_clickable(GSTHomeLocators.viewJsonResultButton, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.viewJsonResultButton, timeout=clickTimeout)
        self.sb.assert_text('myDate', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert json result screenshot")
        self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        self.sb.assert_elements_present(GSTHomeLocators.gridcell, timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert table result screenshot")
        self.sb.click(GSTHomeLocators.view_log_button, timeout=clickTimeout)
        self.sb.assert_text('Query run successfully', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert log result screenshot")

    def run_queries_and_check_table_results(self, testcase_OrderedDictQueries):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        # turn to write queries
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_write_queries, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console_button, timeout=wait_render_time)
        self.sb.wait_for_element_clickable(GSTHomeLocators.searchButton, timeout=wait_render_time)

        # find query and run
        for query_name, queryInfo in testcase_OrderedDictQueries.items():
            LOGGER.info("query_name= " + query_name)
            LOGGER.info("queryInfo= " + str(queryInfo))
            if "null" not in query_name:
                continue

            self.sb.type(GSTHomeLocators.searchButton, query_name)
            self.sb.sleep(2)
            queryList = self.sb.find_elements(GSTHomeLocators.queryList)
            LOGGER.info("query list =" + str(len(queryList)))
            if len(queryList) > 0:
                LOGGER.info("find query")
                self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
            else:
                LOGGER.info("didn't find query")
                self.sb.attach_allure_screenshot("didn't find query screenshot")
                raise Exception("didn't find query " + query_name)
            self.sb.attach_allure_screenshot("find query done screenshot")

            # wait result
            self.sb.wait_for_element_clickable(GSTHomeLocators.run_query_button, timeout=clickTimeout)
            self.sb.click(GSTHomeLocators.run_query_button, timeout=clickTimeout)
            self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
            self.sb.attach_allure_screenshot("run query with multi edge done screenshot")

            # assert result
            self.sb.wait_for_element_clickable(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
            self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
            queryList = self.sb.find_elements(GSTHomeLocators.assert_table_view_result_div)
            LOGGER.info("query list =" + str(len(queryList)))
            if len(queryList) == 0:
                self.sb.attach_allure_screenshot("assert table result failed screenshot")
                raise Exception("table view result failed exception")
            self.sb.find_elements(GSTHomeLocators.table_view_results_button)[1].click()
            self.sb.sleep(2)
            self.sb.attach_allure_screenshot("assert table result screenshot")


    def uploadLocalFiles(self, graphName="MyGraph", filePath="./data/localFiles/"):
        gsw = GraphStudioWindows(self.sb)
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=wait_render_time)
        # click myGraph button
        self.switch_to_graph(graphName)
        self.sb.click(GSTHomeLocators.nav_load_data, timeout=clickTimeout)

        # turn to add data source
        self.sb.wait_for_element_clickable(GSTHomeLocators.publish_data_mapping_button, timeout=wait_render_time)
        self.sb.attach_allure_screenshot("opened map data menu screenshot")
        self.sb.click_nth_visible_element(GSTHomeLocators.toolbars,6, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.dataSourceButtons,1, timeout=clickTimeout)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)

        # set up environment
        self.sb.attach_allure_screenshot("opened add local file screenshot")
        elements = self.sb.find_elements(GSTHomeLocators.deleteIcons)
        LOGGER.info("deleteIcons lenth:" + str(len(elements)))
        if len(elements) > 0:
            for i in range(len(elements)):
                LOGGER.info("currentIndex: " + str(i))
                self.sb.find_elements(GSTHomeLocators.deleteIcons)[0].click()
                gsw.getDialogDeleteButton().click()
                self.sb.sleep(1)

        # upload local files
        LOGGER.info(listdir(filePath))
        for f in listdir(filePath):
            path = os.path.join(filePath, f)
            # skip directories
            if os.path.isdir(path):
                continue
            LOGGER.info(f)
            self.sb.choose_file('input[type="file"]', path)
            self.sb.sleep(2)
            # improve the success rate
            try:
                self.sb.wait_for_element_absent(GSTHomeLocators.progressBar, timeout=300)
                title = gsw.getPopWindowContent()
                LOGGER.info("pop window title:"+title)
                self.sb.assertIn("successfully", title, msg=None)
            except Exception as e:
                LOGGER.info(str(e))
        self.sb.attach_allure_screenshot("uploaded successfully screenshot")
        elements = self.sb.find_elements(GSTHomeLocators.deleteIcons)
        LOGGER.info("upload files number:" + str(len(elements)))
        self.sb.assert_not_equal(0, len(elements))
        gsw.getDialogBackButton().click()
        gsw.getDialogCancelButton().click()

    @decorators.retry_on_exception(tries=2, delay=1, backoff=2, max_delay=5)
    def get_tg_version(self):
        version_text = self.sb.get_text(GSTHomeLocators.cloud_version_new_header, timeout=wait_render_time)
        LOGGER.info(version_text)
        if "version" not in version_text:
            raise Exception("version not in version_text")
        str_tmp = version_text.strip('"')
        LOGGER.info(str_tmp)
        match = re.search(r"\d+\.\d+\.\d+", str_tmp)  # find version number
        LOGGER.info(match)
        is_three_version = True
        if match:
            version_nums = match.group().split(".")
            LOGGER.info(version_nums)
            version = version_nums[0] + version_nums[1] + version_nums[2]
            if version_nums[0] != "3":
                is_three_version = False
                LOGGER.info("is not V3 serious, " + version_nums[0] + ", is_three_version: " + str(is_three_version))
            LOGGER.info("splice version: " + version)
            return version, is_three_version
        
    def get_solution_file(self):
        loginUtil = LoginUtils(self.sb)
        cloud_env_flag = loginUtil.is_oncloud()
        solution_filename = "covid19_tigergraph.tar.gz"
        if cloud_env_flag:
            try:
                version_tmp, is_three_version = self.get_tg_version()
                LOGGER.info("response version:" + version_tmp + ", is_three_version: " + str(is_three_version))
                if version_tmp and version_tmp.strip():
                    if is_three_version and int(version_tmp) < 3100:
                        solution_filename = "covid19.tar.gz"
                        LOGGER.info("Current version < 3.10.0")
                    else:
                        solution_filename = "covid19_cloud.tar.gz"
                        LOGGER.info("Current version >= 3.10.0")
            except Exception as e:
                LOGGER.info(str(e))
        return solution_filename


    def load_data(self, graphName = "MyGraph", checkResult = True):
        if graphName == "":
            graphName = self.graphName

        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=clickTimeout)
        # click myGraph button
        self.switch_to_graph(graphName)
        self.wait_and_click(GSTHomeLocators.nav_load_data)
        self.wait_progress_bar()

        self.sb.attach_allure_screenshot("opened load data menu screenshot")
        self.wait_and_click(GSTLoadDataLocators.start_resume_load_btn)
        self.wait_and_click(DialogLocators.continue_btn)
        self.wait_progress_bar()
        self.sb.attach_allure_screenshot("started to load screenshot")
        loginUtil = LoginUtils(self.sb)
        cloud_env_flag = loginUtil.is_oncloud()
        version_more_than_391 = True
        if cloud_env_flag:
            try:
                version_tmp, is_three_version = self.get_tg_version()
                LOGGER.info(version_tmp)
                if version_tmp and version_tmp.strip():
                    if is_three_version and int(version_tmp) < 392:
                        version_more_than_391 = False
                        LOGGER.info("Current version < 392")
            except Exception as e:
                LOGGER.info(str(e))

        for i in range(200):
            LOGGER.info(i)
            self.sb.sleep(1)
            # if not version_more_than_391 and graphName == "MyMultiEdge":
            #     LOGGER.info("version < 392, known issue, skip wait MyMultiEdge result")
            #     break
            if not self.sb.is_element_clickable(GSTHomeLocators.pauseButton, by="css selector"):
                LOGGER.info('pause button not clickable')
                if graphName == "MyGraph" and self.sb.is_text_visible("33,900"):
                    LOGGER.info('Pre_check MyGraph vertex and edge lines, break')
                    break
                # double check loading finished
                self.sb.wait_for_element_clickable(GSTLoadDataLocators.show_left_chart_visual_info, timeout=clickTimeout)
                self.sb.click(GSTLoadDataLocators.show_left_chart_visual_info)
                if self.sb.is_text_visible("Status: RUNNING"):
                    LOGGER.info("still has job Status: RUNNING")
                elif self.sb.is_text_visible("Status: PAUSED"):
                    LOGGER.info("still has job Status: PAUSED")
                elif self.sb.is_text_visible("Status: STOPPED"):
                    LOGGER.info("still has job Status: STOPPED")
                elif self.sb.is_text_visible("Status: NOT STARTED"):
                    LOGGER.info("still has job Status: NOT STARTED")
                elif self.sb.is_text_visible("Status: Data not mapped"):
                    LOGGER.info("still has job Status: Data not mapped")
                else:
                    LOGGER.info("Statistics update OK")
                    if self.sb.is_element_visible(GSTLoadDataLocators.close_left_chart_visual_info):
                        LOGGER.info("Statistics update OK, close_left_chart_visual_info ")
                        self.wait_and_click(GSTLoadDataLocators.close_left_chart_visual_info)
                    break
                if self.sb.is_element_visible(GSTLoadDataLocators.close_left_chart_visual_info):
                    LOGGER.info("Statistics not ready, close_left_chart_visual_info ")
                    self.wait_and_click(GSTLoadDataLocators.close_left_chart_visual_info)
            # add loading status assertion
            elif i == 199 and self.sb.is_element_clickable(GSTHomeLocators.pauseButton, by="css selector"):
                self.sb.attach_allure_screenshot("loading jobs status not update to finished in 200 seconds screenshot")
                # to do assert loading jobs status
                if version_more_than_391:
                    raise Exception("loading jobs status not update to finished in 200 seconds")

        # assert total vertex and edge
        self.sb.attach_allure_screenshot("finished loading screenshot")
        if checkResult:
            if graphName == self.graphName:
                LOGGER.info('assert MyGraph vertex and edge lines')
                self.sb.assert_text("33,900", timeout=wait_render_time)  # to assert firstly
                self.verify_load_data("33,900", "49,770")
            else:
                LOGGER.info('assert MyMultiEdge vertex and edge lines')
                # to do assert loading jobs result after fix
                if version_more_than_391:
                    self.sb.assert_text("19,499", timeout=wait_render_time)
                    self.verify_load_data("19,499", "10,835")
                else:
                    LOGGER.info("version < 392, known issue, wait max 20 minutes assert MyMultiEdge result")
                    self.sb.assert_text("19,499", timeout=1200)
                    self.verify_load_data("19,499", "10,835")

    def open_user_info(self):
        Browser = os.getenv("Browser", "")
        # Browser = "firefox"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            self.sb.refresh()
        self.sb.wait_for_element_clickable(GSTHomeLocators.userIcon, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.userIcon)

    def headerInEveryTools_GST(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)

        self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        # assert login user account name
        self.sb.assert_text("tigergraph", GSTHomeLocators.userNameSpan,timeout=assertTextTimeout)
        self.sb.sleep(1)
        self.sb.assert_text("English", GSTHomeLocators.languageSpan,timeout=assertTextTimeout)
        LOGGER.info(self.sb.get_text(GSTHomeLocators.languageSpan))
        self.sb.hover_on_element(GSTHomeLocators.languageButton)
        # switch to chinese
        self.sb.wait_for_element_clickable(GSTHomeLocators.chineseButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.chineseButton)
        self.sb.sleep(3)
        self.sb.assert_text("主页",timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("switched to chinese screenshot")
        self.sb.sleep(1)

        # switch to japanese
        self.open_user_info()
        self.sb.assert_text("简体中文",GSTHomeLocators.languageSpan,timeout=assertTextTimeout)
        LOGGER.info(self.sb.get_text(GSTHomeLocators.languageSpan))
        self.sb.hover_on_element(GSTHomeLocators.languageButton)
        self.sb.wait_for_element_clickable(GSTHomeLocators.japaneseButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.japaneseButton)
        self.sb.sleep(3)
        self.sb.assert_text("メインページ",timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("switched to japanese screenshot")
        self.sb.sleep(1)

        # switch to english
        self.open_user_info()
        self.sb.assert_text("日本語", GSTHomeLocators.languageSpan,timeout=assertTextTimeout)
        LOGGER.info(self.sb.get_text(GSTHomeLocators.languageSpan))
        self.sb.hover_on_element(GSTHomeLocators.languageButton)
        self.sb.wait_for_element_clickable(GSTHomeLocators.englishButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.englishButton)
        self.sb.sleep(3)
        self.sb.assert_text("Home",timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("switched to english screenshot")
        self.sb.sleep(1)

        # assert license valid
        self.open_user_info()
        self.sb.wait_for_element_clickable(GSTHomeLocators.licenseButton, timeout=clickTimeout)
        self.sb.hover_on_element(GSTHomeLocators.licenseButton)
        self.sb.assert_text("Valid", timeout=assertTextTimeout)
        self.sb.sleep(1)

        # hide temporary
        # # assert theme
        # LOGGER.info("GST dark theme "+self.sb.get_text(GSTHomeLocators.themeSpan))
        # if "Off" in self.sb.get_text(GSTHomeLocators.themeSpan):
        #     self.sb.assert_text("Off", GSTHomeLocators.themeSpan,timeout=assertTextTimeout)
        #     self.sb.attach_allure_screenshot("dark theme off screenshot")
        #     self.sb.click(GSTHomeLocators.switchThemeButton)
        #     self.sb.sleep(1)
        #     self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        #     self.sb.assert_text("On", GSTHomeLocators.themeSpan,timeout=assertTextTimeout)
        #     self.sb.attach_allure_screenshot("dark theme on screenshot")
        # elif "On" in self.sb.get_text(GSTHomeLocators.themeSpan):
        #     self.sb.assert_text("On", GSTHomeLocators.themeSpan, timeout=assertTextTimeout)
        #     self.sb.attach_allure_screenshot("dark theme on screenshot")
        #     self.sb.click(GSTHomeLocators.switchThemeButton)
        #     self.sb.sleep(1)
        #     self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        #     self.sb.assert_text("Off", GSTHomeLocators.themeSpan, timeout=assertTextTimeout)
        #     self.sb.attach_allure_screenshot("dark theme off screenshot")

        # assert help page
        self.sb.sleep(1)
        # self.sb.click(GSTHomeLocators.userIcon)
        self.sb.click(GSTHomeLocators.GSTHelpButton, timeout=clickTimeout)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        all_handles = self.sb.driver.window_handles
        LOGGER.info(all_handles)
        self.sb.switch_to_window(all_handles[1], timeout=None)
        self.sb.attach_allure_screenshot("GST help page screenshot")
        self.sb.assert_text("TigerGraph GraphStudio", timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()

        # assert third party notice
        self.go_to_third_party_notice_page()
        all_handles = self.sb.driver.window_handles
        LOGGER.info(all_handles)
        self.sb.switch_to_window(all_handles[1], timeout=None)
        self.sb.attach_allure_screenshot("third party notice page screenshot")
        self.sb.assert_text("GraphStudio Patent and Third Party Notice", timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()


    def go_to_third_party_notice_page(self):
        self.sb.sleep(1)
        if self.sb.is_element_present(GSTHomeLocators.thirdPartyNoticeButton):
            LOGGER.info("thirdPartyNoticeButton existed")
            self.sb.attach_allure_screenshot("third party notice button existed screenshot")
        else:
            self.sb.wait_for_element_clickable(GSTHomeLocators.userIcon, timeout=clickTimeout)
            self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.thirdPartyNoticeButton, timeout=clickTimeout)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)

    def headerInEveryTools_GAP(self):
        self.sb.wait_for_element_visible(GSTHomeLocators.nav_home, timeout=60)
        self.sb.wait_for_element_clickable(GSTHomeLocators.toolsIcon, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.toolsIcon, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.adminIcon, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.adminIcon, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.userIcon, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        # assert login user account name
        self.sb.assert_text("tigergraph", GSTHomeLocators.userNameSpan,timeout=assertTextTimeout)
        self.sb.assert_text("English", GSTHomeLocators.languageSpan,timeout=assertTextTimeout)
        LOGGER.info(self.sb.get_text(GSTHomeLocators.languageSpan))
        self.sb.wait_for_element_clickable(GSTHomeLocators.languageButton, timeout=clickTimeout)
        self.sb.hover_on_element(GSTHomeLocators.languageButton, timeout=clickTimeout)
        # switch to chinese
        self.sb.wait_for_element_clickable(GSTHomeLocators.chineseButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.chineseButton, timeout=clickTimeout)
        self.sb.sleep(3)
        self.sb.assert_text("数据面板",timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("switched to chinese screenshot")

        # switch to japanese
        self.open_user_info()
        self.sb.assert_text("简体中文",GSTHomeLocators.languageSpan,timeout=assertTextTimeout)
        LOGGER.info(self.sb.get_text(GSTHomeLocators.languageSpan))
        self.sb.wait_for_element_clickable(GSTHomeLocators.languageButton, timeout=clickTimeout)
        self.sb.hover_on_element(GSTHomeLocators.languageButton, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.japaneseButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.japaneseButton, timeout=clickTimeout)
        self.sb.sleep(3)
        self.sb.assert_text("ダッシュボード",timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("switched to japanese screenshot")

        # switch to english
        self.open_user_info()
        self.sb.assert_text("日本語", GSTHomeLocators.languageSpan,timeout=assertTextTimeout)
        LOGGER.info(self.sb.get_text(GSTHomeLocators.languageSpan))
        self.sb.wait_for_element_clickable(GSTHomeLocators.languageButton, timeout=clickTimeout)
        self.sb.hover_on_element(GSTHomeLocators.languageButton)
        self.sb.wait_for_element_clickable(GSTHomeLocators.englishButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.englishButton)
        self.sb.sleep(3)
        self.sb.assert_text("Dashboard",timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("switched to english screenshot")
        self.sb.sleep(1)

        # hide temporary
        # # assert theme
        # self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        # LOGGER.info("GAP dark theme " + self.sb.get_text(GSTHomeLocators.themeSpan))
        # if "Off" in self.sb.get_text(GSTHomeLocators.themeSpan):
        #     self.sb.assert_text("Off", GSTHomeLocators.themeSpan, timeout=assertTextTimeout)
        #     self.sb.attach_allure_screenshot("dark theme off screenshot")
        #     self.sb.click(GSTHomeLocators.switchThemeButton)
        #     self.sb.sleep(1)
        #     self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        #     self.sb.assert_text("On", GSTHomeLocators.themeSpan, timeout=assertTextTimeout)
        #     self.sb.attach_allure_screenshot("dark theme on screenshot")
        # elif "On" in self.sb.get_text(GSTHomeLocators.themeSpan):
        #     self.sb.assert_text("On", GSTHomeLocators.themeSpan, timeout=assertTextTimeout)
        #     self.sb.attach_allure_screenshot("dark theme on screenshot")
        #     self.sb.click(GSTHomeLocators.switchThemeButton)
        #     self.sb.sleep(1)
        #     self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        #     self.sb.assert_text("Off", GSTHomeLocators.themeSpan, timeout=assertTextTimeout)
        #     self.sb.attach_allure_screenshot("dark theme off screenshot")

        # assert help page
        # self.sb.sleep(1)
        self.sb.wait_for_element_clickable(GSTHomeLocators.userIcon, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.GAPHelpButton, timeout=clickTimeout)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        # get all window handles
        myAllHandles = self.sb.driver.window_handles
        LOGGER.info(myAllHandles)
        self.sb.switch_to_window(myAllHandles[1], timeout=None)
        self.sb.attach_allure_screenshot("GAP help page screenshot")
        self.sb.assert_text("The TigerGraph Admin Portal is a browser-based DevOps tool",timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()

        # assert third party notice
        self.go_to_third_party_notice_page()
        all_handles = self.sb.driver.window_handles
        LOGGER.info(all_handles)
        self.sb.switch_to_window(all_handles[1], timeout=None)
        self.sb.attach_allure_screenshot("third party notice page screenshot")
        self.sb.assert_text("GraphStudio Patent and Third Party Notice",timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()


    def headerInEveryTools_Insights(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)

        self.sb.click(GSTHomeLocators.toolsIcon, timeout=clickTimeout)
        self.sb.sleep(1)
        self.sb.click(GSTHomeLocators.insightsIcon, timeout=clickTimeout)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        self.sb.click(GSTHomeLocators.userIcon, timeout=clickTimeout)
        # assert login user account name
        self.sb.assert_text("tigergraph", GSTHomeLocators.userNameSpan,timeout=assertTextTimeout)
        self.sb.assert_text("Log out",timeout=assertTextTimeout)

        # assert help page
        self.sb.sleep(1)
        # self.sb.click(GSTHomeLocators.userIcon)
        self.sb.click(GSTHomeLocators.insightsHelpButton)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        # get all window handles
        myAllHandles = self.sb.driver.window_handles
        LOGGER.info(myAllHandles)
        self.sb.switch_to_window(myAllHandles[1], timeout=None)
        self.sb.attach_allure_screenshot("insights help page screenshot")
        self.sb.assert_text("TigerGraph Insights is a no-code visual graph analyzer",timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()

        # assert third party notice
        self.go_to_third_party_notice_page()
        all_handles = self.sb.driver.window_handles
        LOGGER.info(all_handles)
        self.sb.switch_to_window(all_handles[1], timeout=None)
        self.sb.attach_allure_screenshot("third party notice page screenshot")
        self.sb.assert_text("GraphStudio Patent and Third Party Notice",timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()

    def headerInEveryTools_GSQL(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)

        self.sb.click(GSTHomeLocators.toolsIcon)
        self.sb.sleep(1)
        self.sb.click(GSTHomeLocators.gshellIcon)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        self.sb.click(GSTHomeLocators.userIcon)
        # assert login user account name
        self.sb.assert_text("tigergraph", GSTHomeLocators.userNameSpan,timeout=assertTextTimeout)
        self.sb.assert_text("Log out",timeout=assertTextTimeout)

        # assert help page
        self.sb.sleep(1)
        # self.sb.click(GSTHomeLocators.userIcon)
        self.sb.click(GSTHomeLocators.gShellHelpButton)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        # get all window handles
        myAllHandles = self.sb.driver.window_handles
        LOGGER.info(myAllHandles)
        self.sb.switch_to_window(myAllHandles[1], timeout=None)
        self.sb.attach_allure_screenshot("GSQL help page screenshot")
        self.sb.assert_text("TigerGraph provides the GSQL shell",timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()

        # assert third party notice
        self.go_to_third_party_notice_page()
        all_handles = self.sb.driver.window_handles
        LOGGER.info(all_handles)
        self.sb.switch_to_window(all_handles[1], timeout=None)
        self.sb.attach_allure_screenshot("third party notice page screenshot")
        self.sb.assert_text("GraphStudio Patent and Third Party Notice",timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()


    def headerInEveryTools_GraphQL(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)

        self.wait_and_click(GSTHomeLocators.toolsIcon)
        self.sb.sleep(1)
        self.wait_and_click(GSTHomeLocators.graphQLIcon)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        self.wait_and_click(GSTHomeLocators.userIcon)
        # assert login user account name
        self.sb.assert_text("tigergraph", GSTHomeLocators.userNameSpan,timeout=assertTextTimeout)
        self.sb.assert_text("Log out",timeout=assertTextTimeout)

        # assert help page
        self.sb.sleep(1)
        # self.wait_and_click(GSTHomeLocators.userIcon)
        self.wait_and_click(GSTHomeLocators.graphQLHelpButton)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        # get all window handles
        myAllHandles = self.sb.driver.window_handles
        LOGGER.info(myAllHandles)
        self.sb.switch_to_window(myAllHandles[1], timeout=None)
        self.sb.attach_allure_screenshot("GraphQL help page screenshot")
        self.sb.assert_text("TigerGraph GraphQL Service",timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()

        # assert third party notice
        self.go_to_third_party_notice_page()
        all_handles = self.sb.driver.window_handles
        LOGGER.info(all_handles)
        self.sb.switch_to_window(all_handles[1], timeout=None)
        self.sb.attach_allure_screenshot("third party notice page screenshot")
        self.sb.assert_text("GraphStudio Patent and Third Party Notice",timeout=assertTextTimeout)
        self.sb.driver.close()
        self.sb.sleep(1)
        self.sb.switch_to_default_window()

    def RDBMS_MySQL(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)

        # init environmnet, if mysql_yuling graph existed, delete
        self.wait_and_click(GSTHomeLocators.globalGraphButton)
        gsw = GraphStudioWindows(self.sb)
        graphList = self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)
        buttonList = self.sb.find_elements(GSTHomeLocators.getAllDeleteButtonList)
        LOGGER.info("graphlist="+str(len(graphList))+", deleteButton="+str(len(buttonList)))
        LOGGER.info("graphlist="+str(graphList)+", deleteButton="+str(buttonList))
        for i in range(len(graphList)):
            LOGGER.info(i)
            LOGGER.info(self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)[i].text.strip())
            if "mysql_yuling" in self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)[i].text.strip():
                self.sb.find_elements(GSTHomeLocators.getAllDeleteButtonList)[i-1].click()
                gsw.getDialogContinueButton().click()
                self.sb.attach_allure_screenshot("delete graph result screenshot")
                self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
                break

        # select mysql
        self.sb.wait_for_element_clickable(GSTHomeLocators.migrateDatabaseIcon, timeout=clickTimeout)
        self.wait_and_click(GSTHomeLocators.migrateDatabaseIcon)
        self.wait_and_click(GSTHomeLocators.mySQLbutton)
        self.wait_and_click(GSTHomeLocators.nextButtonCommon)

        # input connection info
        self.sb.sleep(3)
        self.sb.type(GSTHomeLocators.rdbmsServer,"**************")
        self.sb.type(GSTHomeLocators.rdbmsPort,"30008")
        self.sb.type(GSTHomeLocators.rdbmsDatabase,"yuling")
        self.sb.type(GSTHomeLocators.rdbmsUsername,"root")
        self.sb.type(GSTHomeLocators.rdbmsPassword,"123456")
        self.wait_and_click(GSTHomeLocators.nextButtonCommon)
        self.sb.attach_allure_screenshot("input connection info screenshot")

        # choose tables
        self.sb.sleep(3)
        self.sb.attach_allure_screenshot("choose tables screenshot")
        self.wait_horizon_progress_bar(wait_present_time=short_clickTimeout, wait_not_visible_time=wait_render_time)
        self.wait_and_click(GSTHomeLocators.nextButtonCommon)

        # configuration
        self.sb.attach_allure_screenshot("configuration screenshot")
        self.wait_horizon_progress_bar(wait_present_time=short_clickTimeout, wait_not_visible_time=wait_render_time)
        self.wait_and_click(GSTHomeLocators.migrateButton)

        # wait progressbar disappear
        self.wait_horizon_progress_bar(wait_present_time=short_clickTimeout, wait_not_visible_time=300)
        self.sb.attach_allure_screenshot("begin to migrate screenshot")
        self.sb.wait_for_element_visible(GSTHomeLocators.verifyButton, timeout=300)
        self.wait_and_click(GSTHomeLocators.verifyButton)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        self.sb.refresh()

        # load data
        self.load_data(graphName="mysql_yuling", checkResult=False)

        # assert total vertex
        self.sb.attach_allure_screenshot("finished loading screenshot")
        # skip assert the number until fixed the duplicate loading (https://graphsql.atlassian.net/browse/GLE-5409)
        self.sb.assert_text(text="3", selector=GSTHomeLocators.statistics_result, timeout=assertTextTimeout)  # assert firstly
        LOGGER.info("first assert_text done")
        self.verify_load_data("3", "0")
        self.sb.attach_allure_screenshot("assert vertex pass result screenshot")

    def RDBMS_PostgreSQL(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)

        # init environmnet, if mysql_yuling graph existed, delete
        self.wait_and_click(GSTHomeLocators.globalGraphButton)
        gsw = GraphStudioWindows(self.sb)
        graphList = self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)
        buttonList = self.sb.find_elements(GSTHomeLocators.getAllDeleteButtonList)
        LOGGER.info("graphlist="+str(len(graphList))+", deleteButton="+str(len(buttonList)))
        LOGGER.info("graphlist="+str(graphList)+", deleteButton="+str(buttonList))
        for i in range(len(graphList)):
            LOGGER.info(i)
            LOGGER.info(self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)[i].text.strip())
            if "postgresql_yuling" in self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)[i].text.strip():
                self.sb.find_elements(GSTHomeLocators.getAllDeleteButtonList)[i-1].click()
                gsw.getDialogContinueButton().click()
                self.sb.attach_allure_screenshot("delete graph result screenshot")
                self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
                break

        # select mysql
        self.wait_and_click(GSTHomeLocators.migrateDatabaseIcon)
        self.wait_and_click(GSTHomeLocators.postgreSQLbutton)
        self.wait_and_click(GSTHomeLocators.nextButtonCommon)

        # input connection info
        self.sb.sleep(3)
        self.sb.type(GSTHomeLocators.rdbmsServer,"**************")
        self.sb.type(GSTHomeLocators.rdbmsPort,"9092")
        self.sb.type(GSTHomeLocators.rdbmsDatabase,"yuling")
        self.sb.type(GSTHomeLocators.rdbmsUsername,"postgres")
        self.sb.type(GSTHomeLocators.rdbmsPassword,"123456")
        self.wait_and_click(GSTHomeLocators.nextButtonCommon)
        self.sb.attach_allure_screenshot("input connection info screenshot")

        # choose tables
        self.sb.sleep(3)
        self.sb.attach_allure_screenshot("choose tables screenshot")
        self.wait_horizon_progress_bar(wait_present_time=short_clickTimeout, wait_not_visible_time=wait_render_time)
        self.wait_and_click(GSTHomeLocators.nextButtonCommon)

        # configuration
        self.sb.attach_allure_screenshot("configuration screenshot")
        self.wait_horizon_progress_bar(wait_present_time=short_clickTimeout, wait_not_visible_time=wait_render_time)
        self.wait_and_click(GSTHomeLocators.migrateButton)

        # wait progressbar disappear
        self.wait_horizon_progress_bar(wait_present_time=short_clickTimeout, wait_not_visible_time=300)
        self.sb.attach_allure_screenshot("begin to migrate screenshot")
        self.sb.wait_for_element_visible(GSTHomeLocators.verifyButton, timeout=300)
        self.wait_and_click(GSTHomeLocators.verifyButton)
        self.sb.wait_for_ready_state_complete()
        self.sb.sleep(3)
        self.sb.refresh()

        # load data
        self.load_data(graphName="postgresql_yuling", checkResult=False)

        # assert total vertex
        self.sb.attach_allure_screenshot("finished loading screenshot")
        self.sb.assert_text(text="3", selector=GSTHomeLocators.statistics_result, timeout=assertTextTimeout)  # assert firstly
        LOGGER.info("first assert_text done")
        self.verify_load_data("3", "0")
        self.sb.attach_allure_screenshot("assert vertex pass result screenshot")

    def wait_and_click(self, css):
        LOGGER.info("wait and click: " + css)
        self.sb.wait_for_element_clickable(css, timeout=clickTimeout)
        self.sb.click(css, timeout=clickTimeout)

    def action_page_check(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        # turn to global view
        if self.sb.is_text_visible("Global View"):
            LOGGER.info("now is global view graph")
        else:
            self.wait_and_click(GSTHomeLocators.globalGraphButton)
            graphList = self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)
            LOGGER.info("graphlist="+str(len(graphList)))
            for i in range(len(graphList)):
                LOGGER.info(i)
                LOGGER.info(self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)[i].text.strip())
                if "Global View" in self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)[i].text.strip():
                    self.sb.find_elements(GSTHomeLocators.getAllGraphNameList)[i].click()
                    self.sb.attach_allure_screenshot("switch global view result screenshot")
                    break

        # switch to action page
        self.sb.wait_for_element_clickable(GSTHomeLocators.nav_actions, timeout=clickTimeout)
        self.wait_and_click(GSTHomeLocators.nav_actions)

        # check clear all graph data
        self.sb.wait_for_element_clickable(GSTHomeLocators.clear_all_graph_button, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.clear_all_graph_button, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.input_clear_all_graph, "clear all graph data", timeout=clickTimeout)
        # assert button clickable
        if not self.sb.is_element_clickable(GSTHomeLocators.dialog_delete_permanently_button):
            LOGGER.info("dialog_delete_permanently_button not clickable")
            self.sb.attach_allure_screenshot("dialog_delete_permanently_button not clickable screenshot")
            raise Exception("dialog_delete_permanently_button not clickable")
        self.sb.attach_allure_screenshot("check clear all graph data done screenshot")
        self.sb.wait_for_element_clickable(GSTHomeLocators.cancel_delete_permanently_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.cancel_delete_permanently_button, timeout=clickTimeout)

        # check rebuild and assert button clickable
        if not self.sb.is_element_clickable(GSTHomeLocators.rebuild_button):
            LOGGER.info("rebuild_button not clickable")
            self.sb.attach_allure_screenshot("rebuild_button not clickable screenshot")
            raise Exception("rebuild_button not clickable")
        else:
            LOGGER.info("rebuild_button clickable")
        self.sb.attach_allure_screenshot("rebuild_button clickable screenshot")

    def export_solution_check(self, all_graph=True, single_graph=True, with_data=True):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        # turn to global view
        if self.sb.is_text_visible("Global View"):
            LOGGER.info("now is global view graph")
        else:
            self.sb.click(GSTHomeLocators.nav_home)

        # export current solution
        self.sb.wait_for_element_clickable(GSTHomeLocators.export_solution_btn, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.export_solution_btn)

        # select the export parameter
        self.sb.wait_for_element_visible(GSTHomeLocators.export_solution_dialog, timeout=clickTimeout)
        # checkbox 1:data, 2:all,  3:MyGraph
        if all_graph:
            LOGGER.info("select all graph to export")
            self.sb.wait_for_element_clickable(GSTHomeLocators.export_solution_checkbox_on_dialog, timeout=clickTimeout)
            self.sb.find_elements(GSTHomeLocators.export_solution_checkbox_on_dialog)[2].click()
        elif single_graph:
            LOGGER.info("select single graph to export")
            self.sb.wait_for_element_clickable(GSTHomeLocators.export_solution_checkbox_on_dialog, timeout=clickTimeout)
            self.sb.find_elements(GSTHomeLocators.export_solution_checkbox_on_dialog)[3].click()
        else:
            LOGGER.info("select no graph to export")
        self.sb.attach_allure_screenshot("select graph to export screenshot")

        if with_data:
            LOGGER.info("select data to export")
            self.sb.wait_for_element_clickable(GSTHomeLocators.export_solution_checkbox_on_dialog, timeout=clickTimeout)
            self.sb.find_elements(GSTHomeLocators.export_solution_checkbox_on_dialog)[1].click()
        else:
            LOGGER.info("select no data to export")
        self.sb.attach_allure_screenshot("select data to export screenshot")

        # assert the download files
        Browser = os.getenv("Browser", "")
        # Browser = "edge"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            download_path = self.sb.get_downloads_folder()
        else:
            download_path = self.sb.get_downloads_folder()
            # download_path = "/tmp/GST"

        file_name = "export_"
        LOGGER.info("download file_name:" + file_name)
        gap = GAPPage()
        gap.setChromeDownloadPath(self.sb, download_path)
        gap.delete_files_under_chrome_download_path(path=download_path, fileName=file_name, contain_file_name=True)
        self.sb.wait_for_element_clickable(GSTHomeLocators.export_solution_confirm_btn, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.export_solution_confirm_btn, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("begin to export solution screenshot")

        # Check download file and file size
        download_res, download_file = gap.judge_file_exist(18, download_path, file_name, "gz", 10, False)
        LOGGER.info("download_res: " + str(download_res))
        LOGGER.info("download_file: " + download_file)
        self.sb.attach_allure_screenshot("Download current file result")
        if not download_res:
            download_res, download_file = gap.judge_file_exist(5, download_path, file_name, "gz", 3, False)
            LOGGER.info("not download_res: " + str(download_res))
            LOGGER.info("not download_file: " + download_file)
            raise Exception("download file not found")
        else:
            file_size = os.path.getsize("{}/{}".format(download_path, download_file))
            LOGGER.info("file_size: " + str(file_size))
            self.sb.assert_true(file_size > 10)

    def local_admin_privillege(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_actions, timeout=60)
        self.switch_to_graph(self.graphName)
        gsw = GraphStudioWindows(self.sb)
        # assert the username is globaldesigner
        # self.sb.click(GSTHomeLocators.userIcon)
        self.sb.execute_script(GSTHomeLocators.tools_header_icon_JS)
        self.sb.sleep(2)
        # assert login user account name and role
        self.sb.assert_text("u_localadmin", timeout=assertTextTimeout)
        # assert superuser
        self.sb.assert_text("admin", GSTHomeLocators.userRole, timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("check u_localadmin screenshot")

        # operate in design schema
        self.sb.click(GSTHomeLocators.nav_design_schema)
        self.sb.wait_for_element_clickable(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.publish_schema_button)
        title = gsw.getPopWindowContent()
        LOGGER.info("pop window title:" + title)
        self.sb.assert_equal("Graph schema style is updatedDISMISS", title, msg=None)
        self.sb.attach_allure_screenshot("published schema screenshot")

        # operate in Expore graph
        self.sb.wait_for_element_clickable(GSTHomeLocators.nav_explore_graph, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.nav_explore_graph, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.pick_vertices_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.pick_vertices_button, timeout=clickTimeout)
        self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        self.sb.attach_allure_screenshot("Expore graph pick vertices screenshot")

    def global_designer_privillege(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_actions, timeout=60)
        self.switch_to_graph(self.graphName)
        gsw = GraphStudioWindows(self.sb)
        # assert the username is globaldesigner
        # self.sb.click(GSTHomeLocators.userIcon)
        self.sb.execute_script(GSTHomeLocators.tools_header_icon_JS)
        self.sb.sleep(2)
        # assert login user account name and role
        # self.sb.assert_text("u_globaldesigner", GSTHomeLocators.userNameSpan, timeout=assertTextTimeout)
        self.sb.assert_text("u_globaldesigner", timeout=assertTextTimeout)
        # assert superuser
        self.sb.assert_text("global...signer", GSTHomeLocators.userRole, timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("check globaldesigner screenshot")

        # operate in design schema
        self.sb.click(GSTHomeLocators.nav_design_schema)
        self.sb.wait_for_element_clickable(GSTHomeLocators.publish_schema_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.publish_schema_button)
        title = gsw.getPopWindowContent()
        LOGGER.info("pop window title:" + title)
        self.sb.assert_equal("Graph schema style is updatedDISMISS", title, msg=None)
        self.sb.attach_allure_screenshot("published schema screenshot")

        # operate in Expore graph
        self.sb.wait_for_element_clickable(GSTHomeLocators.nav_explore_graph, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.nav_explore_graph, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.pick_vertices_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.pick_vertices_button, timeout=clickTimeout)
        self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        self.sb.attach_allure_screenshot("Expore graph pick vertices screenshot")


    def discardDraft(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)
        # turn to write queries
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_write_queries, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.downloadQuery, timeout=clickTimeout)
        self.delete_exist_QueryAndDraft(query_name="discardDraft")
        self.sb.attach_allure_screenshot("delete query done screenshot")

        # create new gsql query
        self.sb.wait_for_element_clickable(GSTHomeLocators.createNewQueryButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.createNewQueryButton, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.inputNewQueryName, "discardDraft", timeout=clickTimeout)
        self.sb.click(DialogLocators.create_btn, timeout=clickTimeout)

        # select the query and discard
        self.sb.wait_for_text_not_visible("No results found", timeout=clickTimeout)
        self.sb.sleep(1)
        self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
        self.sb.attach_allure_screenshot("create query done screenshot")
        self.sb.click(GSTHomeLocators.discardDraftButton, timeout=clickTimeout)
        self.sb.click(DialogLocators.continue_btn, timeout=clickTimeout)
        # content = gsw.getPopWindowContent()
        # LOGGER.info("discard draft result:" + content)
        # self.sb.assert_equal('The draft of "discardDraft" is deletedDISMISS', content)
        self.sb.attach_allure_screenshot("discard draft done screenshot")
        self.wait_progress_bar()
        self.sb.assert_text("No results found", timeout=assertTextTimeout)
        queryList = self.sb.find_elements(GSTHomeLocators.queryList)
        self.sb.assert_equal(0, len(queryList))


    """ wait the circle progress bar """
    def wait_progress_bar(self, wait_present_time=30, wait_not_visible_time=30):
        try:
            self.sb.wait_for_element_present(GSTHomeLocators.circle_progressBar, timeout=wait_present_time)
            LOGGER.info("wait_progress_bar present PASS")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_visible(GSTHomeLocators.circle_progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_progress_bar not visible PASS")
        except Exception as e:
            LOGGER.info("wait_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_progress_bar exception screenshot")

    """ wait the horizon progress bar """
    def wait_horizon_progress_bar(self, wait_present_time=30, wait_not_visible_time=30):
        try:
            self.sb.wait_for_element_present(GSTHomeLocators.progressBar, timeout=wait_present_time)
            LOGGER.info("wait_horizon_progress_bar present PASS")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_visible(GSTHomeLocators.progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_horizon_progress_bar not visible PASS")
            self.sb.wait_for_element_absent(GSTHomeLocators.progressBar, timeout=wait_not_visible_time)
            LOGGER.info("wait_horizon_progress_bar_absent  PASS")
        except Exception as e:
            LOGGER.info("wait_horizon_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_horizon_progress_bar exception screenshot")


    def delete_exist_QueryAndDraft(self, query_name="", is_gsql_query=False):
        gsw = GraphStudioWindows(self.sb)
        # delete old query before creating a query
        self.sb.type(GSTHomeLocators.searchButton, query_name)
        self.sb.sleep(2)
        queryList = self.sb.find_elements(GSTHomeLocators.queryList)
        LOGGER.info("query list =" + str(len(queryList)))
        if len(queryList) > 0:
            for i in range(len(queryList)):
                self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
                if is_gsql_query:
                    self.sb.wait_for_element_clickable(GSTHomeLocators.deleteQueryButton, timeout=clickTimeout)
                    self.sb.click(GSTHomeLocators.deleteQueryButton, timeout=clickTimeout)
                else:
                    self.sb.wait_for_element_clickable(GSTHomeLocators.discardDraftButton, timeout=clickTimeout)
                    self.sb.click(GSTHomeLocators.discardDraftButton, timeout=clickTimeout)
                gsw.getDialogContinueButton().click()
                self.wait_progress_bar()
        self.sb.attach_allure_screenshot("delete existed query done screenshot")
        self.sb.wait_for_text_visible("No results found", timeout=clickTimeout)

    def deleteQueryAndDraft(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)
        # turn to write queries
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_write_queries, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.downloadQuery, timeout=clickTimeout)
        self.delete_exist_QueryAndDraft(query_name="deleteQuery")

        # create new gsql query
        self.sb.sleep(3) #improve the success rate
        self.sb.wait_for_element_clickable(GSTHomeLocators.createNewQueryButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.createNewQueryButton, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.inputNewQueryName, "deleteQuery", timeout=clickTimeout)
        self.sb.click(DialogLocators.create_btn, timeout=clickTimeout)
        self.wait_progress_bar()

        # select the query and discard
        self.sb.wait_for_text_not_visible("No results found", timeout=clickTimeout)
        self.sb.sleep(3)
        self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
        self.sb.attach_allure_screenshot("create query done screenshot")

        # install query
        self.sb.wait_for_element_clickable(GSTHomeLocators.installCurrentQueryButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.installCurrentQueryButton, timeout=clickTimeout)
        self.wait_horizon_progress_bar(wait_present_time=wait_render_time, wait_not_visible_time=300)
        self.sb.attach_allure_screenshot("install query done screenshot")

        # delete query and draft
        self.sb.sleep(3)#control the install and delete rhythm
        # should select the query again to enable delete button
        queryList = self.sb.find_elements(GSTHomeLocators.queryList)
        LOGGER.info("query list =" + str(len(queryList)))
        self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
        self.sb.wait_for_element_clickable(GSTHomeLocators.deleteQueryButton, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.deleteQueryButton, timeout=clickTimeout)
        self.sb.click(DialogLocators.continue_btn, timeout=clickTimeout)
        self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        self.sb.attach_allure_screenshot("deleting query screenshot")

        # content = gsw.getPopWindowContent()
        # LOGGER.info("delete result:" + content)
        # self.sb.assert_equal('The query "deleteQuery" is deletedDISMISS', content)
        self.sb.attach_allure_screenshot("delete query and draft done screenshot")
        # use other assert method
        self.sb.assert_text("No results found", timeout=clickTimeout)
        self.sb.sleep(2)#should wait 2 second for render the progressbar
        queryList = self.sb.find_elements(GSTHomeLocators.queryList)
        self.sb.assert_equal(0, len(queryList))


    def downloadCurrentQuery(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)
        # turn to write queries
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_write_queries, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.downloadQuery, timeout=clickTimeout)
        self.delete_exist_QueryAndDraft(query_name="DownloadCurrentQuery")
        self.sb.attach_allure_screenshot("delete query done screenshot")

        # create new gsql query
        self.sb.wait_for_element_clickable(GSTHomeLocators.createNewQueryButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.createNewQueryButton, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.inputNewQueryName, "DownloadCurrentQuery", timeout=clickTimeout)
        self.sb.click(DialogLocators.create_btn, timeout=clickTimeout)
        self.wait_progress_bar()

        # select the query and discard
        self.sb.wait_for_text_not_visible("No results found", timeout=clickTimeout)
        self.sb.sleep(1)
        self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
        self.sb.attach_allure_screenshot("create query done screenshot")

        # download current query
        Browser = os.getenv("Browser", "")
        # Browser = "edge"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            download_path = self.sb.get_downloads_folder()
        else:
            download_path = "/tmp/GST"
        GAPPage().setChromeDownloadPath(self.sb, download_path)
        GAPPage().delete_files_under_chrome_download_path(path=download_path, fileName="DownloadCurrentQuery")
        self.sb.wait_for_element_clickable(GSTHomeLocators.downloadCurrentQueryButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.downloadCurrentQueryButton, timeout=clickTimeout)

        # Check download file and file size
        download_res, download_file = GAPPage().judge_file_exist(5, download_path, "DownloadCurrentQuery", "gsql")
        LOGGER.info("download_res: " + str(download_res))
        LOGGER.info("download_file: " + download_file)
        self.sb.attach_allure_screenshot("Download current query result")
        if not download_res:
            download_res, download_file = GAPPage().judge_file_exist(5, download_path, "DownloadCurrentQuery", "gsql")
            LOGGER.info("not download_res: " + str(download_res))
            LOGGER.info("not download_file: " + download_file)
            raise Exception("download file not found")
        else:
            file_size = os.path.getsize("{}/{}".format(download_path, download_file))
            LOGGER.info("file_size: " + str(file_size))
            self.sb.assert_true(file_size > 10)


    def downloadAllQueries(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        # turn to write queries
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_write_queries, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.downloadAllQueryButton, timeout=clickTimeout)

        # download all queries
        Browser = os.getenv("Browser", "")
        # Browser = "edge"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            download_path = self.sb.get_downloads_folder()
        else:
            download_path = "/tmp/GST"
        GAPPage().setChromeDownloadPath(self.sb, download_path)
        GAPPage().delete_files_under_chrome_download_path(path=download_path,fileName=self.graphName + "_Queries")
        self.sb.click(GSTHomeLocators.downloadAllQueryButton, timeout=clickTimeout)
        self.sb.sleep(10)

        # Check download file and file size
        download_res, download_file = GAPPage().judge_file_exist(5, download_path, self.graphName + "_Queries", "zip")
        LOGGER.info("download_res: " + str(download_res))
        LOGGER.info("download_file: " + download_file)
        self.sb.attach_allure_screenshot("Download all queries result")
        if not download_res:
            download_res, download_file = GAPPage().judge_file_exist(5, download_path, self.graphName + "_Queries", "zip")
            LOGGER.info("not download_res: " + str(download_res))
            LOGGER.info("not download_file: " + download_file)
            raise Exception("download file not found")
        else:
            file_size = os.path.getsize("{}/{}".format(download_path, download_file))
            LOGGER.info("file_size: " + str(file_size))
            self.sb.assert_true(file_size > 10)


    def addVertex(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)

        # turn to build graph patterns
        self.switch_to_graph(self.graphName)
        self.close_security_float_windows()
        self.sb.click(GSTHomeLocators.nav_build_graph_patterns, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console, timeout=clickTimeout)

        # delete old pattern before creating a query
        self.sb.type(GSTHomeLocators.searchButton, "myPattern")
        # self.sb.type(GSTHomeLocators.searchButton, "t")
        self.sb.sleep(1)
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("query list =" + str(len(queryList)))
        try:
            if len(queryList) > 0:
                for i in range(len(queryList)):
                    self.sb.find_elements(GSTHomeLocators.deletePatterns)[0].click()
                    gsw.getDialogContinueButton().click()
                    self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        except Exception as e:
            LOGGER.info(str(e))
        self.sb.attach_allure_screenshot("delete myPattern done screenshot")

        # create new patterns
        self.sb.wait_for_element_clickable(GSTHomeLocators.createNewPatternButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.createNewPatternButton, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.inputNewPatternName, "myPattern", timeout=clickTimeout)
        self.sb.wait_for_element_clickable(DialogLocators.add_btn, timeout=clickTimeout)
        self.sb.click(DialogLocators.add_btn, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("create pattern done screenshot")

        # add a vertex
        self.sb.wait_for_element_clickable(GSTHomeLocators.addVertexButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.addVertexButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.outputButton, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        self.sb.click(GSTHomeLocators.limitButton, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.useLimitBox, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.useLimitBox, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        self.sb.click(GSTHomeLocators.savePatternButton, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("add vertex done screenshot")

        # run the pattern and assert result
        self.sb.wait_for_element_clickable(GSTHomeLocators.runPatternButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.runPatternButton, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("run pattern screenshot")
        # offten not see progress bar, so handle by wait_for_element_present method
        self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        self.sb.wait_for_element_clickable(GSTHomeLocators.viewJsonResultButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.viewJsonResultButton, timeout=clickTimeout)
        self.sb.assert_text('"v_type": "City"', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert json result screenshot")
        self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        self.sb.assert_text('1 – 10 of 10', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert table result screenshot")
        self.sb.click(GSTHomeLocators.viewProblemsButton, timeout=clickTimeout)
        self.sb.assert_text('No problems have been found in the graph pattern', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert problems result screenshot")


    def editVertex(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)

        # turn to build graph patterns
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_build_graph_patterns, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console, timeout=clickTimeout)

        # delete old pattern before creating a query
        self.sb.type(GSTHomeLocators.searchButton, "myPattern")
        # self.sb.type(GSTHomeLocators.searchButton, "t")
        self.sb.sleep(1)
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("query list =" + str(len(queryList)))
        self.sb.attach_allure_screenshot("search myPattern done screenshot")
        if len(queryList) == 0:
            raise Exception("pattern not exist, stop editing")

        # edit patterns
        # TODO click the vertex in the canvas
        self.sb.attach_allure_screenshot("create pattern done screenshot")

        # edit vertex
        # self.sb.click(GSTHomeLocators.addVertexButton, timeout=clickTimeout)
        # self.sb.click(GSTHomeLocators.outputButton, timeout=clickTimeout)
        # self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        # self.sb.click(GSTHomeLocators.limitButton, timeout=clickTimeout)
        # self.sb.sleep(0.5)
        # self.sb.click(GSTHomeLocators.useLimitBox, timeout=clickTimeout)
        # self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        # self.sb.click(GSTHomeLocators.savePatternButton, timeout=clickTimeout)
        # self.sb.attach_allure_screenshot("add vertex done screenshot")

        # # run the pattern and assert result
        # self.sb.click(GSTHomeLocators.runPatternButton, timeout=clickTimeout)
        # self.sb.click(GSTHomeLocators.viewJsonResultButton, timeout=clickTimeout)
        # self.sb.assert_text('"v_type": "City"')
        # self.sb.attach_allure_screenshot("assert json result screenshot")
        # self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        # self.sb.assert_text('1 – 10 of 10')
        # self.sb.attach_allure_screenshot("assert table result screenshot")
        # self.sb.click(GSTHomeLocators.viewProblemsButton, timeout=clickTimeout)
        # self.sb.assert_text('No problems have been found in the graph pattern')
        # self.sb.attach_allure_screenshot("assert problems result screenshot")

    def deleteVertex(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)

        # turn to build graph patterns
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_build_graph_patterns, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console, timeout=clickTimeout)

        # delete old pattern before creating a query
        self.sb.type(GSTHomeLocators.searchButton, "myPattern")
        self.sb.sleep(1)
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("query list =" + str(len(queryList)))
        if len(queryList) == 0:
            raise Exception("myPattern not exist, stop deleting")
        else:
            for i in range(len(queryList)):
                self.sb.find_elements(GSTHomeLocators.deletePatterns)[0].click()
                gsw.getDialogContinueButton().click()
                self.wait_progress_bar()
                self.sb.sleep(0.3)

        self.sb.attach_allure_screenshot("delete myPattern done screenshot")
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("query list =" + str(len(queryList)))
        self.sb.assert_equal(0, len(queryList))

    def addEdge(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)

        # turn to build graph patterns
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_build_graph_patterns, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console, timeout=clickTimeout)

        # delete old pattern before creating a query
        self.sb.type(GSTHomeLocators.searchButton, "myPattern_edge")
        # self.sb.type(GSTHomeLocators.searchButton, "t")
        self.sb.sleep(1)
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("query list =" + str(len(queryList)))
        if len(queryList) > 0:
            for i in range(len(queryList)):
                self.sb.find_elements(GSTHomeLocators.deletePatterns)[0].click()
                gsw.getDialogContinueButton().click()
                self.wait_progress_bar()
                self.sb.sleep(0.3)

        self.sb.attach_allure_screenshot("delete myPattern_edge done screenshot")

        # create new patterns
        self.sb.wait_for_element_clickable(GSTHomeLocators.createNewPatternButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.createNewPatternButton, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.inputNewPatternName, "myPattern_edge", timeout=clickTimeout)
        self.sb.click(DialogLocators.add_btn, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("create myPattern_edge done screenshot")

        # add a city vertex
        self.sb.click(GSTHomeLocators.addVertexButton, timeout=clickTimeout)
        # select City vertex
        self.sb.click_nth_visible_element(GSTHomeLocators.vertexListButtons, 1)

        self.sb.click(GSTHomeLocators.outputButton, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        self.sb.click(GSTHomeLocators.limitButton, timeout=clickTimeout)
        self.sb.sleep(0.5)
        self.sb.click(GSTHomeLocators.useLimitBox, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        self.sb.click(GSTHomeLocators.savePatternButton, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("add vertex done screenshot")

        # add a province vertex
        self.sb.click(GSTHomeLocators.addVertexButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.outputButton, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        self.sb.click(GSTHomeLocators.limitButton, timeout=clickTimeout)
        self.sb.sleep(0.5)
        self.sb.click(GSTHomeLocators.useLimitBox, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        self.sb.click(GSTHomeLocators.savePatternButton, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("add vertex done screenshot")

        # run the pattern and assert result
        self.sb.click(GSTHomeLocators.runPatternButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.viewJsonResultButton, timeout=clickTimeout)
        self.sb.assert_text('"v_type": "City"', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert json result screenshot")
        self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        self.sb.assert_text('1 – 10 of 10', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert table result screenshot")
        self.sb.click(GSTHomeLocators.viewProblemsButton, timeout=clickTimeout)
        self.sb.assert_text('No problems have been found in the graph pattern', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert problems result screenshot")


    def close_security_float_windows(self):
        try:
            if self.sb.is_text_visible("user's password"):
                self.sb.execute_script(GSTHomeLocators.close_security_icon_JS)
                LOGGER.info("close_security_float_windows done")
            else:
                LOGGER.info("security_float_windows not appear, didn't find user's password")
                self.sb.attach_allure_screenshot("close_security_float_windows failed screenshot")
        except Exception as e:
            LOGGER.info("close_security_float_windows Exception:" + str(e))
            self.sb.attach_allure_screenshot("close_security_float_windows failed screenshot")


    def editEdge(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)

        # turn to build graph patterns
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_build_graph_patterns, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console, timeout=clickTimeout)

        # delete old pattern before creating a query
        self.sb.type(GSTHomeLocators.searchButton, "myPattern_edge")
        # self.sb.type(GSTHomeLocators.searchButton, "t")
        self.sb.sleep(1)
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("query list =" + str(len(queryList)))
        self.sb.attach_allure_screenshot("search myPattern done screenshot")
        if len(queryList) == 0:
            raise Exception("pattern not exist, stop editing")

        # edit patterns
        # TODO click the vertex in the canvas
        self.sb.attach_allure_screenshot("create pattern done screenshot")

        # edit vertex
        # self.sb.click(GSTHomeLocators.addVertexButton, timeout=clickTimeout)
        # self.sb.click(GSTHomeLocators.outputButton, timeout=clickTimeout)
        # self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        # self.sb.click(GSTHomeLocators.limitButton, timeout=clickTimeout)
        # self.sb.sleep(0.5)
        # self.sb.click(GSTHomeLocators.useLimitBox, timeout=clickTimeout)
        # self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        # self.sb.click(GSTHomeLocators.savePatternButton, timeout=clickTimeout)
        # self.sb.attach_allure_screenshot("add vertex done screenshot")

        # run the pattern and assert result
        # self.sb.click(GSTHomeLocators.runPatternButton, timeout=clickTimeout)
        # self.sb.click(GSTHomeLocators.viewJsonResultButton, timeout=clickTimeout)
        # self.sb.assert_text('"v_type": "City"')
        # self.sb.attach_allure_screenshot("assert json result screenshot")
        # self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        # self.sb.assert_text('1 – 10 of 10')
        # self.sb.attach_allure_screenshot("assert table result screenshot")
        # self.sb.click(GSTHomeLocators.viewProblemsButton, timeout=clickTimeout)
        # self.sb.assert_text('No problems have been found in the graph pattern')
        # self.sb.attach_allure_screenshot("assert problems result screenshot")

    def deleteEdge(self):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)

        # turn to build graph patterns
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_build_graph_patterns, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console, timeout=clickTimeout)

        # delete old pattern before creating a query
        self.sb.type(GSTHomeLocators.searchButton, "myPattern_edge")
        self.sb.sleep(1)
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("query list =" + str(len(queryList)))
        if len(queryList) == 0:
            raise Exception("myPattern not exist, stop deleting")
        else:
            for i in range(len(queryList)):
                self.sb.find_elements(GSTHomeLocators.deletePatterns)[0].click()
                gsw.getDialogContinueButton().click()
                self.wait_progress_bar()
                self.sb.sleep(0.3)

        self.sb.attach_allure_screenshot("delete myPattern done screenshot")
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("query list =" + str(len(queryList)))
        self.sb.assert_equal(0, len(queryList))



    def save_exploration(self, exploration_name = "saveExploration"):
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        gsw = GraphStudioWindows(self.sb)

        # turn to build graph patterns
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_build_graph_patterns, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console, timeout=wait_render_time)
        self.sb.wait_for_element_clickable(GSTHomeLocators.searchButton, timeout=wait_render_time)
        self.close_security_float_windows()

        # delete old pattern before creating a query
        self.sb.type(GSTHomeLocators.searchButton, exploration_name)
        self.sb.sleep(1)
        queryList = self.sb.find_elements(GSTHomeLocators.patternList)
        LOGGER.info("previous pattern query list =" + str(len(queryList)))
        try:
            if len(queryList) > 0:
                for i in range(len(queryList)):
                    self.sb.find_elements(GSTHomeLocators.deletePatterns)[0].click()
                    gsw.getDialogContinueButton().click()
                    self.wait_progress_bar()
                    self.sb.sleep(0.3)
        except Exception as e:
            LOGGER.info(str(e))
        self.sb.attach_allure_screenshot("delete "+ exploration_name+" pattern done screenshot")

        # create new patterns
        self.sb.wait_for_element_clickable(GSTHomeLocators.createNewPatternButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.createNewPatternButton, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.inputNewPatternName, exploration_name, timeout=clickTimeout)
        self.sb.click(DialogLocators.add_btn, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("create "+ exploration_name+" pattern done screenshot")

        # add a vertex
        self.sb.click(GSTHomeLocators.addVertexButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.outputButton, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        self.sb.click(GSTHomeLocators.limitButton, timeout=clickTimeout)
        self.sb.sleep(0.5)
        self.sb.click(GSTHomeLocators.useLimitBox, timeout=clickTimeout)
        self.sb.click_nth_visible_element(GSTHomeLocators.updateButtons, 1)
        self.sb.click(GSTHomeLocators.savePatternButton, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("add vertex done screenshot")

        # delete saveExploration if existed
        self.sb.click(GSTHomeLocators.view_graph_result_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.open_exploration_button, timeout=clickTimeout)
        self.wait_progress_bar()
        self.sb.sleep(5)
        self.sb.wait_for_element_clickable(DialogLocators.cancel_btn, timeout=wait_render_time)
        queryList = self.sb.find_elements(GSTHomeLocators.delete_exploration_button)
        LOGGER.info("previous exploration query list =" + str(len(queryList)))
        try:
            if len(queryList) > 0:
                self.sb.attach_allure_screenshot("begin to detect "+exploration_name+" existed screenshot")
                # optimize: delete exploration exactly
                for i in range(len(queryList)):
                    LOGGER.info("exploration_name: " + exploration_name)
                    tmp_title = self.sb.find_elements(GSTHomeLocators.exploration_title)[i].text
                    LOGGER.info("       exploration_title: " + tmp_title)
                    if exploration_name in tmp_title:
                        self.sb.attach_allure_screenshot("delete "+exploration_name+" screenshot")
                        self.sb.find_elements(GSTHomeLocators.delete_exploration_button)[i].click()
                        self.sb.click(DialogLocators.continue_btn, timeout=clickTimeout)
                        self.sb.sleep(0.5)
        except Exception as e:
            LOGGER.info(str(e))
        self.sb.wait_for_element_clickable(DialogLocators.cancel_btn, timeout=wait_render_time)
        self.sb.click(DialogLocators.cancel_btn, timeout=clickTimeout)

        # run the pattern and save exploration
        self.sb.wait_for_element_clickable(GSTHomeLocators.runPatternButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.runPatternButton, timeout=clickTimeout)
        self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        self.sb.sleep(3)  # control the rhythm after run pattern
        self.sb.wait_for_element_clickable(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        self.sb.assert_text('1 – 10 of 10', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert table result screenshot")
        self.sb.click(GSTHomeLocators.view_graph_result_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.save_exploration_button, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.input_exploration_name, exploration_name)
        self.sb.type(GSTHomeLocators.exploration_description_area, "This is a test for saving " + exploration_name + " exploration!")
        self.sb.click(DialogLocators.save_btn, timeout=clickTimeout)
        self.sb.attach_allure_screenshot("saved " + exploration_name + " successfully screenshot")
        # LOGGER.info("saved " + exploration_name + " successfully log, instead of screenshot")
        # assert result
        # title = gsw.getPopWindowContent()
        # LOGGER.info("pop window title:" + str(title))
        # self.sb.assert_equal('Exploration result "'+exploration_name+'" is savedDISMISS', title, msg=None)
        # open exploration and assert save successfully

        # double check saveExploration successfully
        self.sb.click(GSTHomeLocators.view_graph_result_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.open_exploration_button, timeout=clickTimeout)
        self.wait_progress_bar()
        self.sb.sleep(5)
        self.sb.wait_for_element_clickable(DialogLocators.cancel_btn, timeout=wait_render_time)
        queryList = self.sb.find_elements(GSTHomeLocators.delete_exploration_button)
        LOGGER.info("assert exploration query list =" + str(len(queryList)))
        assert_flag = False
        try:
            if len(queryList) > 0:
                self.sb.attach_allure_screenshot("find "+exploration_name+" screenshot")
                # optimize: delete exploration exactly
                for i in range(len(queryList)):
                    LOGGER.info("exploration_name: " + exploration_name)
                    tmp_title = self.sb.find_elements(GSTHomeLocators.exploration_title)[i].text
                    LOGGER.info("   exploration_title: " + tmp_title)
                    if exploration_name in tmp_title:
                        assert_flag = True
                        break
        except Exception as e:
            LOGGER.info(str(e))
        LOGGER.info("assert_flag: " + str(assert_flag))
        self.sb.assert_true(assert_flag)
        self.sb.attach_allure_screenshot("double check save " + exploration_name + " exploration successfully screenshot")
        self.sb.wait_for_element_clickable(DialogLocators.cancel_btn, timeout=wait_render_time)
        self.sb.click(DialogLocators.cancel_btn, timeout=clickTimeout)


    def open_exploration(self):
        self.save_exploration(exploration_name="openExploration")
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)

        # turn to build graph patterns
        self.switch_to_graph(self.graphName)
        self.sb.click(GSTHomeLocators.nav_build_graph_patterns, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console, timeout=clickTimeout)

        self.sb.click(GSTHomeLocators.view_graph_result_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.open_exploration_button, timeout=clickTimeout)
        self.wait_progress_bar()
        # self.sb.wait_for_text_not_visible("You have no", timeout=clickTimeout)
        self.sb.sleep(3)
        queryList = self.sb.find_elements(GSTHomeLocators.select_open_exploration_div)
        LOGGER.info("previous query list =" + str(len(queryList)))
        if len(queryList) == 0:
            self.sb.attach_allure_screenshot("can't find openExploration screenshot")
            raise Exception("can't find saveExploration")

        # open exploration and assert
        self.sb.wait_for_element_clickable(GSTHomeLocators.select_open_exploration_div, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.select_open_exploration_div, timeout=clickTimeout)
        self.sb.click(DialogLocators.open_btn, timeout=clickTimeout)
        self.sb.sleep(0.5)
        self.sb.click(DialogLocators.continue_btn, timeout=clickTimeout)
        self.sb.sleep(0.5)
        self.sb.attach_allure_screenshot("opened saveExploration screenshot")


    def export_PNG(self):
        self.save_exploration(exploration_name="export_PNG")

        # download PNG
        Browser = os.getenv("Browser", "")
        # Browser = "edge"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            download_path = self.sb.get_downloads_folder()
        else:
            download_path = "/tmp/GST"
        GAPPage().setChromeDownloadPath(self.sb, download_path)
        GAPPage().delete_files_under_chrome_download_path(path=download_path,fileName="Screenshot")

        # export PNG
        self.sb.wait_for_element_clickable(GSTHomeLocators.export_graph_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.export_graph_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.export_PNG_button, timeout=clickTimeout)

        # Check download file and file size
        download_res, download_file = GAPPage().judge_file_exist(5, download_path, "Screenshot", "png")
        LOGGER.info("download_res: " + str(download_res))
        LOGGER.info("download_file: " + download_file)
        self.sb.attach_allure_screenshot("Download Screenshot_PNG result")
        if not download_res:
            download_res, download_file = GAPPage().judge_file_exist(5, download_path, "Screenshot", "png")
            LOGGER.info("not download_res: " + str(download_res))
            LOGGER.info("not download_file: " + download_file)
            raise Exception("download file not found")
        else:
            file_size = os.path.getsize("{}/{}".format(download_path, download_file))
            LOGGER.info("file_size: " + str(file_size))
            self.sb.assert_true(file_size > 10)


    def export_CSV(self):
        self.save_exploration(exploration_name="export_CSV")

        # download CSV
        Browser = os.getenv("Browser", "")
        # Browser = "edge"
        LOGGER.info("Browser= " + Browser)
        if Browser == "firefox":
            download_path = self.sb.get_downloads_folder()
        else:
            download_path = "/tmp/GST"
        GAPPage().setChromeDownloadPath(self.sb, download_path)
        GAPPage().delete_files_under_chrome_download_path(path=download_path, fileName=self.graphName + "_export_vertices_and_edges")

        # export CSV
        self.sb.wait_for_element_clickable(GSTHomeLocators.export_graph_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.export_graph_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.export_CSV_button, timeout=clickTimeout)

        # Check download file and file size
        download_res, download_file = GAPPage().judge_file_exist(5, download_path, self.graphName + "_export_vertices_and_edges", "zip")
        LOGGER.info("download_res: " + str(download_res))
        LOGGER.info("download_file: " + download_file)
        self.sb.attach_allure_screenshot("Download export_CSV result")
        if not download_res:
            download_res, download_file = GAPPage().judge_file_exist(5, download_path, self.graphName + "_export_vertices_and_edges", "zip")
            LOGGER.info("not download_res: " + str(download_res))
            LOGGER.info("not download_file: " + download_file)
            raise Exception("download file not found")
        else:
            file_size = os.path.getsize("{}/{}".format(download_path, download_file))
            LOGGER.info("file_size: " + str(file_size))
            self.sb.assert_true(file_size > 10)

    def create_OpenCypher_query(self, query_name, myGsql):
        """
        create opencypher query and check result
        """
        gsw = GraphStudioWindows(self.sb)
        # delete old query before creating a query
        self.sb.type(GSTHomeLocators.searchButton, query_name)
        self.sb.sleep(2)
        queryList = self.sb.find_elements(GSTHomeLocators.queryList)
        LOGGER.info("query list =" + str(len(queryList)))
        if len(queryList) > 0:
            for i in range(len(queryList)):
                self.sb.find_elements(GSTHomeLocators.queryList)[0].click()
                if self.sb.is_element_clickable(GSTHomeLocators.deleteQueryButton):
                    self.sb.click(GSTHomeLocators.deleteQueryButton, timeout=clickTimeout)
                else:
                    self.sb.click(GSTHomeLocators.discardDraftButton, timeout=clickTimeout)
                gsw.getDialogContinueButton().click()
                self.wait_progress_bar()

        self.sb.attach_allure_screenshot("delete query done screenshot")

        # create new gsql query
        self.sb.wait_for_element_clickable(GSTHomeLocators.createNewQueryButton, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.createNewQueryButton, timeout=clickTimeout)

        # select query type
        self.sb.click(GSTHomeLocators.openCypherLabel, timeout = clickTimeout)
        self.sb.type(GSTHomeLocators.inputNewQueryName, query_name, timeout = clickTimeout)
        self.sb.click(DialogLocators.create_btn, timeout=clickTimeout)
        self.wait_progress_bar()

        # input query, save and run
        self.sb.wait_for_text_not_visible("No results found", timeout=clickTimeout)
        self.sb.sleep(3)
        # check OpenCypher mark for the query
        self.sb.assert_element_present(GSTHomeLocators.openCypherMark)

        cmd = "queryEditorChart.textEditor.setValue(`"+myGsql+"`)"
        LOGGER.info("create query cmd: " + cmd)
        self.sb.execute_script(cmd)
        self.sb.attach_allure_screenshot("create opencypher query done screenshot")
        # save query
        self.sb.sleep(10)
        self.sb.attach_allure_screenshot("wait auto_save done screenshot")
        self.sb.wait_for_element_clickable(GSTHomeLocators.save_query_button, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.save_query_button, timeout=clickTimeout)

    def run_OpenCypher_query_interpret_mode(self):
        """
        create opencypher query and run query in interpret mode
        """
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        # turn to write queries
        self.switch_to_graph("MyGraph")
        self.sb.click(GSTHomeLocators.nav_write_queries, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console_button, timeout=wait_render_time)
        self.sb.wait_for_element_clickable(GSTHomeLocators.searchButton, timeout=wait_render_time)

        # create OpenCypher query    
        myGsql = "CREATE DISTRIBUTED OPENCYPHER QUERY opencypher_test(datetime date) FOR GRAPH MyGraph { \n" +\
                 "/* Write query logic here */ \n" +\
                 "MATCH (c:Country)-[pp:PATIENT_FROM_COUNTRY]-(p:Patient)\n" +\
                 "MATCH (te:TravelEvent)-[traveled:PATIENT_TRAVELED]-(p)\n" +\
                 "WHERE p.sex = 'female' AND te.visited_date > $date \n" +\
                 "RETURN p\n" +\
                 "ORDER BY p.birth_year DESC\n" +\
                 "LIMIT 10 }"

        self.create_OpenCypher_query("opencypher_test", myGsql)

        # run query in interpret mode
        self.sb.sleep(10)
        self.sb.attach_allure_screenshot("wait auto_save done screenshot")
        self.sb.wait_for_element_clickable(GSTHomeLocators.save_query_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.save_query_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.run_query_button, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.input_para_opencypher, "2020-01-01", timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.run_query_button_below_input_parameter, timeout=clickTimeout)

        # wait result
        self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        self.sb.attach_allure_screenshot("run opencypher query screenshot")

        # assert result
        self.sb.wait_for_element_clickable(GSTHomeLocators.viewJsonResultButton, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.viewJsonResultButton, timeout=clickTimeout)
        self.sb.assert_text('Patient', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert json result screenshot")
        self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        self.sb.assert_elements_present(GSTHomeLocators.gridcell, timeout=assertTextTimeout)
        # check result column count
        self.sb.assert_elements_present(GSTHomeLocators.result_count_div.format("1 – 10 of 10"), timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert table result screenshot")
        self.sb.click(GSTHomeLocators.view_log_button, timeout=clickTimeout)
        self.sb.assert_text('Query run successfully', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert log result screenshot")

    def run_run_OpenCypher_query_installed_mode(self):
        """
        Create OpenCypher query and run in installed mdoe
        """
        self.sb.wait_for_element(GSTHomeLocators.nav_home, timeout=60)
        # turn to write queries
        self.switch_to_graph("MyGraph")
        self.sb.click(GSTHomeLocators.nav_write_queries, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.console_button, timeout=wait_render_time)
        self.sb.wait_for_element_clickable(GSTHomeLocators.searchButton, timeout=wait_render_time)

        # create OpenCypher query    
        myGsql = "CREATE DISTRIBUTED OPENCYPHER QUERY opencypher_test1(datetime date) FOR GRAPH MyGraph { \n" +\
                 "/* Write query logic here */ \n" +\
                 "MATCH (c:Country)-[pp:PATIENT_FROM_COUNTRY]-(p:Patient)\n" +\
                 "MATCH (te:TravelEvent)-[traveled:PATIENT_TRAVELED]-(p)\n" +\
                 "WHERE p.sex = 'female' AND te.visited_date > $date \n" +\
                 "RETURN p\n" +\
                 "ORDER BY p.birth_year DESC\n" +\
                 "LIMIT 10 }"

        self.create_OpenCypher_query("opencypher_test1", myGsql)

        # install query
        self.sb.sleep(10)
        self.sb.attach_allure_screenshot("wait auto_save done screenshot")
        self.sb.wait_for_element_clickable(GSTHomeLocators.save_query_button, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.save_query_button, timeout=clickTimeout)
        self.sb.wait_for_element_clickable(GSTHomeLocators.installCurrentQueryButton, timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.installCurrentQueryButton, timeout=clickTimeout)
        self.wait_horizon_progress_bar(wait_present_time=wait_render_time, wait_not_visible_time=300)
        self.sb.attach_allure_screenshot("install query done screenshot")

        # run query
        self.sb.sleep(3)
        self.sb.click(GSTHomeLocators.run_installed_query_button, timeout=clickTimeout)
        self.sb.type(GSTHomeLocators.input_para_opencypher, "2020-01-01", timeout=clickTimeout)
        self.sb.click(GSTHomeLocators.run_query_button_below_input_parameter, timeout=clickTimeout)

        # wait result
        self.wait_progress_bar(wait_present_time=clickTimeout, wait_not_visible_time=wait_render_time)
        self.sb.attach_allure_screenshot("run opencypher query screenshot")

        # assert result
        self.sb.wait_for_element_clickable(GSTHomeLocators.viewJsonResultButton, timeout=wait_render_time)
        self.sb.click(GSTHomeLocators.viewJsonResultButton, timeout=clickTimeout)
        self.sb.assert_text('Patient', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert json result screenshot")
        self.sb.click(GSTHomeLocators.viewTableResultButton, timeout=clickTimeout)
        self.sb.assert_elements_present(GSTHomeLocators.gridcell, timeout=assertTextTimeout)
        # check result column count
        self.sb.assert_elements_present(GSTHomeLocators.result_count_div.format("1 – 10 of 10"), timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert table result screenshot")
        self.sb.click(GSTHomeLocators.view_log_button, timeout=clickTimeout)
        self.sb.assert_text('Query run successfully', timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("assert log result screenshot")

    def is_cluster_available(self, db_version):
        """
        Check db version
        """
        loginUtil = LoginUtils(self.sb)
        cloud_env_flag = loginUtil.is_oncloud()
        is_case_executed = True
        if cloud_env_flag:
            try:
                version_tmp, is_three_version = self.get_tg_version()
                LOGGER.info(version_tmp)
                if version_tmp and version_tmp.strip():
                    if int(version_tmp) < db_version:
                        is_case_executed = False
                        LOGGER.info(f"Current version < db_version")
            except Exception as e:
                LOGGER.info(str(e))
        return is_case_executed