from enum import Enum
import logging
import allure
from locators.tools.graph_studio_locators import (
    DialogLocators,
    GSTHomeLocators,
    GSTWriteQueriesLocators,
)
from common import tools_settings, decorator
from base.tools_basepage import ToolsBasePage
from pages.tools.graphstudio.graph_studio_home_page import GSTHomePage
from seleniumbase.common import decorators

LOGGER = logging.getLogger(__name__)


class QueryStatus(Enum):
    INSTALLED = "Query installed"
    INTERPRETED = "Query not installed"
    INSTALLING = "Installing query"
    DRAFT = "Query draft saved"


class GSTWriteQueriesPage(ToolsBasePage):

    # Navigate
    @allure.step("Navigate to write queries")
    def navigate_to_write_queries(self, graphName):
        home_page = GSTHomePage(self.sb)
        home_page.switch_to_graph(graphName)
        LOGGER.info("Navigate to Write Queries page.")
        self.sb.wait_for_element_clickable(
            GSTWriteQueriesLocators.nav_write_queries,
            timeout=tools_settings.CLICK_TIME_OUT
        )
        self.sb.click(
            GSTWriteQueriesLocators.nav_write_queries,
            timeout=tools_settings.CLICK_TIME_OUT
        )
        home_page.wait_progress_bar(wait_present_time=tools_settings.CLICK_TIME_OUT)
        self.sb.assert_element_visible(GSTWriteQueriesLocators.header_name, timeout=tools_settings.ASSERT_TEXT_TIMEOUT)

    # Install query
    def is_install_all_queries_btn_enabled(self):
        return self.sb.is_element_enabled(
            GSTWriteQueriesLocators.install_all_queries_btn
        )

    def is_ready_install(self):
        if self.is_install_all_queries_btn_enabled():
            return True
        else:
            LOGGER.warning("Install all button is disabled. Maybe all queries installed")
            # add queries numbers assert to double check
            self.sb.attach_allure_screenshot("Install all button is disabled")
            queryList = self.sb.find_elements(GSTHomeLocators.query_list_number)
            LOGGER.info("query list =" + str(len(queryList)))
            if len(queryList) > 0:
                LOGGER.info("find queries")
            else:
                LOGGER.info("didn't find queries")
                self.sb.attach_allure_screenshot("didn't find queries screenshot")
                raise Exception("didn't find queries ")
            return False

    def click_install_all_queries_btn(self):
        LOGGER.info("Click install all queries button")
        self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.install_all_queries_btn, timeout=tools_settings.CLICK_TIME_OUT)
        self.sb.click(GSTWriteQueriesLocators.install_all_queries_btn)

    def click_install_all_queries_btn_in_popup(self):
        self.sb.wait_for_element_visible(DialogLocators.dialog_window, timeout=tools_settings.ADD_QUERY_TIMEOUT)
        self.sb.attach_allure_screenshot("Install all queries window pop")
        LOGGER.info("Click install queries button")
        self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.install_btn_in_popup, timeout=tools_settings.CLICK_TIME_OUT)
        self.sb.click(GSTWriteQueriesLocators.install_btn_in_popup)

    @decorator.log_runtime("Elapsed Time in installing all queries")
    def wait_for_query_installation_complete(self, present_timeout=tools_settings.CLICK_TIME_OUT, not_visiable_timeout=60):
        try:
            self.sb.sleep(3)
            self.sb.wait_for_element_present(GSTHomeLocators.progressBar, timeout=present_timeout)
            LOGGER.info("wait_for_progressBar_present")
            self.sb.sleep(3)
            self.sb.wait_for_element_not_visible(GSTHomeLocators.progressBar, timeout=not_visiable_timeout)
            LOGGER.info("wait_for_progressBar_not_visible")
            self.sb.attach_allure_screenshot("wait for progress bar not visible done screenshot")
        except Exception as e:
            LOGGER.info("wait_for_run_query_complete exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_for_query_installation_complete exception screenshot")

    def wait_for_horizon_progress_bar(self, present_timeout=tools_settings.CLICK_TIME_OUT, not_visiable_timeout=60):
        try:
            self.sb.wait_for_element_present(GSTHomeLocators.progressBar, timeout=present_timeout)
            LOGGER.info("wait_for_progressBar_present")
            self.sb.sleep(3)
            self.sb.wait_for_element_not_visible(GSTHomeLocators.progressBar, timeout=not_visiable_timeout)
            LOGGER.info("wait_for_progressBar_not_visible")
            self.sb.attach_allure_screenshot("wait for progress bar not visible done screenshot")
        except Exception as e:
            LOGGER.info("wait_for_horizon_progress_bar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_for_horizon_progress_bar exception screenshot")


    def assert_install_all_queries_result_in_log(self):
        LOGGER.info("Check the install result in log panel")
        self.click_view_logs_btn()
        self.sb.deferred_assert_text("Queries installed successfully", GSTWriteQueriesLocators.log_container)
        self.sb.attach_allure_screenshot("assert log: Queries installed successfully screenshot")
 

    def assert_install_all_queries_btn_disabled(self):
        self.sb.assert_false(
            self.is_install_all_queries_btn_enabled(),
            "Expect `install all query button` disabled, but enabled",
        )

    # Search and select query
    def select_query(self, query_name):
        self.sb.click(
            f"//a[@id='query-list-item']//p[normalize-space(text())='{query_name}']"
        )

    def search_query(self, query_name):
        self.sb.update_text(
            GSTWriteQueriesLocators.query_search_input, query_name, retry=True
        )

    def get_number_of_queries(self):
        """return the length of query list, including installed and interpreted queries"""
        return len(self.sb.find_elements(GSTWriteQueriesLocators.query_list_item))

    def search_select_query(self, query_name):
        """Better to use this method since maybe too many queries"""
        self.search_query(query_name)
        self.select_query(query_name)

    def get_query_status(self, query_name):
        """return the query status"""
        query_icon = f"//p[normalize-space(text())='{query_name}']//ancestor::a[@id='query-list-item']//mat-icon"
        status = self.sb.get_attribute(query_icon, "alt")
        return QueryStatus(status)

    def get_current_query_status(self):
        """return the query status of current selected query"""
        status = self.sb.get_attribute(
            "//a[@id='query-list-item' and contains(@class,'active')]//mat-icon", "alt"
        )
        return QueryStatus(status)

    # Run query
    def click_run_installed_query_btn(self):
        self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.run_installed_query_btn,timeout=tools_settings.CLICK_TIME_OUT)
        self.sb.click(GSTWriteQueriesLocators.run_installed_query_btn)

    def click_run_interpreted_query_btn(self):
        self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.run_interpreted_query_btn,timeout=tools_settings.CLICK_TIME_OUT)
        self.sb.click(GSTWriteQueriesLocators.run_interpreted_query_btn)

    def click_run_query_btn_in_toolbar(self):
        if self.sb.is_element_enabled(GSTWriteQueriesLocators.run_installed_query_btn):
            self.click_run_installed_query_btn()
        elif self.sb.is_element_enabled(
            GSTWriteQueriesLocators.run_interpreted_query_btn
        ):
            self.click_run_interpreted_query_btn()

    def click_run_query_btn(self):
        if self.sb.is_element_enabled(GSTWriteQueriesLocators.run_query_btn):
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.run_query_btn,timeout=tools_settings.CLICK_TIME_OUT)
            self.sb.click(GSTWriteQueriesLocators.run_query_btn)

    @decorator.log_runtime("Elapsed Time in running the query")
    def wait_for_run_query_complete(self, run_query_timeout=3600):
        try:
            self.sb.wait_for_element_present(GSTHomeLocators.circle_progressBar, timeout=tools_settings.CLICK_TIME_OUT)
            LOGGER.info("wait_for_element_present passed")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_visible(GSTHomeLocators.circle_progressBar, timeout=run_query_timeout)
            LOGGER.info("wait_for_element_not_visible passed")
        except Exception as e:
            LOGGER.info("wait_for_run_query_complete exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_for_run_query_complete exception screenshot")

    @decorator.log_runtime("Elapsed Time in running the query")
    def wait_for_install_query_run_complete(self, run_query_timeout=60):
        try:
            self.sb.wait_for_element_present(GSTHomeLocators.run_install_query_circle_progressBar, timeout=run_query_timeout)
            LOGGER.info("wait_for_element_present passed")
            self.sb.sleep(1)
            self.sb.wait_for_element_not_visible(GSTHomeLocators.run_install_query_circle_progressBar, timeout=run_query_timeout)
            LOGGER.info("wait_for_element_not_visible passed")
        except Exception as e:
            LOGGER.info("wait_for_install_query_run_complete exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_for_install_query_run_complete exception screenshot")

    # Parameters
    def get_parameter_count(self):
        self.sb.is_element_visible(GSTWriteQueriesLocators.parameter_hint)
        return len(self.sb.find_elements(GSTWriteQueriesLocators.parameter_hint))

    def get_parameter_container(self, parameter_name):
        "Get query parameter container locator based on query parameter name"
        return f"//mat-hint[contains(text(),' {parameter_name}:')]/ancestor::div[1]/following-sibling::div[1]"

    def get_parameter_fields(self, parameter_name):
        """Get the parameter input field based on the query parameter name, a query parameter
        container may have multiple parameter input fields, and a parameter input
        field may contain parameter types such as input boxes and drop-down lists."""
        return f"{self.get_parameter_container(parameter_name)}//mat-form-field"

    def get_parameter_field(self, parameter_name, field_index):
        """Get the parameter input field based on the query parameter name and the parameter
        input field index. A parameter input field may contain parameter types such as
        input boxes and drop-down lists. The field_index must be started from 1 due to xpath"""
        return f"({self.get_parameter_fields(parameter_name)})[{field_index}]"

    def get_parameter_input(self, parameter_name):
        """Get the parameter input location based on the query parameter name,
        applicable to parameters with only one input box"""
        return f"{self.get_parameter_container(parameter_name)}//input"

    def get_parameter_nth_input(self, parameter_name, input_index):
        """Obtain parameter input positioning based on query parameter name and input box number,
        applicable to parameters with multiple input boxes. e.g. SET"""
        return f"{self.get_parameter_field(parameter_name,input_index)}//input"

    def get_option(self, option_name):
        """Get the option selection by option name"""
        return f"//div[contains(@class,'mat-select-panel')]//span[@class='mat-option-text' and normalize-space(text())='{option_name}']"

    def get_add_values_btn(self, parameter_name):
        """Get the `Add Values` button based on the parameter name.
        Suitable for parameter types such as SET"""
        return f"{self.get_parameter_container(parameter_name)}//mat-icon[text()='add']"

    def fill_input(self, parameter_name, value):
        """Fill in the input value of the parameter, which has only one input value"""
        self.sb.update_text(self.get_parameter_input(parameter_name), value)
        LOGGER.info(f"Fill `{value}` for `{parameter_name}`")

    def fill_nth_input(self, parameter_name, input_index, value):
        """Fill in the nth input value of the parameter"""
        self.sb.update_text(
            self.get_parameter_nth_input(parameter_name, input_index), value
        )
        LOGGER.info(f"Fill `{value}` into `{parameter_name}`")

    def select_drop_down_option(self, parameter_name, field_index, option_name):
        """Selects a drop-down list option based on the parameter name,
        drop-down list index and option value"""
        self.sb.wait_for_element_clickable(self.get_parameter_field(parameter_name, field_index), timeout=tools_settings.CLICK_TIME_OUT)
        self.sb.click(self.get_parameter_field(parameter_name, field_index))
        self.sb.click(self.get_option(option_name))
        LOGGER.info(f"Select `{option_name}` for {parameter_name}")

    def click_add_values_btn(self, parameter_name):
        self.sb.click(self.get_add_values_btn(parameter_name))

    # Result
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def click_view_logs_btn(self):
        try:
            LOGGER.info("before click_view_json_result_btn, check the progress bar again")
            self.wait_for_horizon_progress_bar(present_timeout=1)
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.view_logs_btn, timeout=tools_settings.CLICK_TIME_OUT)
            self.sb.click(GSTWriteQueriesLocators.view_logs_btn)
        except Exception as e:
            LOGGER.info("click_view_logs_btn failed: "+str(e))
            LOGGER.info("click_view_json_result_btn, retry!")
            self.sb.click(GSTWriteQueriesLocators.view_json_result_btn)
            raise Exception(e)
        
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def click_view_json_result_btn(self):
        try:
            self.sb.wait_for_element_clickable(GSTWriteQueriesLocators.view_json_result_btn, timeout=tools_settings.CLICK_TIME_OUT)
            self.sb.click(GSTWriteQueriesLocators.view_json_result_btn)
        except Exception as e:
            LOGGER.info("click_view_json_result_btn failed: "+str(e))
            self.sb.click(GSTWriteQueriesLocators.view_json_result_btn)
            raise Exception(e)

    def get_log_content(self):
        """Get the log content"""
        return self.sb.find_element(GSTWriteQueriesLocators.log_container).text

    def check_run_query_log(self, expect_res=""):
        """Check that the logs for running queries are as expected"""
        self.click_view_logs_btn()
        self.sb.attach_allure_screenshot("check the run query result")
        if self.get_current_query_status() == QueryStatus.INSTALLED:
            if expect_res != "":
                self.sb.assert_true(self.is_expected_log(
                    self.get_log_content(), expect_res
                ), "Run installed query got unexpected error: {}".format(self.get_log_content()))
            else:
                self.sb.assert_true(self.is_expected_log(
                    self.get_log_content(), self.expected_installed_logs
                ), "Run installed query got unexpected error: {}".format(self.get_log_content()))
        elif (
            self.get_current_query_status() == QueryStatus.INTERPRETED
            or self.get_current_query_status() == QueryStatus.DRAFT
        ) and not self.is_expected_log(
            self.get_log_content(), self.expected_interpreted_logs
        ):
            LOGGER.warning(
                f"Unexpected log result was returned when running the query:{self.get_log_content()}"
            )

    expected_interpreted_logs = [
        "The feature is not supported yet in interpreted mode query.",
        "Query run successfully",
    ]
    #sometimes the query result log will update to default automatically
    #there existing an issue the query result will over by format check if the run query time is very short(<3s)
    expected_installed_logs = [
        "Query run successfully",
        "No log to show",
        "You are running queries in a cluster environment"
    ]

    def is_expected_log(self, result_log, expected_log):
        """Returns true when the log result is the expected result value"""
        if any(expected_log in result_log for expected_log in expected_log):
            return True
        else:
            return False
