import logging
from locators.tools.insights_locators import InsightLocators
from selenium.webdriver.common.by import By
from seleniumbase.common import decorators
from base.tools_basecase import ToolsBaseCase
from utils.data_util.login import LoginUtils
from os import listdir

LOGGER = logging.getLogger(__name__)
short_wait_time_out = 10
click_time_out = 60
assertTextTimeout = 60
wait_render_timeout = 120

class InsightsAppPage:
    application_create_confirm = "Created a new application."
    application_duplicate_confirm = "Duplicated the application."
    application_save_confirm = "Application saved successfully."
    application_update_confirm = "Updated the application."
    application_delete_confirm = "Deleted the application."
    page_create_confirm = "Page created successfully."
    page_change_confirm = "Page changed successfully."
    screenshot_save_confirm = "screenshot saved successfully."
    widget_save_confirm = "Widget saved successfully."
    widget_delete_confirm = "Widget deleted successfully."
    run_pattern_confirm = "Query executed successfully."
    no_data_confirm = "No data is available for configure."
    # copy_share_widget_confirm = "iframe code copied successfully."
    copy_share_widget_confirm = "Link copied successfully."
    run_algo_confirm = "Algorithm run successfully."
    application_default_name = "insights_e2e_default_app_test"
    application_default_name_filter = "insights_e2e_default_app_test1_filter"
    application_default_name_filter_under_app = "insights_e2e_default_app_test_filter_under_app"
    page_default_name = "insights_e2e_default_page_test"
    graph_widget_default_name = "insights_e2e_default_widget_test"
    widget_type = {
      "graph":"Graph",
      "bar": "Bar chart",
      "line": "Line chart",
      "pie": "Pie chart",
      "sankey": "Sankey chart",
      "table": "Table",
      "single": "Single value",
      "input": "Inputs",
      "map": "Map"
    }
    page_widget_auto_save = "Page changed successfully"
    share_app_auto_save = "Saved successfully"
    widget_auto_save = "Widget saved successfully"


    def __init__(self, sb):
        self.sb = sb

    def insight_login(self, login_way="undefined"):
        loginUtil = LoginUtils(self.sb)
        loginUtil.login(ToolsBaseCase.Insights_URL, login_way)
    
    def create_default_application_with_graph(self, app_name=application_default_name,
     page_name=page_default_name, widget_name=graph_widget_default_name):
        self.insight_login()
        # init env, delete all app
        self.delete_all_old_apps()

        self.create_application(app_name)

        #create page under application
        self.create_page(page_name)
        
        #add widget
        self.add_widget_with_type(self.widget_type["graph"], widget_name)

        #query graph data
        self.add_schema_with_ID("2")
        self.add_vertex_limit(10)
        self.add_schema_with_ID("2")
        self.run_pattern()
        self.sb.attach_allure_screenshot("run pattern result")
        self.sb.assert_text(self.run_pattern_confirm, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.run_pattern_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)) or (self.no_data_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)))

        #apply and save
        self.apply_save_widget()

    def generate_access_token_in_default_app(self, app_name=application_default_name):
        self.search_application_name(app_name)
        self.click_application_configuration_with_name(app_name)
        self.sb.click(InsightLocators.config_settings)
        self.edit_application_switch_tab_with_name()
        self.generate_new_token()

    def create_widget_auto_save(self, app_name=application_default_name,
     page_name=page_default_name, widget_name=graph_widget_default_name):
        # if exist, delete the application
        self.delete_existed_app(app_name=app_name)

        self.search_application_name("")
        self.click_create_application()
        self.update_application_name(app_name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create application")
        # asert the application created and the user guide will display in created page
        self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        # self.sb.assertIn(self.application_create_confirm, self.getText_if_element_present(InsightLocators.confirm_mess))
        self.sb.assert_text(app_name, timeout=click_time_out)

        #create page under application
        self.click_create_page()
        self.update_application_name(page_name)
        self.click_submit()
        self.assert_text_exist(page_name)
        self.sb.attach_allure_screenshot("create page under application")

        #add widget
        self.add_widget()
        self.create_widget_type(InsightLocators.graph_widget)
        self.update_widget_name(widget_name)
        self.sb.attach_allure_screenshot("add {0} widget".format(widget_name))

        #query graph data
        self.add_schema_with_ID("2")
        self.add_vertex_limit(10)
        self.add_schema_with_ID("2")
        self.run_pattern()
        self.sb.attach_allure_screenshot("run pattern result")
        self.sb.assert_text(self.run_pattern_confirm, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.run_pattern_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)) or (self.no_data_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)))

        #apply and auto save
        self.apply_widget()
        self.sb.attach_allure_screenshot("save {0} widget".format(widget_name))
        self.sb.assert_text(self.page_widget_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.page_widget_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess)))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def delete_all_old_apps(self):
        self.sb.wait_for_element(InsightLocators.search_app, timeout=60)
        self.sb.sleep(3) #improve succsss rate
        grid_list = self.sb.find_elements(InsightLocators.application_config_svg)
        LOGGER.info("all app_list lenth:" + str(len(grid_list)))
        for i in range(len(grid_list)):
            self.sb.find_elements(InsightLocators.application_config_svg)[0].click()
            self.sb.click(InsightLocators.config_delete)
            self.sb.click(InsightLocators.ok_button)
            self.sb.sleep(2)
            LOGGER.info("delete app " + str(i+1))
        self.sb.attach_allure_screenshot("delete all old application result")

    def create_and_share_default_application_with_graph(self, app_name=application_default_name,
     page_name=page_default_name, widget_name=graph_widget_default_name):

        self.delete_existed_app(app_name=app_name)
        self.search_application_name("")
        self.click_create_application()
        self.update_application_name(app_name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create application")
        # asert the application created and the user guide will display in created page
        self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        self.sb.assert_text(app_name, timeout=assertTextTimeout)

        #create page under application
        self.click_create_page()
        self.update_application_name(page_name)
        self.click_submit()
        self.assert_text_exist(page_name)
        self.sb.attach_allure_screenshot("create page under application")

        #add widget
        self.add_widget()
        self.create_widget_type(InsightLocators.graph_widget)
        self.update_widget_name(widget_name)
        self.sb.attach_allure_screenshot("add {0} widget".format(widget_name))

        #query graph data
        self.add_schema_with_ID("2")
        self.add_vertex_limit(10)
        self.add_schema_with_ID("2")
        self.run_pattern()
        self.sb.attach_allure_screenshot("run pattern result")
        self.sb.assert_text(self.run_pattern_confirm, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.run_pattern_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)) or (self.no_data_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)))

        #apply and save
        self.apply_widget()
        # self.save_application()
        self.sb.attach_allure_screenshot("save {0} widget".format(widget_name))

        #share the app
        self.sb.wait_for_element(InsightLocators.more_button, timeout=60)
        userName = "u_globaldesigner"
        self.more_page()
        self.sb.click(InsightLocators.config_share, timeout=click_time_out)
        self.sb.wait_for_element(InsightLocators.add_user_input, timeout=click_time_out)

        self.sb.type(InsightLocators.add_user_input, userName, timeout=click_time_out)
        if self.sb.is_element_present(InsightLocators.no_result):
            LOGGER.info("can't find user")
            self.sb.attach_allure_screenshot("can't find user:" +userName)
            raise Exception("can't find user:" +userName)

        self.sb.click(InsightLocators.find_user, timeout=click_time_out)
        self.sb.click(InsightLocators.add_role_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("add user:" + userName)
        self.sb.sleep(3)
        self.sb.click(InsightLocators.save_role_button, timeout=click_time_out)
        self.sb.assert_text(self.share_app_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue(self.share_app_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess))

        # login with user
        self.insight_login(login_way="global_designer")
        self.sb.wait_for_element_clickable(InsightLocators.search_app, timeout=60)

        # turn to shared app with me tab
        self.close_guide_panel()
        self.sb.wait_for_element_clickable(InsightLocators.shared_app_with_me_button, timeout=click_time_out)
        self.sb.click_nth_visible_element(InsightLocators.shared_app_with_me_button, number=2, timeout=click_time_out)
        self.search_application_name(app_name)
        grid_list = self.sb.find_elements(InsightLocators.application_grid)
        LOGGER.info("grid_list lenth:" + str(len(grid_list)))
        if len(grid_list) == 0:
            self.sb.attach_allure_screenshot("not found app:" + app_name)
            raise Exception("not found the app")
        # self.open_application_with_name(app_name)
        for i in range(len(grid_list)):
            if i == 0 and self.sb.is_element_present(InsightLocators.application_grid):
                self.sb.find_elements(InsightLocators.application_grid)[0].click()
                LOGGER.info(app_name + " clicked")

        # wait and assert the element
        self.click_widget_configuration_with_name(name=widget_name)
        if self.sb.is_element_present(InsightLocators.config_edit):
            LOGGER.info("shared successfully")
            self.sb.attach_allure_screenshot("found edit in:" + widget_name)
        else:
            self.sb.attach_allure_screenshot("not found edit in :" + widget_name)
            raise Exception("not found edit in:" + widget_name)

    def import_380_appliction(self, app_name=application_default_name):
        self.delete_existed_app(app_name=app_name)
        self.search_application_name("")
        self.click_create_application()
        self.update_application_name(app_name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create application")
        # asert the application created and the user guide will display in created page
        self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        self.sb.assert_text(app_name, timeout=assertTextTimeout)

        #import V380 application
        self.more_page()
        self.import_380_application()
        self.sb.attach_allure_screenshot("import application.json successfully screenshot")
        # assert the widget
        self.sb.assert_text("Page00", timeout=assertTextTimeout)
        self.sb.assert_text("Page01", timeout=assertTextTimeout)
        self.sb.assert_text("Page03", timeout=assertTextTimeout)

        # edit an widget
        self.click_page_with_name("Page00")
        self.click_widget_configuration_with_name(name='SankeyWidget')
        self.wait_and_click(InsightLocators.config_edit)
        self.update_widget_name("SankeyWidget_new")
        # apply and auto save
        self.apply_widget()
        self.sb.attach_allure_screenshot("save {0} widget".format("SankeyWidget"))
        self.sb.assert_text(self.widget_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.widget_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess)))

        # delete widget
        self.delete_widget("LineWidget")
        self.sb.attach_allure_screenshot("delete {0} widget".format("LineWidget"))
        self.sb.assert_text(self.page_widget_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.page_widget_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess)))

        # add widget
        self.add_widget()
        self.create_widget_type(InsightLocators.graph_widget)
        self.update_widget_name("new_widget")
        self.sb.attach_allure_screenshot("add {0} widget".format("new_widget"))
        # apply and auto save
        self.apply_widget()
        self.sb.attach_allure_screenshot("save {0} widget".format("new_widget"))
        self.sb.assert_text(self.page_widget_auto_save, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.page_widget_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess)))

        # delete application
        self.click_home_button_on_page()
        # try to delete the imported application
        self.delete_existed_app(app_name=app_name)

        # double check the application is deleted
        self.search_application_name("")
        self.search_application_name(app_name)
        LOGGER.info("app_name:" + app_name)
        grid_list = self.sb.find_elements(InsightLocators.application_grid)
        LOGGER.info("delete app, grid_list lenth:" + str(len(grid_list)))
        self.sb.assert_equal(len(grid_list), 0)

    def table_widget_conditional_styling(self, app_name=application_default_name):

        self.delete_existed_app(app_name=app_name)
        self.click_create_application()
        self.update_application_name(app_name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create application")
        # asert the application created and the user guide will display in created page
        self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        self.sb.assert_text(app_name, timeout=assertTextTimeout)

        # add widget
        self.create_page(app_name)
        self.add_widget_with_type("Table", "table_widget")
        self.sb.attach_allure_screenshot("add {0} widget".format("table_widget"))

        # run query
        self.add_schema_with_ID("2")
        self.add_vertex_limit(10)
        self.run_pattern()
        self.sb.attach_allure_screenshot("run pattern result")
        self.sb.assert_text(self.run_pattern_confirm, timeout=assertTextTimeout)
        # self.sb.assertTrue((self.run_pattern_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)) or (self.no_data_confirm in self.getText_if_element_present(
        #     InsightLocators.confirm_mess)))

        # add conditional styling
        self.sb.click_visible_elements(InsightLocators.add_conditional_styling_button, timeout=click_time_out)
        self.sb.click_visible_elements(InsightLocators.city_condition_input, timeout=click_time_out)
        if self.sb.is_element_present(InsightLocators.city_condition_no_result):
            self.sb.attach_allure_screenshot("condition no results screenshot")
            raise Exception("condition no results")

        city_name = self.sb.find_elements(InsightLocators.get_city_name)[1].text
        LOGGER.info("city_name: " + city_name)
        self.sb.find_elements(InsightLocators.city_condition_value_input)[1].clear()
        self.sb.find_elements(InsightLocators.city_condition_value_input)[1].send_keys(city_name)
        self.sb.click_visible_elements(InsightLocators.city_condition_OK_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("input condition results screenshot")
        self.apply_widget()


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def delete_existed_app(self, app_name=""):
        self.sb.wait_for_element_clickable(InsightLocators.search_app, timeout=60)
        self.sb.sleep(3)  # wait for input element
        try:
            # if exist, delete the application
            self.search_application_name(app_name)
            LOGGER.info("app_name:" + app_name)
            grid_list = self.sb.find_elements(InsightLocators.application_grid)
            LOGGER.info("grid_list lenth:" + str(len(grid_list)))
            for i in range(len(grid_list)):
                if self.sb.is_element_present(InsightLocators.application_grid):
                    self.click_application_configuration_with_name(app_name)
                    self.sb.click(InsightLocators.config_delete)
                    self.sb.click(InsightLocators.ok_button)
                    self.sb.attach_allure_screenshot("delete existed application result screenshot")
                    self.sb.wait_for_element_not_visible(InsightLocators.ok_button, timeout=wait_render_timeout)
                else:
                    LOGGER.info(app_name + " not existed")
        except Exception as e:
            self.sb.attach_allure_screenshot("delete existed application exception screenshot")
            LOGGER.info("delete_existed_app exception: "+str(e))
        finally:
            self.search_application_name("")
            self.sb.type(InsightLocators.search_app, "")

    def init_access_token_env(self):
        self.sb.sleep(3)
        if self.sb.is_element_present(InsightLocators.generate_new_token_delete_button):
            self.sb.click(InsightLocators.generate_new_token_delete_button, timeout=click_time_out)
            self.sb.wait_for_element_clickable(InsightLocators.generate_new_token_button, timeout=click_time_out)
        self.sb.sleep(1)

    def close_guide_panel(self):
        self.sb.sleep(2)
        if self.sb.is_element_present(InsightLocators.close_button):
            self.sb.click(InsightLocators.close_button)
        self.sb.sleep(1)

    def close_app_panel(self):
        self.sb.attach_allure_screenshot("before close_app_panel screenshot")
        if self.sb.is_element_present(InsightLocators.close_app_panel_button):
            self.sb.click(InsightLocators.close_app_panel_button)
        self.sb.sleep(1)

    def click_create_application(self):
        self.close_guide_panel()
        self.sb.click(InsightLocators.addapp_button)

    def click_submit(self):
        self.wait_and_click(InsightLocators.submit_button)
        self.sb.attach_allure_screenshot("click submit button screenshot")
        self.wait_app_panel_circle_progress_bar()
        self.sb.wait_for_element_not_visible(InsightLocators.cancel_button, timeout=click_time_out)
        LOGGER.info("wait_for_element_not_visible: cancel_button")
        self.close_app_panel()

    def getText_if_element_present(self, selector):  
        if self.sb.assert_element_present(selector, timeout=60):
            res = self.sb.get_text_content(selector, timeout=30)
            LOGGER.info(f"return result: {res}")
            return res
        else:
            self.sb.attach_allure_screenshot("wait pop window failed screenshot")
            LOGGER.info("Not get pop window content, since don't see pop window until 60s")
            return ""

    def update_application_name(self, app_name):
        self.sb.sleep(3)
        self.close_guide_panel()
        self.sb.type(InsightLocators.app_name, app_name,  timeout=5)     
        self.sb.sleep(3)   

    #select the graph name as provide name, and will use default one if not exists
    def select_graph_name(self, graph_name):
        locator_graph="//li[@role='option']/div[text()='" + graph_name + "']"
        self.sb.click(InsightLocators.select_default_graph) 
        self.sb.sleep(2)
        if(self.sb.is_element_present(locator_graph)):
            self.sb.click(locator_graph)
        #close the graph dropdown anyway
        self.sb.click(InsightLocators.app_name)
        self.sb.sleep(1)  

    def search_user_name(self, user_name):
        return "//span[text()='" +user_name+ "']"

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=32)
    def search_application_name(self, app_name):
        self.close_guide_panel()
        self.sb.wait_for_element_clickable(InsightLocators.search_app, timeout=wait_render_timeout)
        self.sb.type(InsightLocators.search_app, app_name)

    def click_application_configuration_with_name(self, name):
        self.sb.wait_for_element_clickable(InsightLocators.search_app, timeout=wait_render_timeout)
        self.sb.wait_for_element_clickable("//span[text()='"+ name +"']/../div", timeout=wait_render_timeout)
        self.sb.click("//span[text()='"+ name +"']/../div", timeout=click_time_out)

    def click_page_with_name(self, name):
        self.close_guide_panel()
        self.sb.click("//div[text()='"+ name +"']", timeout=click_time_out)
        self.sb.sleep(2)

    def click_filter_with_widgetname(self, name):
        self.close_guide_panel()
        self.sb.click("//div[text()='"+ name +"']" + InsightLocators.filter_button_prefix, timeout=click_time_out)
        self.sb.sleep(2)
    
    def click_with_text_name(self, name):
        self.sb.sleep(1)
        locator_text = "//div[text()='"+ name +"']"
        if(self.sb.assert_element_present(locator_text, timeout=assertTextTimeout)):
            self.sb.click(locator_text)
        self.sb.sleep(1)
    
    def open_other_application_panel(self):
        self.close_guide_panel()
        self.sb.click_nth_visible_element(InsightLocators.shared_app_with_me_button, number=2, timeout=click_time_out)
        # self.sb.click(InsightLocators.other_application_button)   # css has changed
        self.sb.sleep(2)

    def open_first_application(self):
        if(self.sb.assert_element_visible(InsightLocators.first_application_area, timeout=click_time_out)):
            self.sb.find_elements(InsightLocators.first_application_area)[0].click()
            self.sb.sleep(2)
        else:
            LOGGER.info(f"no application create by other, locator: {InsightLocators.first_application_area}")

    def open_application_with_name(self, name):
        self.sb.click("//span[text()='"+ name +"']/../../a")
        self.sb.sleep(2)

    # General/Security
    def edit_application_switch_tab_with_name(self, name="Security"):
        self.sb.wait_for_element_clickable('//button[text()="'+name+'"]', timeout=click_time_out)
        self.sb.click('//button[text()="'+name+'"]', timeout=click_time_out)

    def generate_new_token(self, db_user_name="u_localadmin", db_psd="localadmin"):
        self.init_access_token_env()
        self.sb.wait_for_element_clickable(InsightLocators.generate_new_token_button, timeout=click_time_out)
        self.sb.click(InsightLocators.generate_new_token_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("init env screenshot")
        # add username and psd
        self.sb.type(InsightLocators.generate_new_token_input_username_button, db_user_name, timeout=click_time_out)
        self.sb.type(InsightLocators.generate_new_token_input_psd_button, db_psd, timeout=click_time_out)
        self.sb.wait_for_element_clickable(InsightLocators.generate_new_token_submit_button, timeout=click_time_out)
        self.sb.click(InsightLocators.generate_new_token_submit_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("add username and psd")
        # assert result
        self.sb.assert_element_present(InsightLocators.generate_new_token_delete_button, timeout=click_time_out)
        self.sb.attach_allure_screenshot("generate access token successfully")
        self.close_app_panel()


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def click_create_page(self):
        self.close_guide_panel()
        self.sb.click(InsightLocators.create_page, timeout=click_time_out)

    def assert_text_exist_span(self, name, timeout=assertTextTimeout):
        self.sb.sleep(1)   
        self.sb.assert_element_present("//span[text()='"+ name +"']", timeout=timeout)

    def assert_button_exist(self, name, timeout=assertTextTimeout):
        self.sb.sleep(1)   
        self.sb.assert_element_present("//button[text()='"+ name +"']", timeout=timeout)

    def assert_text_exist(self, name, timeout=assertTextTimeout):
        self.sb.sleep(1)   
        self.sb.assert_element_present("//div[text()='"+ name +"']", timeout=timeout)

    def assert_text_not_exist(self, name):
        self.sb.sleep(1)   
        self.sb.assert_element_not_present("//div[text()='"+ name +"']", timeout=assertTextTimeout)
    
    def save_application(self):
        self.close_guide_panel()
        self.sb.click(InsightLocators.save_button, timeout=click_time_out)
        self.sb.sleep(1)   

    def more_page(self):
        self.close_guide_panel()
        self.sb.click(InsightLocators.more_button, timeout=click_time_out)
        self.sb.sleep(1)

    def click_home_button_on_page(self):
        self.close_guide_panel()
        self.sb.wait_for_element_clickable(InsightLocators.home_button, timeout=click_time_out)
        self.sb.click(InsightLocators.home_button, timeout=click_time_out)
        self.sb.sleep(1)

    def import_380_application(self):
        # upload local files
        filePath = "./data/insights/"
        LOGGER.info(listdir(filePath))
        for f in listdir(filePath):
            LOGGER.info(f)
            self.sb.choose_file('input[type="file"]', filePath + f)
            LOGGER.info(self.getText_if_element_present(InsightLocators.confirm_mess))
            self.sb.assert_text("Page changed successfully", timeout=assertTextTimeout)
            # self.sb.assertTrue(("Page changed successfully" in self.getText_if_element_present(InsightLocators.confirm_mess)))


    def add_widget(self):
        self.close_guide_panel()
        self.sb.click(InsightLocators.create_widget, timeout=click_time_out)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def create_widget_type(self, type):
        self.sb.click(type)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def create_widget_type_with_name(self, name):
        self.sb.click(InsightLocators.type_widget.format(name))
        self.sb.sleep(1)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_show_schema(self):
        self.select_graph()
        self.sb.click(InsightLocators.pattern_input, timeout=click_time_out)
        self.sb.sleep(1) 
        self.sb.click(InsightLocators.show_schema, timeout=click_time_out)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def select_graph(self, graph="MyGraph"):
        try:
            self.close_guide_panel()
            elements = self.sb.find_elements("//div[text()='" + graph + "']")
            if len(elements) == 1:
                LOGGER.info("right graph, return")
                return
            else:
                LOGGER.info("wrong graph, begin to select graph")
                self.sb.wait_for_element_clickable(InsightLocators.select_graph, timeout=click_time_out)
                self.sb.click(InsightLocators.select_graph)
                self.sb.sleep(2)
                self.sb.click("//div[text()='" + graph + "']")
                self.sb.sleep(1)
        except Exception as e:
            self.sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_schema_with_ID(self, value):
        try:
            self.close_guide_panel()
            self.select_graph()
            self.sb.wait_for_element_clickable(InsightLocators.pattern_input, timeout=click_time_out)
            self.sb.click(InsightLocators.pattern_input)
            self.sb.sleep(2)
            self.sb.click("//ul[@aria-label='Menu']/li[" + value + "]")
            self.sb.sleep(1)
        except Exception as e:
            self.sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_conditional_styling(self, value):
        try:
            self.close_guide_panel()
            self.sb.wait_for_element_clickable(InsightLocators.pattern_input, timeout=click_time_out)
            self.sb.click(InsightLocators.pattern_input)
            self.sb.sleep(2)
            self.sb.click("//ul[@aria-label='Menu']/li[" + value + "]")
            self.sb.sleep(1)
        except Exception as e:
            self.sb.refresh()
            raise Exception(e)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_schema_with_Name(self, value):
        self.close_guide_panel()
        self.select_graph()
        self.sb.click(InsightLocators.pattern_input)
        self.click_with_text_name(value)

    def apply_widget(self):
        self.sb.click(InsightLocators.apply_widget)
        self.sb.sleep(1)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def update_widget_name(self, widget_name):
        self.close_guide_panel()
        self.sb.type(InsightLocators.widget_name_input, widget_name)
        self.sb.sleep(2)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def click_widget_configuration_with_name(self, name):
        self.sb.wait_for_element_clickable("//div[text()='"+ name +"']/../div[2]/div[1]", timeout=click_time_out)
        self.sb.click("//div[text()='"+ name +"']/../div[2]/div[1]")
        self.sb.sleep(1)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def add_vertex_limit(self, num):
        self.sb.click(InsightLocators.add_filter)
        self.sb.sleep(1) 
        self.sb.click(InsightLocators.vertex_limit)
        self.sb.sleep(1) 
        self.sb.type(InsightLocators.int_input, num)
        self.sb.sleep(1)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def run_pattern(self):
        self.sb.click(InsightLocators.pattern_run_button)
        self.run_pattern_proceed()

    def run_pattern_proceed(self):
        self.sb.sleep(1)
        if(self.sb.is_element_present(InsightLocators.proceed_button)):
            self.sb.click(InsightLocators.proceed_button)

    def open_graph_analytics_panel(self):
        self.sb.click(InsightLocators.open_algorithm_panel)
        self.sb.sleep(1) 
    
    def select_algo_query(self, algo_name):
        self.sb.click(InsightLocators.open_algo_dropdown) 
        self.sb.sleep(1)
        if(algo_name == "PageRank"):
            self.sb.click(InsightLocators.pagerank_query)
        elif(algo_name == "Louvain"):
            self.sb.click(InsightLocators.louvain_query)
        self.sb.sleep(1)  

    def run_algorithm(self):
        self.sb.sleep(1) 
        self.sb.click(InsightLocators.run_algo_button)
        self.sb.sleep(1) 

    def check_app_existed(self, app_name):
        self.search_application(app_name)
        grid_list = self.sb.find_elements(InsightLocators.application_config_svg)
        LOGGER.info("search app_list lenth:" + str(len(grid_list)))
        self.search_application_name("")
        self.sb.type(InsightLocators.search_app, "")
        self.sb.attach_allure_screenshot("restore app page")
        if len(grid_list) > 0:
            return True
        else:
            return False


    def close_filter(self):
        self.sb.sleep(1)
        self.sb.click(InsightLocators.close_filter)
        self.sb.sleep(1)

    def assert_widget_result(self, res):
        # self.sb.assert_text(res, timeout=assertTextTimeout)
        self.sb.assertTrue((res in self.getText_if_element_present(
            InsightLocators.confirm_mess)) or (self.page_change_confirm in self.getText_if_element_present(
            InsightLocators.confirm_mess)) or (self.screenshot_save_confirm in self.getText_if_element_present(
            InsightLocators.confirm_mess)))

    def assert_page_create(self):
        self.close_guide_panel()
        self.sb.assertIn(self.page_create_confirm, self.getText_if_element_present(
            InsightLocators.confirm_mess))

    def apply_save_widget(self):
        self.apply_widget()
        self.sb.attach_allure_screenshot("save widget")
        self.assert_widget_result(self.widget_save_confirm)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def create_application(self, name):
        try:
            self.delete_existed_app(app_name=name)
            self.click_create_application()
            self.update_application_name(name)
            self.click_submit()
            self.sb.attach_allure_screenshot("create application screenshot")
            # asert the application created and the user guide will display in created page
            self.sb.assert_text(name, timeout=assertTextTimeout)
            self.sb.assert_element_present(InsightLocators.guide_img, By.XPATH, timeout=assertTextTimeout)
        except Exception as e:
            LOGGER.info(str(e))
            if self.sb.is_element_present(InsightLocators.homepage):
                self.sb.click(InsightLocators.homepage)
            raise Exception(e)


    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def create_page(self, name):
        self.click_create_page()
        self.update_application_name(name)
        self.click_submit()
        self.sb.attach_allure_screenshot("create page")
        self.assert_text_exist(name)

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def delete_application(self, name):
        try:
            self.search_application(name)
            self.click_application_configuration_with_name(name)
            self.sb.click(InsightLocators.config_delete)
            self.sb.click(InsightLocators.ok_button)
            self.sb.assertIn(self.application_delete_confirm, self.getText_if_element_present(
                InsightLocators.confirm_mess))
            self.sb.attach_allure_screenshot("delete application result")
        except Exception as e:
            LOGGER.info("delete application exception:" + str(e))
            self.sb.attach_allure_screenshot("delete application exception screenshot")
        finally:
            self.search_application_name("")
            self.sb.type(InsightLocators.search_app, "")

    def search_application(self, name):
        try:
            self.search_application_name(name)
            self.sb.assert_element_present(InsightLocators.app_number)
            self.sb.attach_allure_screenshot("search application result")
            self.sb.sleep(2)
        except Exception as e:
            self.sb.attach_allure_screenshot("search_application exception screenshot")
            LOGGER.info(str(e))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def duplicate_application(self, name):
        self.click_application_configuration_with_name(name)
        self.sb.click(InsightLocators.config_duplicate)
        self.sb.assertIn(self.application_duplicate_confirm,
                      self.getText_if_element_present(InsightLocators.confirm_mess))
        self.sb.attach_allure_screenshot("duplicate application result")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def change_application_name(self, current_name, update_name):
        self.click_application_configuration_with_name(current_name)
        self.sb.click(InsightLocators.config_settings)
        self.update_application_name(update_name)
        self.click_submit()
        # self.sb.assertIn(self.application_update_confirm, self.getText_if_element_present(
        #     InsightLocators.confirm_mess))
        self.sb.assert_text(update_name, timeout=assertTextTimeout)
        self.sb.attach_allure_screenshot("update application result")

    def add_widget_with_type(self, type, name):
        self.add_widget()
        self.create_widget_type_with_name(type)
        self.assert_text_exist("Widget type")
        self.update_widget_name(name)
        self.sb.attach_allure_screenshot("add {0} widget".format(name))

    #  init env: should stay in page/widget of insights
    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def share_widget(self, app_name="", widget_name=""):
        loginUtil = LoginUtils(self.sb)
        if loginUtil.is_oncloud():
            #  todo skip templately, wait add db user in TG cloud
            LOGGER.info("share widget on TG cloud")

        else:
            LOGGER.info("share widget on-prem")
            self.click_home_button_on_page()
            self.generate_access_token_in_default_app(app_name)
            self.open_application_with_name(app_name)
            self.click_widget_configuration_with_name(widget_name)
            self.sb.click(InsightLocators.config_share)
            self.sb.attach_allure_screenshot("give the share page info")
            self.assert_text_exist("Share Widget")
            URL_text= self.sb.find_element(InsightLocators.widget_share_URL).get_attribute('value')
            LOGGER.info("shared link: " + URL_text)
            self.sb.click(InsightLocators.widget_share_copy_button)
            self.assert_widget_result(self.copy_share_widget_confirm)
            self.sb.click(InsightLocators.close_share_widget)
            # assert share link can be opened successfully
            self.sb.open_new_window()
            self.sb.open(URL_text)
            # self.sb.switch_to_newest_window()

            self.sb.wait_for_element_visible(InsightLocators.assert_shared_link_elements, timeout=wait_render_timeout)
            self.sb.attach_allure_screenshot("open shared widget done, see the elements")
            try:
                elements = self.sb.find_elements(InsightLocators.assert_shared_link_elements)
                LOGGER.info("shared widget find len(elements): " + str(len(elements)))
                if len(elements) == 0:
                    self.sb.attach_allure_screenshot("open shared widget failed")
                    raise Exception("open shared widget failed")
            except Exception as e:
                raise Exception(str(e))
            finally:
                # self.sb.go_back() # didn't work because it will be u_localadmin user logining status
                self.sb.switch_to_default_window()



    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def duplicate_widget(self, name, pagename='Current Page'):
        try:
            self.click_widget_configuration_with_name(name)
            loginUtil = LoginUtils(self.sb)
            if loginUtil.is_oncloud():
                LOGGER.info("login on TG cloud")
                self.sb.hover_on_element(InsightLocators.config_duplicate_compatible_widget)
                self.sb.sleep(1)
                pagename_element = "//li[text()='{}']".format(pagename)
                if self.sb.is_element_present(pagename_element):
                    LOGGER.info("Click Current Page")
                    self.sb.click(pagename_element)
                else:
                    self.sb.click(InsightLocators.config_duplicate_compatible_widget)
            else:
                LOGGER.info("login on-prem")
                self.sb.hover_on_element(InsightLocators.config_duplicate_compatible_widget)
                self.sb.sleep(1)
                pagename_element="//li[text()='{}']".format(pagename)
                if self.sb.is_element_present(pagename_element):
                    LOGGER.info("Click Current Page")
                    self.sb.click(pagename_element)
                else:
                    self.sb.click(InsightLocators.config_duplicate_compatible_widget)
            self.sb.attach_allure_screenshot("duplicate the widget")
            self.assert_text_exist("[duplicate] " + name)
        except Exception as e:
            # self.sb.refresh() # refresh will not work, and will update to u_localadmin logining status, can't duplicate or delete the widget
            # LOGGER.info("refresh page")
            # self.sb.sleep(3)
            self.sb.attach_allure_screenshot("duplicate_widget exception")
            raise Exception(str(e))

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def delete_widget(self, name):
        self.click_widget_configuration_with_name(name)
        self.sb.click(InsightLocators.config_delete)
        self.sb.click(InsightLocators.ok_button)
        self.assert_text_not_exist(name)
        self.sb.attach_allure_screenshot("delete widget result")
        self.assert_widget_result(self.widget_delete_confirm)


    def wait_and_click(self, css, timeout=click_time_out ):
        LOGGER.info("wait and click: " + css)
        self.sb.wait_for_element_clickable(css, timeout=click_time_out)
        self.sb.click(css, timeout=timeout)

    def wait_app_panel_circle_progress_bar(self, wait_present_time=short_wait_time_out, wait_not_visible_time=click_time_out):
        try:
            self.sb.wait_for_element_present(InsightLocators.click_submit_button_progressbar, timeout=wait_present_time)
            LOGGER.info("wait_click_submit_button_progressbar present PASS")
            self.sb.wait_for_element_not_present(InsightLocators.click_submit_button_progressbar, timeout=wait_not_visible_time)
            LOGGER.info("wait_click_submit_button_progressbar not visible PASS")
        except Exception as e:
            LOGGER.info("wait_click_submit_button_progressbar exception: " + str(e))
            self.sb.attach_allure_screenshot("wait_click_submit_button_progressbar exception screenshot")

    @decorators.retry_on_exception(tries=3, delay=1, backoff=2, max_delay=5)
    def share_application_with_name(self, appname, username):
        self.click_application_configuration_with_name(appname)
        self.sb.click(InsightLocators.config_share)
        self.sb.wait_for_element(InsightLocators.add_user_input, timeout=click_time_out)
        if not self.sb.is_element_present(InsightLocators.contain_span_text.format(username)):
            self.sb.type(InsightLocators.add_user_input, username, timeout=click_time_out)
            if self.sb.is_element_present(InsightLocators.no_result):
                LOGGER.info("can't find user {0}".format(username))
                self.sb.attach_allure_screenshot("can't find user:" + username)
                raise Exception("can't find user:" + username)
            self.sb.sleep(1)
            self.sb.click(InsightLocators.find_user, timeout=click_time_out)
            self.sb.click(InsightLocators.add_role_button, timeout=click_time_out)
            self.sb.sleep(2)
            self.sb.attach_allure_screenshot("add user:" + username)
            self.sb.click(InsightLocators.save_role_button, timeout=click_time_out)
            self.sb.sleep(1)
            self.sb.assert_text(self.share_app_auto_save, timeout=assertTextTimeout)
            # self.sb.assertTrue(self.share_app_auto_save in self.getText_if_element_present(InsightLocators.confirm_mess))
        else:
            self.sb.click(InsightLocators.done_button, timeout=click_time_out)











