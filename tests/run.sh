#!/bin/bash

##############################################################
# This script triggers cloud e2e testing managed by QA
# It can be offline, that is, does not require the support
# of Github Action or Jenkins, the purpose is to facilitate
# porting and local debugging. Tools-related tests are
# performed on the Cluster on TGCloud by default.
##############################################################

while getopts "E:H:M:N:R:F:C:I:J:V:B:S:" flag; do
    case "${flag}" in
    E) TEST_ENV=${OPTARG} ;;
    H) HEADER=${OPTARG} ;;
    M) MARKER=${OPTARG} ;;
    N) N_CPUS=${OPTARG} ;;
    R) RERUNS=${OPTARG} ;;
    F) FoT_PARAM=${OPTARG} ;;
    C) CLOUD_PARAM=${OPTARG} ;;
    I) JOB_ID=${OPTARG} ;;
    J) JOB_NAME=${OPTARG} ;;
    V) VERSION=${OPTARG} ;;
    B) BROWSER=${OPTARG} ;;
    S) SKIP_TEST=${OPTARG} ;;
    \?)
        printf "[Usage] $(date '+%F %T') -E <TEST_ENV> -H <HEADER> -M <MARKER> -N <N_CPUS> -R <RERUNS> -F <FoT_PARAM> -C <CLOUD_PARAM> -I <JOB_ID> -J <JOB_NAME> -V <VERSION> -B <BROWSER> -S <SKIP_TEST>\n" >&2
        exit 1
        ;;
    esac
done

if [[ $# -lt 1 ]]; then
    echo "Warning: The input parameter is not correct!"
    printf "[Usage] $(date '+%F %T') -E <TEST_ENV> -H <HEADER> -M <MARKER> -N <N_CPUS> -R <RERUNS> -F <FoT_PARAM> -C <CLOUD_PARAM> -I <JOB_ID> -J <JOB_NAME> -V <VERSION> -B <BROWSER> -S <SKIP_TEST>\n"
    exit 1
fi

# init
init() {
    echo "tools_e2e - Step - init - Init environment"
    current_path=$(cd "$(dirname "$0")" && pwd)
    cd "${current_path}" || exit
    # shellcheck source=/dev/null
    source "${current_path}/scripts/sys_utils.sh"
    E2E_RESULT_DIR="/tmp/e2e"
    TOOLS_E2E_LOG_DIR="${E2E_RESULT_DIR}/log/tools"
    JUNIT_DATA_DIR="${current_path}/junit"
    ENVIRONMENT_FILE="${current_path}/environment.properties"
    sudo rm -rf ${JUNIT_DATA_DIR}
    sudo rm -rf ${ENVIRONMENT_FILE}
    sudo rm -rf ${TOOLS_E2E_LOG_DIR}
    mkdir -p "${TOOLS_E2E_LOG_DIR}"
    TOOLS_E2E_PREPARE_ENV_LOG="$TOOLS_E2E_LOG_DIR/tools_prepare_env.log"
    TOOLS_E2E_UPDATE_INPUT_LOG="$TOOLS_E2E_LOG_DIR/tools_update_input.log"
    TOOLS_ALLURE_DIR="${current_path}/alluredir/tools"
    TOOLS_COMMANDS="$E2E_RESULT_DIR/pytest_command/tools_commands_file"
    TOOLS_E2E_RESULT_SUMMARY="$TOOLS_E2E_LOG_DIR/tools_e2e_result_summary"
    START_TIME=$(date +"%Y-%m-%d %H:%M:%S")
}

# Cleanup test results
cleanup() {
    echo "tools_e2e - Step - cleanup - Cleanup old test results"
    sudo rm -rf "${TOOLS_ALLURE_DIR}"
    sudo rm -rf "$TOOLS_E2E_LOG_DIR/*"
    sudo rm -rf "${current_path}/data/extensions/modheader/"
    # Initial environment properties
    printf 'TOOLS_E2E_ROOT=%s\n' "$current_path" >environment.properties
    printf 'TOOLS_E2E_LOG_DIR=%s\n' "$TOOLS_E2E_LOG_DIR" >>environment.properties
    printf 'TOOLS_ALLURE_DIR=%s\n' "$TOOLS_ALLURE_DIR" >>environment.properties
}

# Check the provided test env is working or not
check_testenv(){
  status=$(curl -k -m 5 -o /dev/null -s -w %{http_code} $TEST_ENV)
	if [ $status != "200" ];then
		echo "unable to access for: $TEST_ENV, will exit the process"
		exit 1
	fi
  # Check if OS is centos7 will skip the E2E
  if [ -f /etc/os-release ]; then
      # get OS version
      os_info=$(cat /etc/os-release)

      # check CentOS version 7
      if echo "$os_info" | grep -q "CentOS Linux 7"; then
          echo "tools_e2e skipped: The OS is CentOS 7"
          exit 0
      else
          echo "tools_e2e continue: The OS is not CentOS 7"
      fi
  else
      echo "/etc/os-release file not found, continuing"
      echo "tools_e2e continue: The OS is not CentOS 7"
  fi
}

# Run prepare_env to check and install tools
prepare_env() {
    echo "tools_e2e - Step - prepare_env - Check and install tools"
    echo "$TOOLS_E2E_PREPARE_ENV_LOG"
    echo "User pass browser parameter: $BROWSER"
    if [ -z "$BROWSER" ] || [ "$BROWSER" == "none" ]; then
      export Browser="chrome"
      echo "Before prepare_env, Browser is null and set to default chrome"
    else
      export Browser="$BROWSER"
      echo "Before prepare_env, Browser is set to: $Browser"
    fi
    bash "${current_path}/scripts/prepare_env.sh" >"$TOOLS_E2E_PREPARE_ENV_LOG" 2>&1
}

# Check the input parameters and update the test data
update_input() {
    echo "tools_e2e - Step - update_input - Check and update inputs"
    N_CPUS="${N_CPUS:-1}"
    RERUNS="${RERUNS:-0}"
    echo "Update the parameters."
    printf 'TEST_ENV="%s"\nHEADER="%s"\nMARKER=\"%s\"\nN_CPUS=%s\nRERUNS=%s\nFoT_PARAM="%s"\nCLOUD_PARAM="%s"\nBROWSER="%s"\nSKIP_TEST="%s"\n' \
        "$TEST_ENV" "$HEADER" "$MARKER" "$N_CPUS" "$RERUNS" "$FoT_PARAM" "$CLOUD_PARAM" "$BROWSER" "$SKIP_TEST" >>environment.properties
    printf 'extensions=%s\n' "$current_path/data/extensions" >>environment.properties
    printf 'JOB_NAME=%s\nJOB_ID=%s\nSTART_TIME="%s"\nVERSION=%s\n' "$JOB_NAME" "$JOB_ID" "$START_TIME" "$VERSION" >>environment.properties
    echo "$TOOLS_E2E_UPDATE_INPUT_LOG"
    bash "${current_path}/scripts/update_input.sh" >"${TOOLS_E2E_UPDATE_INPUT_LOG}" 2>&1 || {
        echo 'update_input.sh failed'
        exit 1
    }
    # shellcheck source=/dev/null
    source environment.properties
    parameter_string="TEST_ENV: $TEST_ENV; HEADER: ${HEADER:-none}; MARKER: ${MARKER:-none}; N_CPUS: $N_CPUS; RERUNS: $RERUNS; FoT_PARAM: ${FoT_PARAM:-none}; CLOUD_PARAM: ${CLOUD_PARAM:-none}; BROWSER: ${BROWSER:-none}; SKIP_TEST: ${SKIP_TEST:-none};"
    echo '============================= test parameter list =============================='
    echo "${parameter_string}"
    echo '================================================================================'
}

# update the config value into tools_test_data.json
change_config() {
    echo "tools_e2e - Step - change_config - pdate the config value into tools_test_data.json"
    local CONFIG_FILE="${current_path}/config/tools_test_data.json"
    local CONFIG_FILE_TEMP="${current_path}/config/tools_test_data_temp.json"
    echo "Start change the config file: $CONFIG_FILE ....."
    jq '.test_env = "'${TEST_ENV}'"' $CONFIG_FILE >$CONFIG_FILE_TEMP
    mv -f $CONFIG_FILE_TEMP $CONFIG_FILE
}

# Run e2e test cases
run_tests() {
    echo "tools_e2e - Step - run_tests - Run tools e2e test cases"
    # shellcheck source=/dev/null
    source environment.properties
    # shellcheck source=/dev/null
    source "e2e_env/bin/activate"
    export HEADER="$HEADER"
    export FOT_HEADER="$FoT_PARAM"
    export TOOLS_E2E_ROOT="$TOOLS_E2E_ROOT"
    # Generate commands
    python3 "${TOOLS_E2E_ROOT}/scripts/marker/marker_analyzer.py"
    # Run cloud e2e test cases
    echo "The tools pytest commands are:"
    cat $TOOLS_COMMANDS
    echo "chmod +x TOOLS_COMMANDS"
    chmod +x $TOOLS_COMMANDS
    echo "bash -xc TOOLS_COMMANDS"
    bash -x -c $TOOLS_COMMANDS
}

# check test result by checking the log files
check_result() {
    echo "tools_e2e - Step - check_result - check test result log files "
    # shellcheck source=/dev/null
    source environment.properties
    # generate the summary file
    echo "" >$TOOLS_E2E_RESULT_SUMMARY
    for file in "$TOOLS_E2E_LOG_DIR"/*result.log; do
        last_line=$(tail -n 1 "$file")
        if grep -q "pass" <<<"$last_line" && ! grep -q "fail\|error" <<<"$last_line"; then
            printf "%s: case_passed:\n %s\n\n" "$file" "$last_line" >>$TOOLS_E2E_RESULT_SUMMARY
        else
            printf "%s: case_failed:\n %s\n\n" "$file" "$last_line" >>$TOOLS_E2E_RESULT_SUMMARY
        fi
    done

    # Generate a file to record all failed test cases
    search_failure_testcase

    # Check the result and add summary for jenkins pipeline. Exit 1 if any case failed
    if grep -q "case_failed" "$TOOLS_E2E_RESULT_SUMMARY"; then
        echo "tools e2e test failed! please review the logs and contact QA Team."
        printf 'STATUS=%s\n' "failed" >>environment.properties
        echo "currentBuild.result='FAILURE'" >>"$TOOLS_E2E_RESULT_SUMMARY"
        cat "$TOOLS_E2E_RESULT_SUMMARY"
        exit 1
    else
        echo "tools e2e test passed!"
        printf 'STATUS=%s\n' "success" >>environment.properties
        echo "currentBuild.result='SUCCESS'" >>"$TOOLS_E2E_RESULT_SUMMARY"
    fi

}

search_failure_testcase(){
    # Generate a file to record all failed test cases
    python3 "${current_path}/scripts/search_failure_testcase.py"
    file_path_failure_case="$current_path/tool_e2e_failure_casename.txt"

    if [ -f "$file_path_failure_case" ]; then
        echo "The File of $file_path_failure_case exists, will move to $TOOLS_E2E_LOG_DIR."
        sudo cp -r $file_path_failure_case $TOOLS_E2E_LOG_DIR
    fi
}

push_test_result_to_server(){
    if [ -n "$VERSION" ];then
        echo "will push the test result into mit server, and the current version is $VERSION"
        bash ${current_path}/scripts/upload_results_to_web.sh
    else
        echo "the version info is empty, will skip push the result into server"
    fi
}


download_extension(){
    echo "${current_path}, ready to download_extension"
    bash "${current_path}/scripts/download_extensions.sh"
}

# main function
main() {
    init
    cleanup
    check_testenv
    prepare_env
    update_input
    download_extension
    change_config
    run_tests
    check_result
    push_test_result_to_server
}

main
