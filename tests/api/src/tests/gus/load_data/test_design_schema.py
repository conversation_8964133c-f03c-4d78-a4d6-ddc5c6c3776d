import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure
from src.common.logger import logger

from src.operation.gus import GUS

from src.api.tools_api import tools

from src.operation.design_schema import Design_Schema


class TestDesignSchema(APIBaseCase):

    login_user_data = data.get_yaml_data("general/design_schema.yml")
    export_test_data_invalid = login_user_data.get("invalid_export")
    export_test_data_valid = login_user_data.get("valid_export")
    invalid_gui_store = login_user_data.get("invalid_gui_store")
    test_data_valid = []
    test_data_invalid = []
    test_gui_store_invalid = []
    for case in export_test_data_valid:
        test_data_valid.append(
            (
                case,
                export_test_data_valid.get(case).get("params"),
                export_test_data_valid.get(case).get("status_code"),
            )
        )
    for case in export_test_data_invalid:
        test_data_invalid.append(
            (
                case,
                export_test_data_invalid.get(case).get("params"),
                export_test_data_invalid.get(case).get("status_code"),
            )
        )
    for case in invalid_gui_store:
        test_gui_store_invalid.append(
            (
                case,
                invalid_gui_store.get(case).get("params"),
                invalid_gui_store.get(case).get("status_code"),
            )
        )

    @allure.title("export with valid params")
    @allure.description(
        "TestType: positive \n" 
        "Target: Check we can export solution with invalid params \n"
        "Description: export solution with valid params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(test_data_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=1)
    def test_export_valid(self, case, params, status_code):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        header = {
            "Cookie": cookie
        }
        
        res = tools.export_solution(paras=params, headers=header)
        logger.info("the response of api is {}".format(res.status_code))
        assert res.status_code == status_code, "the returned status code is: {}, expected {}".format(res.status_code, status_code)

    @allure.title("export with invalid params")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we unable export solution with invalid params \n"
        "Description: export solution with invalid params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=1)
    def test_export_invalid(self, case, params, status_code):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        header = {
            "Cookie": cookie
        }
        
        res = tools.export_solution(paras=params, headers=header)
        logger.info("the response of api is {}".format(res.status_code))
        assert res.status_code == status_code, "the returned status code is: {}, expected {}".format(res.status_code, status_code)

    # skip this cases because need auth token.
    @allure.title("gui-store with valid and invalid params")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we unable call api with invalid params \n"
        "Description: check api with invalid params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )

    # @parameterized.expand(test_gui_store_invalid, skip_on_empty=True)
    # @pytest.mark.high
    # @pytest.mark.smoke
    # @pytest.mark.api
    # @pytest.mark.gus
    # @pytest.mark.solution
    # @pytest.mark.run(order=1)
    def test_gui_store(self, case, params, status_code):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        header = {
            "Cookie": cookie
        }

        res = tools.gui_store(paras=params, headers=header)
        logger.info("the response of api is {}".format(res.message))
        logger.info("the response of api is {}".format(res.status_code))
        assert res.status_code == status_code, "the returned status code is: {}, expected {}".format(res.status_code, status_code)


    @allure.title("login tools and import solution negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we can login tools and import solution failed with wrong payload  \n"
        "Description: login tools and import solution failed with wrong payload \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=2)
    def test_login_gst_import_solution_with_special_characters(self):
        login = Login()
        result = login.login()
        my_cookies = result.get_cookies()
        ds = Design_Schema()
        header = {
                'Cookie': my_cookies,
                'Content-Type': 'text/csv',
                "Content-Disposition": 'form-data; name="file!<>?*"; filename="Contact.csv"'
            }
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import", cookies="", header=header))
        result.assert_result(state_code=400, error_state=True, message="Invalid payload.")

    @allure.title("login tools and import solution negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we can login tools and import solution failed with wrong payload  \n"
        "Description: login tools and import solution failed with wrong payload \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=3)
    def test_login_gst_import_solution_with_embedded_single_quote(self):
        login = Login()
        result = login.login()
        my_cookies = result.get_cookies()
        ds = Design_Schema()
        header = {
                'Cookie': my_cookies,
                'Content-Type': 'text/csv',
                'Content-Disposition': 'form-data; name="file\'s"; filename="Contact.csv"'
            }
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import", cookies="", header=header))
        result.assert_result(state_code=400, error_state=True, message="Invalid payload.")

    @allure.title("login tools and import solution negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we can login tools and import solution failed with wrong payload  \n"
        "Description: login tools and import solution failed with wrong payload \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.solution
    @pytest.mark.run(order=2)
    def test_login_gst_import_solution_with_field_size_test(self):
        login = Login()
        result = login.login()
        my_cookies = result.get_cookies()
        ds = Design_Schema()
        header = {
                'Cookie': my_cookies,
                'Content-Type': 'text/csv',
                "Content-Disposition": 'form-data; name="file99999999999999999999999999"; filename="Contact.csv"'
            }
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import", cookies="", header=header))
        result.assert_result(state_code=400, error_state=True, message="Invalid payload.")
