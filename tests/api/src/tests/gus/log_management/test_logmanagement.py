import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.api.tools_api import tools
from src.operation.gus import GUS
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure
import time


class TestLogManage(APIBaseCase):

    configs_data = data.get_yaml_data("gus/log_management.yml")
    valid_data = configs_data.get("valid_v2_body")
    invalid_data = configs_data.get("invalid_v2_body")
    v2_body_valid = []
    v2_body_invalid = []
    for case in valid_data:
        v2_body_valid.append(
            (
                case,
                valid_data.get(case).get("query"),
                valid_data.get(case).get("variables"),
                valid_data.get(case).get("message")
            )
        )
    
    for case in invalid_data:
        v2_body_invalid.append(
            (
                case,
                invalid_data.get(case).get("query"),
                invalid_data.get(case).get("variables"),
                invalid_data.get(case).get("message")
            )
        )

    @allure.title("search/view log with valid conditions")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check the log content can be retrive with valid conditions \n"
        "Description: The log content can be retrive with valid conditions \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-23 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(v2_body_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.requestlog2
    @pytest.mark.run(order=1)
    def test_execute_log_operation_with_valid_conditions(self, case, query, variables, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        # check the returned log content
        gus = GUS()
        check_res= gus.execute_v2(cookie, query, variables)
        check_res.assert_v2_result(data=message)

    
    @allure.title("search/view log with invalid conditions")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the log content can't be retrive with invalid conditions \n"
        "Description: The log content can't be retrive with invalid conditions \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-19 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(v2_body_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.requestlog
    @pytest.mark.run(order=1)
    def test_execute_log_operation_with_invalid_conditions(self, case, query, variables, message):
        login = Login()
        result = login.login()
        cookie = result.get_cookies()
        # check the returned errors
        gus = GUS()
        check_res= gus.execute_v2(cookie, query, variables)
        check_res.assert_v2_result_error(errors=message)

    @allure.title("search/view log with with invalid cookie")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check the log content can't be retrive with invalid cookie \n"
        "Description: Unable to search/view log if without cookie \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-10-23 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(v2_body_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.requestlog
    @pytest.mark.run(order=1)
    def test_view_log_content_with_invalid_cookie(self, case, query, variables, message):
        
        cookie=""
        #check the log content can't be retrive with invalid cookie
        gus = GUS()
        check_res= gus.execute_v2(cookie, query, variables)
        check_res.assert_v2_result_error(errors="request is not authenticated")



