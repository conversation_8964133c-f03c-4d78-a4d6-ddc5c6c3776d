import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
import allure

class TestVQB():

    gsql_pattern_data = data.get_yaml_data("gus/VQB_data.yml")
    get_VQB_patterns = gsql_pattern_data.get("get_VQB_patterns")
    delete_VQB_patterns = gsql_pattern_data.get("delete_VQB_patterns")
    get_VQB_patterns_list = []
    add_VQB_patterns_list = []
    delete_VQB_patterns_list = []
    for case in get_VQB_patterns:
        get_VQB_patterns_list.append(
            (
                case,
                get_VQB_patterns.get(case).get("graph_name"),
                get_VQB_patterns.get(case).get("draft"),
                get_VQB_patterns.get(case).get("params"),
                get_VQB_patterns.get(case).get("status_code"),
                get_VQB_patterns.get(case).get("error"),
                get_VQB_patterns.get(case).get("message")
            )
        )
    for case in delete_VQB_patterns:
        delete_VQB_patterns_list.append(
            (
                case,
                delete_VQB_patterns.get(case).get("graph_name"),
                delete_VQB_patterns.get(case).get("pattern_name"),
                delete_VQB_patterns.get(case).get("params"),
                delete_VQB_patterns.get(case).get("status_code"),
                delete_VQB_patterns.get(case).get("error"),
                delete_VQB_patterns.get(case).get("message")
            )
        )

    @allure.title("get graph pattern with params")
    @allure.description(
        "TestType: nagetive \n"
        "Target: Check we can get graph pattern with params \n"
        "Description: get graph pattern with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(get_VQB_patterns_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=1)
    def test_get_VQB_patterns_with_params(self, case, graph_name, draft, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.get_all_patterns("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name+params)
        else:
            result = gus.get_all_patterns(cookie, graph_name+params)
        # check error message
        result.assert_result(status_code, error, message)


    @allure.title("delete graph pattern with params")
    @allure.description(
        "TestType: nagetive \n"
        "Target: Check we can delete graph pattern with params \n"
        "Description: delete graph pattern with params \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-10-11 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @parameterized.expand(delete_VQB_patterns_list, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiqueries
    @pytest.mark.run(order=1)
    def test_delete_VQB_patterns_with_params(self, case, graph_name, pattern_name, params, status_code, error, message):
        login = Login()
        gus = GUS()
        cookie = ""
        if params != "invalid_cookies":
            cookie = login.login().get_cookies()

        if params == "invalid_cookies":
            result = gus.delete_VQB_pattern("TigerGraphApp=a63007de-d83d-40c7-84f9-527022ea710a", graph_name, pattern_name)
        else:
            result = gus.delete_VQB_pattern(cookie, graph_name, pattern_name+params)
        # check error message
        result.assert_result(status_code, error, message)
