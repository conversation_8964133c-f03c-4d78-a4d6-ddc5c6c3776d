from http import cookies
import pytest
import json
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.operation.gus import GUS
from src.common.logger import logger
from parameterized import parameterized
from src.common.api_basecase import APIBaseCase
import allure


class TestExplorationResults(APIBaseCase):
    exploration_results_data = data.get_yaml_data("gus/api_exploration_results.yml")
    get_exploration_res = exploration_results_data.get("get_exploration_result")
    post_exploration_res = exploration_results_data.get("post_exploration_result")
    delete_exploration_res = exploration_results_data.get("delete_exploration_result")
    exploration_data_body = post_exploration_res.get("save_exploration").get("data")
    put_exploration_res = exploration_results_data.get("put_exploration_result")

    ## negative case data
    invalid_get_all_exploration_res = exploration_results_data.get("invalid_get_all_exploration_results")
    invalid_get_exploration_res = exploration_results_data.get("invalid_get_exploration_results")
    invalid_post_exploration_res = exploration_results_data.get("invalid_post_exploration_result")
    invalid_delete_exploration_res = exploration_results_data.get("invalid_delete_exploration_result")
    invalid_put_exploration_res = exploration_results_data.get("invalid_put_exploration_result")

    test_get_exploration_res = []
    test_post_exploration_res = []
    test_delete_exploration_res = []
    test_put_exploration_res = []
    test_get_exploration_res_invalid = []

    ## negative case test data
    test_get_all_exploration_res_invalid = []
    test_get_exploration_res_invalid = []
    test_post_exploration_res_invalid = []
    test_delete_exploration_res_invalid = []
    test_put_exploration_res_invalid = []

    for case in get_exploration_res:
        test_get_exploration_res.append((
            case,
            get_exploration_res.get(case).get("graph_name"),
            get_exploration_res.get(case).get("file_name")
        ))

    for case in post_exploration_res:
        test_post_exploration_res.append((
            case,
            post_exploration_res.get(case).get("graph_name"),
            post_exploration_res.get(case).get("file_name"),
            post_exploration_res.get(case).get("data")
        ))

    for case in delete_exploration_res:
        test_delete_exploration_res.append((
            case,
            delete_exploration_res.get(case).get("graph_name"),
            delete_exploration_res.get(case).get("file_name"),
            exploration_data_body
        ))

    for case in put_exploration_res:
        test_put_exploration_res.append((
            case,
            put_exploration_res.get(case).get("graph_name"),
            put_exploration_res.get(case).get("file_name"),
            exploration_data_body,
            put_exploration_res.get(case).get("change_data")
        ))

    for case in invalid_get_all_exploration_res:
        test_get_all_exploration_res_invalid.append((
            case,
            invalid_get_all_exploration_res.get(case).get("graph_name"),
            invalid_get_all_exploration_res.get(case).get("cookies"),
            invalid_get_all_exploration_res.get(case).get("status_code"),
            invalid_get_all_exploration_res.get(case).get("error"),
            invalid_get_all_exploration_res.get(case).get("message")
        ))

    for case in invalid_get_exploration_res:
        test_get_exploration_res_invalid.append((
            case,
            invalid_get_exploration_res.get(case).get("graph_name"),
            invalid_get_exploration_res.get(case).get("file_name"),
            invalid_get_exploration_res.get(case).get("cookies"),
            invalid_get_exploration_res.get(case).get("status_code"),
            invalid_get_exploration_res.get(case).get("error"),
            invalid_get_exploration_res.get(case).get("message")
        ))

    for case in invalid_post_exploration_res:
        test_post_exploration_res_invalid.append((
            case,
            invalid_post_exploration_res.get(case).get("graph_name"),
            invalid_post_exploration_res.get(case).get("file_name"),
            invalid_post_exploration_res.get(case).get("data"),
            invalid_post_exploration_res.get(case).get("cookies"),
            invalid_post_exploration_res.get(case).get("status_code"),
            invalid_post_exploration_res.get(case).get("error"),
            invalid_post_exploration_res.get(case).get("message"),
            exploration_data_body
        ))

    for case in invalid_delete_exploration_res:
        test_delete_exploration_res_invalid.append((
            case,
            invalid_delete_exploration_res.get(case).get("graph_name"),
            invalid_delete_exploration_res.get(case).get("file_name"),
            invalid_delete_exploration_res.get(case).get("cookies"),
            invalid_delete_exploration_res.get(case).get("status_code"),
            invalid_delete_exploration_res.get(case).get("error"),
            invalid_delete_exploration_res.get(case).get("message")
        ))

    for case in invalid_put_exploration_res:
        test_put_exploration_res_invalid.append((
            case,
            invalid_put_exploration_res.get(case).get("graph_name"),
            invalid_put_exploration_res.get(case).get("file_name"),
            invalid_put_exploration_res.get(case).get("data"),
            invalid_put_exploration_res.get(case).get("cookies"),
            exploration_data_body,
            invalid_put_exploration_res.get(case).get("status_code"),
            invalid_put_exploration_res.get(case).get("error"),
            invalid_put_exploration_res.get(case).get("message")
        ))

    @allure.title("Get exploration results info")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Get exploration results \n"
        "Description: Try to exploration results \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_get_all_exploration_results(self, graph_name="MyGraph"):
        login = Login()
        gus = GUS()
        login_result = login.login()
        result = gus.get_all_exploration_result(login_result.get_cookies(), graph_name)
        result.assert_no_error()

    @allure.title("Open specified exploration result")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Open specified exploration results \n"
        "Description: Try to get specified exploration results and check results \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_get_exploration_res, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_get_exploration_results(self, case, graph_name, file_name):
        login = Login()
        gus = GUS()
        login_result = login.login()
        result = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name)
        result.assert_no_error()

    @allure.title("Save exploration result")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Save exploration results  \n"
        "Description: Try to explora graph and save results  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_post_exploration_res, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_save_exploration_results(self, case, graph_name, file_name, data):
        login = Login()
        gus = GUS()
        login_result = login.login()
        ## check if the exploration result was saved
        exploration_res = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name).results
        if exploration_res:
            gus.delete_exploration_result(login_result.get_cookies(), graph_name, file_name)
        result = gus.post_exploration_result(login_result.get_cookies(), graph_name, file_name, data)
        result.assert_result(201, False, '')

        # check if the exploration result is in list
        res = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name).results
        assert res != '', "The exploration result {} was saved failed".format(file_name)

    @allure.title("Delete exploration result")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check if can delete exploration results sucessfully \n"
        "Description: Try to delete exploration results and check if it works  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_delete_exploration_res, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_delete_exploration_results(self, case, graph_name, file_name, exploration_data_body):
        login = Login()
        gus = GUS()
        login_result = login.login()
        ## check if the exploration result was existed
        exploration_res = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name).results
        if not exploration_res:
            gus.post_exploration_result(login_result.get_cookies(), graph_name, file_name, exploration_data_body)
        
        ## delete exploration result
        result = gus.delete_exploration_result(login_result.get_cookies(), graph_name, file_name)
        result.assert_no_error()

        # check if the exploration result is in list
        res = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name)
        res.assert_result(404, True, "Exploration result '{}' for graph '{}' cannot be found.".format(file_name, graph_name))

    @allure.title("Change and save exploration result")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Change exploration result and save the changes  \n"
        "Description: Try to change exploration result and then save these changes  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_put_exploration_res, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_save_exploration_result_changes(self, case, graph_name, file_name, exploration_data_body, change_data):
        login = Login()
        gus = GUS()
        login_result = login.login()
        ## check if the exploration result was existed
        exploration_res = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name).results
        if not exploration_res:
            ## create exploration result if it's not existing
            gus.post_exploration_result(login_result.get_cookies(), graph_name, file_name, exploration_data_body)
            exploration_res = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name).results

        ## change exploration result and save it
        result = gus.put_exploration_result(login_result.get_cookies(), graph_name, file_name, exploration_res, change_data)
        result.assert_no_error()

        # check if the exploration result is in list
        res = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name).results
        assert 'Province' in str(res), "change exploration result `{}` failed".format(file_name)


    @allure.title("Get exploration results info")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Get exploration results with invalid payload \n"
        "Description: Try to check error message when get exploration results with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_get_all_exploration_res_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_get_all_exploration_results_invalid(self, case, graph_name, cookies, status_code, error, message):
        login = Login()
        gus = GUS()
        login_result = login.login()
        if 'cookies' in case:
            res = gus.get_all_exploration_result(cookies, graph_name)
            res.assert_api_no_authorized()
        else:
            res = gus.get_all_exploration_result(login_result.get_cookies(), graph_name)
            res.assert_result(status_code, error, message)

    @allure.title("Get all exploration results info")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Get all exploration results with invalid payload \n"
        "Description: Try to check error message when get all exploration results with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_get_exploration_res_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_get_exploration_results_invalid(self, case, graph_name, file_name, cookies, status_code, error, message):
        login = Login()
        gus = GUS()
        login_result = login.login()
        if 'cookies' in case:
            res = gus.get_exploration_result(cookies, graph_name, file_name)
            res.assert_api_no_authorized()
        else:
            res = gus.get_exploration_result(login_result.get_cookies(), graph_name, file_name)
            res.assert_result(status_code, error, message)

    @allure.title("save exploration results info")
    @allure.description(
        "TestType: Negative \n" 
        "Target: save exploration results with invalid payload \n"
        "Description: Try to check if can save exploration results with invalid payload \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_post_exploration_res_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_post_exploration_results_invalid(self, 
                                            case, 
                                            graph_name, 
                                            file_name, 
                                            data, 
                                            cookies, 
                                            status_code,
                                            error, 
                                            message, 
                                            exploration_data_body):
        login = Login()
        gus = GUS()
        login_result = login.login()
        if 'cookies' in case:
            res = gus.post_exploration_result(cookies, graph_name, file_name, exploration_data_body)
            res.assert_api_no_authorized()
        elif data:
            res = gus.post_exploration_result(login_result.get_cookies(), graph_name, file_name, data)
            res.assert_result(status_code, error, message)
        else:
            res = gus.post_exploration_result(login_result.get_cookies(), graph_name, file_name, exploration_data_body)
            res.assert_result(status_code, error, message)

    
    @allure.title("Delete exploration result")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check if can delete exploration results with invalid payload \n"
        "Description: Try to delete exploration results with invalid payload and check if can delete successfully \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_delete_exploration_res_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=1)
    def test_delete_exploration_results_invalid(self, case, graph_name, file_name, cookies, status_code, error, message):
        login = Login()
        gus = GUS()
        login_result = login.login()
        
        if 'cookies' in case:
            result = gus.delete_exploration_result(cookies, graph_name, file_name)
            result.assert_api_no_authorized()
        else:
            result = gus.delete_exploration_result(login_result.get_cookies(), graph_name, file_name)
            result.assert_result(status_code, error, message)

    @allure.title("Change and save exploration result")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Change exploration result and save the changes  \n"
        "Description: Try to change exploration result and then save these changes  \n"
        "TestDesigner: Jing Yang \n"
        "Date: 2023-10-20 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2744"
    )
    @parameterized.expand(test_put_exploration_res_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.gus
    @pytest.mark.api
    @pytest.mark.apiexplorationresults
    @pytest.mark.run(order=100)
    def test_save_exploration_result_changes(self, 
                                            case, 
                                            graph_name, 
                                            file_name, 
                                            data, 
                                            cookies, 
                                            exploration_data_body,
                                            status_code,
                                            error,
                                            message):
        login = Login()
        gus = GUS()
        login_result = login.login()

        if 'cookies' in case:
            res = gus.put_exploration_result(cookies, graph_name, file_name, exploration_data_body)
            res.assert_api_no_authorized()
        else:
            res = gus.put_exploration_result(login_result.get_cookies(), graph_name, file_name, data )
            res.assert_result(status_code, error, message)
