import pytest
from src.common.result_base import ResultBase
from src.common.read_data import data
from src.operation.login import Login
from src.common.api_basecase import APIBaseCase
from parameterized import parameterized
import allure
from src.common.logger import logger

from src.operation.design_schema import Design_Schema


class TestLogin(APIBaseCase):

    login_user_data = data.get_yaml_data("general/login_data.yml")
    login_test_data_valid = login_user_data.get("valid_login")
    login_test_data_invalid = login_user_data.get("invalid_login")
    test_data_valid = []
    test_data_invalid = []
    for case in login_test_data_valid:
        test_data_valid.append(
            (
                case,
                login_test_data_valid.get(case).get("username"),
                login_test_data_valid.get(case).get("password"),
                login_test_data_valid.get(case).get("status_code"),
                login_test_data_valid.get(case).get("error"),
                login_test_data_valid.get(case).get("message")
            )
        )
    for case in login_test_data_invalid:
        test_data_invalid.append(
            (
                case,
                login_test_data_invalid.get(case).get("username"),
                login_test_data_invalid.get(case).get("password"),
                login_test_data_invalid.get(case).get("status_code"),
                login_test_data_invalid.get(case).get("error"),
                login_test_data_invalid.get(case).get("message")
            )
        )

    @allure.title("login tools with valid user")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check we can login tools with default tigergraph user \n"
        "Description: login tools with default user \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-21 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_valid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.preexec
    @pytest.mark.apilogin_cloud
    @pytest.mark.apilogin
    @pytest.mark.run(order=0.1)
    def test_login_valid(self, case, username, password, status_code, error, message):
        login = Login()
        result = login.login(username, password)
        result.assert_result(status_code, error, message)
        assert result.get_cookies()!="", "the cookie is null"

    @allure.title("login tools with invalid account")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we unable login tools with invalid account \n"
        "Description: login tools with invalid account \n"
        "TestDesigner: Yuling Liu \n"
        "Date: 2023-09-21 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2673"
    )
    @parameterized.expand(test_data_invalid, skip_on_empty=True)
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.onprem
    @pytest.mark.apilogin
    @pytest.mark.run(order=1)
    def test_login_invalid(self, case, username, password, status_code, error, message):
        login = Login()
        result = login.login(username, password)
        result.assert_result(status_code, error, message)

    @allure.title("login tools and import solution")
    @allure.description(
        "TestType: Positive \n" 
        "Target: Check we can login tools and import solution \n"
        "Description: login tools and import solution \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.preexec
    @pytest.mark.apilogin
    @pytest.mark.run(order=0.2)
    def test_login_gst_import_solution(self):
        login = Login()
        result = login.login()
        my_cookies = result.get_cookies()
        result.assert_result(200, False, "")
        logger.info("my_cookies: " + my_cookies)
        ds = Design_Schema()
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import", cookies=my_cookies))
        result.assert_result(message="")


    @allure.title("login tools and import solution negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we can login tools and import solution failed without cookie \n"
        "Description: login tools and import solution failed without cookie \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.apilogin
    @pytest.mark.run(order=0.3)
    def test_login_gst_import_solution_without_cookie(self):
        ds = Design_Schema()
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import", cookies=""))
        result.assert_result(state_code=401, error_state=True, message="You are not authorized to use this API.")

    @allure.title("login tools and import solution negative cases")
    @allure.description(
        "TestType: Negative \n" 
        "Target: Check we can login tools and import solution failed with invalid file \n"
        "Description: login tools and import solution failed with invalid file \n"
        "TestDesigner: Song Sun \n"
        "Date: 2023-09-26 \n"
        "Link: https://graphsql.atlassian.net/browse/TOOLS-2687"
    )
    @pytest.mark.high
    @pytest.mark.smoke
    @pytest.mark.api
    @pytest.mark.gus
    @pytest.mark.preexec
    @pytest.mark.apilogin
    @pytest.mark.run(order=0.4)
    def test_login_gst_import_solution_with_invalid_file(self):
        login = Login()
        result = login.login()
        my_cookies = result.get_cookies()
        result.assert_result(200, False, "")
        logger.info("my_cookies: " + my_cookies)
        ds = Design_Schema()
        result = ResultBase(ds.upload_post_call_with_cookies(url="/api/system/import", cookies=my_cookies, file_name="wrong_internal_zip.tar.gz"))
        result.assert_result(state_code=500, error_state=True, message="Failed to import graph:")
