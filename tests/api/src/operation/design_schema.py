from pathlib import Path
from src.api.tools_api import tools
from src.common.read_data import data
from src.common.logger import logger
from src.operation.cloud import Cloud
import json
from utils.data_util.data_resolver import read_test_data
import mimetypes
from codecs import encode

import requests


RESOURCES_PATH = Path(__file__).resolve().parents[2].joinpath("data/solution")
CONFIG_DATA = read_test_data(file="tools_test_data.json")
domain = CONFIG_DATA.get("domain")

class Design_Schema():
    login_user_data = data.get_yaml_data("general/login_data.yml")
    TEST_ENV = CONFIG_DATA.get("test_env")

    def update_the_test_env_for_cloud(self):
        if 'tgcloud' in self.TEST_ENV:
            if not domain:
                cloud=Cloud()
                cloud.init_cloud_env()
                CONFIG_DATA = read_test_data(file="tools_test_data.json")
            TEST_ENV = CONFIG_DATA.get("domain")
        else:
            TEST_ENV = self.TEST_ENV
        return TEST_ENV

    # upload solution
    def post_call_with_cookies(self, url="", content_type="json", cookies=""):

        if content_type == "json":
            header = {
                "Cookie": cookies,
                "Content-Type": "application/json"
            }
            json_data = {

            }
            login_response = tools.my_post(url=url, json=json.dumps(json_data), headers=header)
        elif content_type == "form-data":
            header = {
                "Cookie": cookies,
                "Content-Type": "multipart/form-data"
            }
            files = {'file': open('report.xls', 'rb')}

            login_response = tools.my_post(url=url, json=json.dumps(), headers=header)

        return login_response


    def upload_post_call_with_cookies(self, url="", cookies="",header="", file_name="covid19.tar.gz"):
        """
             upload solution post call with cookies
            :param cookies: Cookies gotten from GST
            :return: return the response of api with custom keywords
        """
        file_path = str(RESOURCES_PATH.joinpath(file_name))
        logger.info("file_path: " + file_path)
        URL = self.update_the_test_env_for_cloud() + url
        logger.info("URL: " + URL)
        dataList = []
        boundary = 'wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=file; filename={0}'.format(file_name)))

        fileType = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
        dataList.append(encode('Content-Type: {}'.format(fileType)))
        dataList.append(encode(''))

        with open(file_path, 'rb') as f:
            dataList.append(f.read())
        dataList.append(encode('--' + boundary + '--'))
        dataList.append(encode(''))
        body = b'\r\n'.join(dataList)
        payload = body
        if header == "":
            headers = {
                'Cookie': cookies,
                'Content-Type': 'multipart/form-data',
                'Content-type': 'multipart/form-data; boundary={}'.format(boundary)
            }
        else:
            headers = header
        logger.info("header: " + str(headers))
        resp = requests.request("POST", URL, headers=headers, data=payload)
        logger.info("resp: " + resp.text)
        return resp

   







