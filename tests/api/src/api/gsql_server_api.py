import os
from pathlib import Path
from src.common.logger import logger
from src.common.rest_client import RestClient
from src.common.read_data import data
from src.common.result_base import ResultBase
from src.operation.cloud import Cloud
from utils.data_util.data_resolver import read_test_data

CONFIG_DATA = read_test_data(file="tools_test_data.json")
domain = CONFIG_DATA.get("domain")
test_env = CONFIG_DATA.get("test_env")
if 'tgcloud' in test_env:
    TEST_ENV = domain
else:
    TEST_ENV = test_env


class GSQLServer(RestClient):
    def __init__(self, api_root_url, **kwargs):
        super(GSQLServer, self).__init__(api_root_url, **kwargs)
        #logger.info(f"API root url is {api_root_url}")

    def get_schema_info(self, graph_name, **kwargs):
        return self.get("/api/gsql-server/gsql/schema?graph={}".format(graph_name), **kwargs)

gsqlserver=GSQLServer(TEST_ENV)