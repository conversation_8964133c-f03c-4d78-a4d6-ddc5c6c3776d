import os
from pathlib import Path
from src.common.logger import logger
from src.common.rest_client import RestClient
from src.common.read_data import data
from src.common.result_base import ResultBase
from src.operation.cloud import Cloud
from utils.data_util.data_resolver import read_test_data

CONFIG_DATA = read_test_data(file="tools_test_data.json")
domain = CONFIG_DATA.get("domain")
test_env = CONFIG_DATA.get("test_env")
if 'tgcloud' in test_env:
    TEST_ENV = domain
else:
    TEST_ENV = test_env


class Tools(RestClient):

    def __init__(self, api_root_url, **kwargs):
        super(Tools, self).__init__(api_root_url, **kwargs)
        #logger.info(f"API root url is {api_root_url}")

    def login_gst(self, json, **kwargs):
        return self.post("/api/auth/login", json, **kwargs)

    def logout_gst(self, **kwargs):
        return self.post("/api/auth/logout", **kwargs)
    
    def check_api_version(self, **kwargs):
        return self.get("/api/version", **kwargs)
    
    def get_loading_job_meta(self, graph_name="MyGraph", **kwargs):
        return self.get("/api/loading-jobs/{}/meta".format(graph_name), **kwargs)
    
    def get_config(self, **kwargs):
        return self.get("/api/config", **kwargs)
    
    def get_loadingdata(self, **kwargs):
        return self.get("/api/data/loading_data", **kwargs)

    def post_config(self, **kwargs):
        return self.post("/api/config", **kwargs)
    
    def check_chunk(self, **kwargs):
        return self.get("/api/data/loading_data/upload/chunk", **kwargs)
    
    def upload_chunk(self, **kwargs):
        return self.post("/api/data/loading_data/upload/chunk", **kwargs)
    
    def delete_files(self, **kwargs):
        return self.delete("/api/data/loading_data", **kwargs)
    
    def get_files(self, **kwargs):
        return self.get("/api/data/loading_data", **kwargs)
    
    def upload_assemble(self, **kwargs):
        return self.post("/api/data/loading_data/upload/chunk/assemble", **kwargs)
    
    def generate_cert(self, **kwargs):
        return self.post("/api/config/generate-cert", **kwargs)
    
    def stop_service(self, **kwargs):
        return self.post("/api/service/stop", **kwargs)
    
    def start_service(self, **kwargs):
        return self.post("/api/service/start", **kwargs)
    
    def restart_service(self, **kwargs):
        return self.post("/api/service/restart", **kwargs)

    def execute_api_v2(self,  **kwargs):
        return self.post("/api/v2", **kwargs)
    
    def connect_graphql(self,graph_name,  **kwargs):
        return self.get("/api/graphql/{}".format(graph_name), **kwargs)

    def execute_graphqury(self, graph_name, json, **kwargs):
        return self.post("/api/graphql/{}".format(graph_name), json, **kwargs)

    def get_graph_algorithm(self, **kwargs):
        return self.get("/api/graph-algorithm", **kwargs)

    def install_algorithm(self, graph_name, query_name, **kwargs):
        return self.post("/api/graph-algorithm/{}/install?queryName={}".format(graph_name, query_name), **kwargs)

    def get_query_info(self, graph_name, query_name, **kwargs):
        if query_name:
            return self.get("/api/queries/{}/info/{}".format(graph_name, query_name), **kwargs)
        else:
            return self.get("/api/queries/{}/info".format(graph_name), **kwargs)

    def delete_query(self, graph_name, query_name, **kwargs):
        return self.delete("/api/queries/{}/info/{}".format(graph_name, query_name), **kwargs)

    def delete_all_query_drafts(self, graph_name, params, **kwargs):
        return self.delete("/api/queries/{}/drafts{}".format(graph_name, params), **kwargs)

    def delete_query_draft(self, graph_name, draft, params, **kwargs):
        return self.delete("/api/queries/{}/info/{}/draft{}".format(graph_name, draft, params), **kwargs)

    def delete_query_draft_without_para(self, graph_name, draft, **kwargs):
        return self.delete("/api/queries/{}/info/{}/draft".format(graph_name, draft), **kwargs)

    def delete_gsql_query(self, graph_name, draft_name, **kwargs):
        return self.delete("/api/queries/{}/info/{}".format(graph_name, draft_name), **kwargs)

    def delete_gsql_query_with_para(self, graph_name, draft_name, para, **kwargs):
        return self.delete("/api/queries/{}/info/{}{}".format(graph_name, draft_name, para), **kwargs)

    def install_query_draft(self, graph_name, params, **kwargs):
        return self.post("/api/queries/{}/gsql/install{}".format(graph_name, params), **kwargs)

    """
        design schema model
    """
    def export_solution(self, paras, **kwargs):
        return self.get("/api/system/export{}".format(paras), **kwargs)
    
    def gui_store(self, paras, **kwargs):
        return self.get("/api/system/gui-store{}".format(paras), **kwargs)

    def get_loading_jobs(self, graph_name, params, **kwargs):
        return self.get("/api/loading-jobs/{}/meta{}".format(graph_name, params), **kwargs)

    def delete_all_loading_jobs(self, graph_name, params, **kwargs):
        return self.delete("/api/loading-jobs/{}/meta{}".format(graph_name, params), **kwargs)

    def get_loading_jobs_name(self, graph_name, params, **kwargs):
        return self.get("/api/loading-jobs/{}/meta/{}".format(graph_name, params), **kwargs)

    def delete_loading_jobs_name(self, graph_name, params, **kwargs):
        return self.delete("/api/loading-jobs/{}/meta/{}".format(graph_name, params), **kwargs)

    def get_loading_jobs_progress(self, graph_name, params, **kwargs):
        return self.get("/api/loading-jobs/{}/loading/progress{}".format(graph_name, params), **kwargs)

    def pause_loading_jobs_progress(self, graph_name, params, **kwargs):
        return self.post("/api/loading-jobs/{}/loading/pause{}".format(graph_name, params), **kwargs)

    def grant_role(self, graph_name, params, **kwargs):
        return self.post("/api/gsql-server/gsql/user?graph={}".format(graph_name), params, **kwargs)

    def resume_loading_jobs_progress(self, graph_name, params, **kwargs):
        return self.post("/api/loading-jobs/{}/loading/resume{}".format(graph_name, params), **kwargs)

    def stop_loading_jobs_progress(self, graph_name, params, **kwargs):
        return self.post("/api/loading-jobs/{}/loading/stop{}".format(graph_name, params), **kwargs)

    def post_add_loading_jobs_name(self, graph_name, params, json, **kwargs):
        return self.post("/api/loading-jobs/{}/meta/{}".format(graph_name, params), json, **kwargs)

    def post_start_loading_jobs(self, graph_name, params, json, **kwargs):
        return self.post("/api/loading-jobs/{}/loading/start{}".format(graph_name, params), json, **kwargs)
    """
        write queries
    """
    def post_add_query_draft(self, graph_name, query_name, params, json, **kwargs):
        return self.post("/api/queries/{}/info/{}/draft{}".format(graph_name, query_name, params), json, **kwargs)

    def put_update_query_draft(self, graph_name, query_name, params, json, **kwargs):
        return self.put("/api/queries/{}/info/{}/draft{}".format(graph_name, query_name, params), json, **kwargs)

    def add_draft_query(self, graph_name, query_name, **kwargs):
        return self.post("/api/queries/{}/info/{}/draft".format(graph_name, query_name), **kwargs)

    """
        Graph styles model
    """
    def get_global_graph_styles(self, **kwargs):
        return self.get("/api/graph-styles/global", **kwargs)

    def get_local_graph_styles(self, graph_name, **kwargs):
        return self.get("/api/graph-styles/local/{}".format(graph_name), **kwargs)

    def put_global_graph_styles(self, json, **kwargs):
        return self.put("/api/graph-styles/global", json, **kwargs)

    def put_local_graph_style(self, graph_name, json, **kwargs):
        return self.put("/api/graph-styles/local/{}".format(graph_name), json, **kwargs)

    def post_local_graph_style(self, graph_name, json, **kwargs):
        return self.post("/api/graph-styles/local/{}".format(graph_name), json, **kwargs)

    def delete_global_graph_style(self, **kwargs):
        return self.delete("/api/graph-styles/global", **kwargs)

    def delete_local_graph_style(self, graph_name, **kwargs):
        return self.delete("/api/graph-styles/local/{}".format(graph_name), **kwargs)

    """
       Loading job info model
    """
    def get_loading_job_info(self, graph_name, **kwargs):
        return self.get("/api/loading-job-info/{}".format(graph_name), **kwargs)

    def put_loading_job_info(self, graph_name, json, **kwargs):
        return self.put("/api/loading-job-info/{}".format(graph_name), json, **kwargs)

    def delete_loading_job_info(self, graph_name, **kwargs):
        return self.delete("/api/loading-job-info/{}".format(graph_name), **kwargs)

    """
        VQB
    """
    def get_graph_patterns(self, graph_name, **kwargs):
        return self.get("/api/visual-patterns/{}".format(graph_name), **kwargs)

    def delete_VQB_patterns(self, graph_name, params, **kwargs):
        return self.delete("/api/visual-patterns/{}/{}".format(graph_name, params), **kwargs)

    """
        Exploration results
    """
    def get_all_exploration_results(self, graph_name, **kwargs):
        return self.get("/api/exploration-results/{}".format(graph_name), **kwargs)
    
    def get_exploration_result(self, graph_name, file_name, **kwargs):
        return self.get("/api/exploration-results/{}/{}".format(graph_name, file_name), **kwargs)

    def post_exploration_results(self, graph_name, file_name, json, **kwargs):
        return self.post("/api/exploration-results/{}/{}".format(graph_name, file_name), json, **kwargs)

    def put_exploration_results(self, graph_name, file_name, json, **kwargs):
        return self.put("/api/exploration-results/{}/{}".format(graph_name, file_name), json, **kwargs)

    def delete_exploration_results(self, graph_name, file_name, **kwargs):
        return self.delete("/api/exploration-results/{}/{}".format(graph_name, file_name), **kwargs)

    """
    Data Source models
    """
    def get_datasource_info(self, graph_name, datasource_type, **kwargs):
        return self.get("/api/data-sources/{}/{}/names".format(graph_name, datasource_type), **kwargs)

    def post_datasource_format_check(self,datasource_type, text, **kwargs):
        return self.post("/api/data-sources/{}/format-check".format(datasource_type), text, **kwargs)

    def put_datasource_info(self, graph_name, datasource_type, alias, text,**kwargs):
        return self.put("/api/data-sources/{}/{}/{}".format(graph_name, datasource_type, alias), text, **kwargs)

    def delete_datasource(self, graph_name, datasource_type, alias, **kwargs ):
        return self.delete("/api/data-sources/{}/{}/{}".format(graph_name, datasource_type, alias), **kwargs)

    """
    Health Check
    """
    def get_ping(self, **kwargs):
        return self.get("/api/ping", **kwargs)

tools = Tools(TEST_ENV)


