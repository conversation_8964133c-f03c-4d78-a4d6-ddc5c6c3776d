## The test data of APIs queries

## Positive case of GET /api/queries/{graphName}/info/{query_name}
get_queries_info_cases:
  get_single_query_info:
    graph_name: "MyGraph"
    query_name:
  get_all_queries_info:
    graph_name: "MyGraph"
    query_name: "all_connection"

## Negative case of GET /api/queries/{graphName}/info/{query_name}
get_queries_info_negative_cases:
  get_queries_info_nonexistent_graph:
    graph_name: "nonexistent"
    query_name:
    is_login: True
    status_code: 404
    error: False
    message: "Graph nonexistent does not exist or you don't have privileges on it"
  get_queries_info_without_cookie:
    graph_name: "MyGraph"
    query_name: 
    is_login: False
    status_code: 401
    error: True
    message: "You are not authorized to use this API"
  get_queries_info_with_special_characters:
    graph_name: "test~!@#$%"
    query_name:
    is_login: True
    status_code: 404
    error: True
    message: "Route /api/queries/test~!@ not found."
  get_single_query_with_nonexistent_graph:
    graph_name: "nonexistent"
    query_name: "edge_crawl"
    is_login: True
    status_code: 404
    error: False
    message: "Graph nonexistent does not exist or you don't have privileges on it"
  get_single_query_without_cookie:
    graph_name: "MyGraph"
    query_name: "edge_crawl"
    is_login: False
    status_code: 401
    error: True
    message: "You are not authorized to use this API"
  get_single_query_specialCharacters:
    graph_name: "MyGraph"
    query_name: "notExi~!@#$$query"
    is_login: True
    status_code: 404
    error: True
    message: "Query 'notExi~!@' for graph 'MyGraph' cannot be found."

delete_query_cases:
  delete_algorithm_query:
    graph_name: "MyGraph"
    query: {algrotithm_name: "Maxflow", query_name: "tg_maxflow"}

delete_query_negative_cases:
  delete_query_without_cookie:
    graph_name: "MyGraph"
    query_name: "edge_crawl"
    is_login: False
    status_code: 401
    error: True
    message: "You are not authorized to use this API"
  delete_uninstalled_query:
    graph_name: "MyGraph"
    query_name: "edge_crawl"
    is_login: True
    status_code: 500
    error: True
    message: "Failed to delete query 'edge_crawl' from graph 'MyGraph'"
  delete_query_with_nonexistent_graph:
    graph_name: "nonexistent"
    query_name: "edge_crawl"
    is_login: True
    status_code: 404
    error: False
    message: "Graph nonexistent does not exist or you don't have privileges on it"
  delete_query_with_nonexistent_query:
    graph_name: "MyGraph"
    query_name: "nonexistent"
    is_login: True
    status_code: 500
    error: True
    message: "Failed to delete query 'nonexistent' from graph 'MyGraph'"
  delete_query_with_specialCharacters:
    graph_name: "notExi~!@#$$stedGraph"
    query_name: "edge_crawl"
    is_login: True
    status_code: 404
    error: True
    message: "Route /api/queries/notExi~!@ not found"
delete_all_query_draft_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    draft: "all_connection"
    params: ""
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  special_characters_draft_name:
    graph_name: "MyGraph*#$~"
    draft: "all_connection"
    params: "?jobName=load_job_popData___~*Sheet1_csv_1584998109163"
    status_code: 404
    error: True
    message: "not found"
delete_query_draft_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    draft: "all_connection"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  existent_graph_name:
    graph_name: "MyGraph"
    draft: "draft"
    params: ""
    status_code: 500
    error: True
    message: "Failed to delete"
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    draft: "all_connection"
    params: ""
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  nonexistent_draft_name:
    graph_name: "MyGraph"
    draft: "not_existed_draft"
    params: ""
    status_code: 500
    error: True
    message: "Failed to delete"
#    there is a bug, these params can delete query successfully
#  special_characters_draft_name:
#    graph_name: "MyGraph"
#    draft: "all_connection"
#    params: "?jobName=load_job_popData___~*Sheet1_csv_1584998109163"
#    status_code: 500
#    error: True
#    message: "Failed to delete"
#  oversize_characters_draft_name:
#    graph_name: "MyGraph"
#    draft: "all_connection"
#    params: "?jobName=load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
#    status_code: 500
#    error: True
#    message: "Failed to delete"
#  null_params:
#    graph_name: "MyGraph"
#    draft: "all_connection"
#    params: "?null"
#    status_code: 500
#    error: True
#    message: "Failed to delete"
#  special_characters:
#    graph_name: "MyGraph"
#    draft: "all_connection"
#    params: '?id=null&KEY=””'
#    status_code: 500
#    error: True
#    message: "Failed to delete"
#  embedded_single_quote:
#    graph_name: "MyGraph"
#    draft: "all_connection"
#    params: "?key=jo’scar"
#    status_code: 500
#    error: True
#    message: "Failed to delete"
#  field_size_test:
#    graph_name: "MyGraph"
#    draft: "all_connection"
#    params: "?number=9999999999999999999999999"
#    status_code: 500
#    error: True
#    message: "Failed to delete"
post_add_query_draft_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    query_name: "test_add_draft"
    params: "invalid_cookies"
    json_data: '{"name":"test_add_draft","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_add_draft(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  add_query_draft:
    graph_name: "MyGraph"
    query_name: "test_add_draft"
    params: ""
    json_data: '{"name":"test_add_draft","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_add_draft(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    query_name: "test_add_draft"
    params: ""
    json_data: '{"name":"test_add_draft","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_add_draft(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  special_characters_in_query_name:
    graph_name: "MyGraph"
    query_name: "test_add_draft!@~@#"
    params: ""
    json_data: '{"name":"test_add_draft!@~@#","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_add_draft!@~@#(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 405
    error: True
    message: "Method not allowed."
  oversize_characters_in_query_name:
    graph_name: "MyGraph"
    query_name: "load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    params: ""
    json_data: '{"name":"load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  null_params:
    graph_name: "MyGraph"
    query_name: "test_add_draft1"
    params: "?null"
    json_data: '{"name":"test_add_draft1","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_add_draft1(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    query_name: "test_add_draft2"
    params: '?id=null&KEY=””'
    json_data: '{"name":"test_add_draft2","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_add_draft2(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    query_name: "test_add_draft3"
    params: "?key=jo’scar"
    json_data: '{"name":"test_add_draft3","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_add_draft3(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    query_name: "test_add_draft4"
    params: "?number=9999999999999999999999999"
    json_data: '{"name":"test_add_draft4","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_add_draft4(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_add_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
put_update_query_draft_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    query_name: "test_update_draft"
    params: "invalid_cookies"
    json_data: '{"name":"test_update_draft","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_update_draft(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  add_query_draft:
    graph_name: "MyGraph"
    query_name: "test_update_draft"
    params: ""
    json_data: '{"name":"test_update_draft","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_update_draft(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    query_name: "test_update_draft"
    params: ""
    json_data: '{"name":"test_update_draft","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_update_draft(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  special_characters_in_query_name:
    graph_name: "MyGraph"
    query_name: "test_update_draft!@~@#"
    params: ""
    json_data: '{"name":"test_update_draft!@~@#","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_update_draft!@~@#(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 405
    error: True
    message: "Method not allowed."
  oversize_characters_in_query_name:
    graph_name: "MyGraph"
    query_name: "load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    params: ""
    json_data: '{"name":"load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  null_params:
    graph_name: "MyGraph"
    query_name: "test_update_draft1"
    params: "?null"
    json_data: '{"name":"test_update_draft1","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_update_draft1(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    query_name: "test_update_draft2"
    params: '?id=null&KEY=””'
    json_data: '{"name":"test_update_draft2","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_update_draft2(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    query_name: "test_update_draft3"
    params: "?key=jo’scar"
    json_data: '{"name":"test_update_draft3","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_update_draft3(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    query_name: "test_update_draft4"
    params: "?number=9999999999999999999999999"
    json_data: '{"name":"test_update_draft4","syntax":"GSQL","code":"CREATE DISTRIBUTED QUERY test_update_draft4(/* Parameters here */) FOR GRAPH MyGraph { \n  /* Write query logic here */ \n  PRINT \"test_update_draft works!\"; \n}","graphUpdate":false}'
    status_code: 200
    error: False
    message: ""
post_install_query_valid:
  existent_graph_name:
    graph_name: "MyGraph"
    params: "?queryName=all_connection"
    status_code: 200
    error: False
    message: ""
post_install_query_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    params: "?queryName=all_connection"
    status_code: 500
    error: True
    message: "Cannot install query without being in a graph!"
  nonexistent_draft_name:
    graph_name: "MyGraph"
    params: "?queryName=nonexist_query"
    status_code: 500
    error: True
    message: "cannot be found!Query installation failed!"
  special_characters_draft_name:
    graph_name: "MyGraph"
    params: "?queryName=all_~!@#$%^&*connection"
    status_code: 400
    error: True
    message: "invalid query name"
  oversize_characters_draft_name:
    graph_name: "MyGraph"
    params: "?queryName=load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 500
    error: True
    message: "cannot be found!Query installation failed"
  null_params:
    graph_name: "MyGraph"
    params: "?null"
    status_code: 400
    error: True
    message: "No queries to install."
  special_characters:
    graph_name: "MyGraph"
    params: '?id=null&KEY=””'
    status_code: 400
    error: True
    message: "No queries to install."
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "?key=jo’scar"
    status_code: 400
    error: True
    message: "No queries to install."
  field_size_test:
    graph_name: "MyGraph"
    params: "?number=9999999999999999999999999"
    status_code: 400
    error: True
    message: "No queries to install."