#define the V2 api with the valid body
valid_v2_body:
  get_log_list:
    query: "query ($hostID: String) {\n  Log {\n    List(\n      FilePath: \"\",\n      ServiceName: \"EXE\",\n      Replica: 0,\n      Partition: 0,\n      HostID: $hostID,\n      FileRegxFilter: \"\"\n    ) {\n      Infos {\n        Name\n        Path\n        SourcePath\n      }\n    }\n  }\n}"
    variables: {}
    message: "Infos"
  # comment the test case for APPS-2542
  # search_log_all:
  #   query:  "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
  #   variables: {"pattern": "search_log_for_api_testing"}
  #   message: "search_log_for_api_testing"
  search_log_admin:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["admin"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_controller:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["controller"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_zk:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["zk"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_etcd:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["etcd"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_nginx:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["nginx"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_kafkastrm-ll:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["kafkastrm-ll"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_kafkaconn:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["kafkaconn"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_fileLoader:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["fileLoader"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_kafkaLoader:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["kafkaLoader"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_gui:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["gui"], "pattern": "search_log_for_api_testing" }
    message: "search_log_for_api_testing"
  search_log_gsql:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["gsql"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_executor:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["executor"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_kafka:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["kafka"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_ts3serv:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["ts3serv"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_ts3:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["ts3"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_informant:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["informant"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  # comment the test case for APPS-2542
  # search_log_gse:
  #   query:  "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
  #   variables: {"components": ["gse"],"pattern": "search_log_for_api_testing"}
  #   message: "Results"
  #  offline because https://graphsql.atlassian.net/browse/APPS-2543
  #  search_log_gpe:
  #    query:  "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
  #    variables: {"components": ["gpe"],"pattern": "search_log_for_api_testing"}
  #    message: "Results"
  search_log_dict:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["dict"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  search_log_restpp:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": ["restpp"], "pattern": "search_log_for_api_testing" }
    message: "Results"
  check_log_file_gui:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/gui/GUI#1.out", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_admin:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/admin/ADMIN.INFO", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_controller:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/controller/CTRL#1.log", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_dict:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/dict/DICT.INFO", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_etcd:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/etcd/ETCD#1.out", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_executor:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/executor/EXE_1.log", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_gpe:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/gpe/log.INFO", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_gse:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/gse/log.INFO", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_gsql:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/gsql/log.INFO", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_informant:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/informant/IFM#1.log", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_kafka:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/kafka/kafka.log", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_kafkaconn:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/kafkaconn/kafkaconn.log", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_kafkastrm-ll:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables:
      { "hostID": "m1", "path": "/kafkastrm-ll/kafkastrm-ll.log", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_nginx:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/nginx/logs/NGINX#1.out", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_restpp:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/restpp/RESTPP#1.INFO", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_ts3:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/ts3/TS3_1.log", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_ts3serv:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/ts3serv/TS3SERV#1.out", "offset": 1, "length": 200 }
    message: "log_file_not_empty"
  check_log_file_zk:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/zk/zookeeper.log", "offset": 1, "length": 200 }
    message: "log_file_not_empty"

#define the V2 api with the invalid body
invalid_v2_body:
  get_log_list_invalid_key:
    query: "query ($hostID: String) {\n  Log_invalid {\n    List(\n      FilePath: \"\",\n      ServiceName: \"EXE\",\n      Replica: 0,\n      Partition: 0,\n      HostID: $hostID,\n      FileRegxFilter: \"\"\n    ) {\n      Infos {\n        Name\n        Path\n        SourcePath\n      }\n    }\n  }\n}"
    variables: {}
    message: "Cannot query field "
  get_log_list_invalid_value:
    query: "query ($hostID: String) {\n  Log {\n    List(\n      FilePath: \"\",\n      ServiceName: \"EXE\",\n      Replica: -1,\n      Partition: 0,\n      HostID: $hostID,\n      FileRegxFilter: \"\"\n    ) {\n      Infos {\n        Name\n        Path\n        SourcePath\n      }\n    }\n  }\n}"
    variables: {}
    message: "Replica should be greater than or equal to 0."
  search_log_all_invalid_key:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "pattern_invalid": "search_log_for_api_testing" }
    message: 'Variable "pattern" has invalid value null.'
  search_log_all_invalid_value:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: -1\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "pattern": "search_log_for_api_testing" }
    message: "Limit should be greater than or equal to 0."
  search_log_all_with_empty_variables:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: {}
    message: 'Variable "pattern" has invalid value null.'
  search_log_with_invalid_component_value:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "components": 123, "pattern": "search_log_for_api_testing" }
    message: "could not unmarshal 123 (float64) into string: incompatible type"
  search_log_with_empty_component:
    query: "query ($pattern: String!, $hostIDs: [String!], $components: [String!]) {\n  Log {\n    Search(\n      HostIDs: $hostIDs,\n      Components1: $components,\n      Limit: 1000\n    ) {\n      HostID\n      Results(\n        FileFilter: \"\",\n      \tPattern: $pattern\n      ) {\n        Path\n      \tLine\n      \tOffset\n      \tLineNumber\n      }\n    }\n  }\n}"
    variables: { "pattern": "search_log_for_api_testing" }
    message: 'Unknown argument "Components1"'
  check_log_file_invalid_host:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m0", "path": "/admin/ADMIN.INFO", "offset": 1, "length": 200 }
    message: "EXE does not exist in m0"
  check_log_file_invalid_path:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/admin/invalid/ADMIN.INFO", "offset": 1, "length": 200 }
    message: "no such file or directory"
  check_log_file_invalid_length:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/admin/invalid/ADMIN.INFO", "offset": 1, "length": -200 }
    message: "Length should be greater than or equal to 0."
  check_log_file_invalid_offset:
    query: "query ($path: String!, $hostID: String, $offset: Int!, $length: Int!) {\n  Log {\n    Content(\n      FilePath: $path,\n      ServiceName: \"EXE\",\n      HostID: $hostID,\n      Page: { Offset: $offset, Length: $length }\n    ) {\n      Data\n    }\n  }\n}"
    variables: { "hostID": "m1", "path": "/admin/ADMIN.INFO", "offset": -1, "length": 200 }
    message: "Offset should be greater than or equal to 0."
