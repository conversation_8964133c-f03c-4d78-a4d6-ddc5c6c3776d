#define the invalid cases
get_loading_jobs_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  nonexistent graphName:
    graph_name: "noExistedGraphName"
    params: ""
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  existent graphName:
    graph_name: "MyGraph"
    params: ""
    status_code: 200
    error: False
    message: ""
  null_params:
    graph_name: "MyGraph"
    params: "?null"
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    params: '?id=null&KEY=””'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "?key=jo’scar"
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    params: "?number=9999999999999999999999999"
    status_code: 200
    error: False
    message: ""
delete_all_loading_jobs_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  nonexistent_graphName:
    graph_name: "noExistedGraphName"
    params: ""
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
#  null_params: invalid cases also delete all jobs
#    graph_name: "MyGraph"
#    params: "?null"
#    status_code: 200
#    error: False
#    message: ""
#  special_characters:
#    graph_name: "MyGraph"
#    params: '?id=null&KEY=””'
#    status_code: 200
#    error: False
#    message: ""
#  embedded_single_quote:
#    graph_name: "MyGraph"
#    params: "?key=jo’scar"
#    status_code: 200
#    error: False
#    message: ""
#  field_size_test:
#    graph_name: "MyGraph"
#    params: "?number=9999999999999999999999999"
#    status_code: 200
#    error: False
#    message: ""
get_loading_jobs_name_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    params: "load_job_popData___Sheet1_csv_1584998109163"
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  nonexistent_job_name:
    graph_name: "MyGraph"
    params: "noExistedJobName"
    status_code: 500
    error: True
    message: "Failed to get loading job 'noExistedJobName' for graph 'MyGraph'."
  special_characters_job_name:
    graph_name: "MyGraph"
    params: "load_job_popData___#$%_+Sheet1_csv_1584998109163"
    status_code: 500
    error: True
    message: "Failed to get loading job 'load_job_popData___' for graph 'MyGraph'."
  oversize_characters_job_name:
    graph_name: "MyGraph"
    params: "load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 500
    error: True
    message: "Failed to get loading job 'load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111' for graph 'MyGraph'."
  null_params:
    graph_name: "MyGraph"
    params: "null"
    status_code: 500
    error: True
    message: ""
  special_characters:
    graph_name: "MyGraph"
    params: 'id=null&KEY=””'
    status_code: 500
    error: True
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "key=jo’scar"
    status_code: 500
    error: True
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    params: "number=9999999999999999999999999"
    status_code: 500
    error: True
    message: ""
post_add_loading_jobs_name_invalid:
  add_new_loading_job:
    graph_name: "MyGraph"
    params: "add_new"
    json_data: '{"FileNames":{"MyDataSource":""},"Type":"Offline","JobName":"load_job_SearchTrend_csv_1586467063020","GraphName":"MyGraph","Headers":{},"Filters":[],"LoadingStatements":[{"Type":"Vertex","TargetName":"SearchStat","DataSource":{"Type":"FileVar","Value":"MyDataSource"},"Mappings":[{"Type":"SrcColIndex","Value":0},{"Type":"SrcColIndex","Value":1},{"Type":"SrcColIndex","Value":2},{"Type":"SrcColIndex","Value":3},{"Type":"SrcColIndex","Value":4}],"UsingClauses":{"EOL":"\\n","HEADER":"true","SEPARATOR":","}},{"Type":"Edge","TargetName":"WEATHER_STAMP","FromVertexType":"Day_","ToVertexType":"WeatherStat","DataSource":{"Type":"FileVar","Value":"MyDataSource"},"Mappings":[{"Type":"SrcColIndex","Value":0},{"Type":"SrcColIndex","Value":3}],"UsingClauses":{"EOL":"\\n","SEPARATOR":",","HEADER":"true"},"WhereClause":"($1 == \"20052677\")"}]}'
    status_code: 201
    error: False
    message: "Successfully created loading job"
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    json_data: '{"FileNames":{"MyDataSource":""},"Type":"Offline","JobName":"load_job_popData_!<>@#__Sheet1_csv_1584998109163","GraphName":"MyGraph","Headers":{},"Filters":[],"LoadingStatements":[{"Type":"Vertex","TargetName":"Province","DataSource":{"Type":"FileVar","Value":"MyDataSource"},"Mappings":[{"Type":"SrcColIndex","Value":0},{"Type":"SrcColIndex","Value":0},{"Type":"SrcColIndex","Value":1},{"Type":"SrcColIndex","Value":2}],"UsingClauses":{"EOL":"\\n","HEADER":"true","SEPARATOR":","}}]}'
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
delete_loading_jobs_name_invalid:
  delete_loading_job_valid:
    graph_name: "MyGraph"
    params: "delete_loading_jobs"
    status_code: 200
    error: False
    message: "Successfully deleted loading job"
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    params: "load_job_popData___Sheet1_csv_1584998109163"
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  nonexistent_job_name:
    graph_name: "MyGraph"
    params: "noExistedJobName"
    status_code: 500
    error: True
    message: "Semantic Check Fails: These jobs could not be found anywhere: [noExistedJobName]."
  special_characters_job_name:
    graph_name: "MyGraph"
    params: "load_job_popData___~*Sheet1_csv_1584998109163"
    status_code: 500
    error: True
    message: "Lexical error"
  oversize_characters_job_name:
    graph_name: "MyGraph"
    params: "load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 500
    error: True
    message: "Semantic Check Fails: These jobs could not be found anywhere"
  null_params:
    graph_name: "MyGraph"
    params: "null"
    status_code: 500
    error: True
    message: ""
  special_characters:
    graph_name: "MyGraph"
    params: 'id=null&KEY=””'
    status_code: 500
    error: True
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "key=jo’scar"
    status_code: 500
    error: True
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    params: "number=9999999999999999999999999"
    status_code: 500
    error: True
    message: ""
get_loading_jobs_progress_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  existent_graph_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_popData___Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: ""
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    params: "?jobName=load_job_popData___Sheet1_csv_1584998109163"
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  nonexistent_job_name:
    graph_name: "MyGraph"
    params: "?jobName=noExistedJobName"
    status_code: 200
    error: False
    message: ""
  special_characters_job_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_popData___~*Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: ""
  oversize_characters_job_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 200
    error: False
    message: ""
  null_params:
    graph_name: "MyGraph"
    params: "?null"
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    params: '?id=null&KEY=””'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "?key=jo’scar"
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    params: "?number=9999999999999999999999999"
    status_code: 200
    error: False
    message: ""
post_start_loading_jobs_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    json_data: '[{"name":"load_job_SearchTrend_csv_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  start_loading_job:
    graph_name: "MyGraph"
    params: ""
    json_data: '[{"name":"load_job_SearchTrend_csv_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 200
    error: False
    message: ""
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    params: ""
    json_data: '[{"name":"load_job_SearchTrend_csv_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  nonexistent_job_name:
    graph_name: "MyGraph"
    params: ""
    json_data: '[{"name":"not_existed_loading_job_name","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 200
    error: False
    message: ""
  special_characters_job_name:
    graph_name: "MyGraph"
    params: ""
    json_data: '[{"name":"load_job_SearchTrend_csv*~!@#$%^&*()_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 200
    error: False
    message: ""
  oversize_characters_job_name:
    graph_name: "MyGraph"
    params: ""
    json_data: '[{"name":"jobName=load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 200
    error: False
    message: ""
  null_params:
    graph_name: "MyGraph"
    params: "?null"
    json_data: '[{"name":"load_job_SearchTrend_csv_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    params: '?id=null&KEY=””'
    json_data: '[{"name":"load_job_SearchTrend_csv_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "?key=jo’scar"
    json_data: '[{"name":"load_job_SearchTrend_csv_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    params: "?number=9999999999999999999999999"
    json_data: '[{"name":"load_job_SearchTrend_csv_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
    status_code: 200
    error: False
    message: ""
pause_loading_jobs_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  existent_graph_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_popData___Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: ""
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    params: "?jobName=load_job_popData___Sheet1_csv_1584998109163"
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  nonexistent_job_name:
    graph_name: "MyGraph"
    params: "?jobName=noExistedJobName"
    status_code: 200
    error: False
    message: ""
  special_characters_job_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_popData___~*Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: ""
  oversize_characters_job_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 200
    error: False
    message: ""
  null_params:
    graph_name: "MyGraph"
    params: "?null"
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    params: '?id=null&KEY=””'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "?key=jo’scar"
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    params: "?number=9999999999999999999999999"
    status_code: 200
    error: False
    message: ""
resume_loading_jobs_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  existent_graph_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_popData___Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: ""
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    params: "?jobName=load_job_popData___Sheet1_csv_1584998109163"
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  nonexistent_job_name:
    graph_name: "MyGraph"
    params: "?jobName=noExistedJobName"
    status_code: 200
    error: False
    message: ""
  special_characters_job_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_popData___~*Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: ""
  oversize_characters_job_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 200
    error: False
    message: ""
  null_params:
    graph_name: "MyGraph"
    params: "?null"
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    params: '?id=null&KEY=””'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "?key=jo’scar"
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    params: "?number=9999999999999999999999999"
    status_code: 200
    error: False
    message: ""
stop_loading_jobs_invalid:
  invalid_cookies:
    graph_name: "MyGraph"
    params: "invalid_cookies"
    status_code: 401
    error: True
    message: "You are not authorized to use this API."
  existent_graph_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_popData___Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: ""
  nonexistent_graph_name:
    graph_name: "noExistedGraphName"
    params: "?jobName=load_job_popData___Sheet1_csv_1584998109163"
    status_code: 404
    error: False
    message: "Graph noExistedGraphName does not exist or you don't have privileges on it"
  nonexistent_job_name:
    graph_name: "MyGraph"
    params: "?jobName=noExistedJobName"
    status_code: 200
    error: False
    message: ""
  special_characters_job_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_popData___~*Sheet1_csv_1584998109163"
    status_code: 200
    error: False
    message: ""
  oversize_characters_job_name:
    graph_name: "MyGraph"
    params: "?jobName=load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111load_job_myPersonManyCharactors__V4__normal_csv_1652861346111"
    status_code: 200
    error: False
    message: ""
  null_params:
    graph_name: "MyGraph"
    params: "?null"
    status_code: 200
    error: False
    message: ""
  special_characters:
    graph_name: "MyGraph"
    params: '?id=null&KEY=””'
    status_code: 200
    error: False
    message: ""
  embedded_single_quote:
    graph_name: "MyGraph"
    params: "?key=jo’scar"
    status_code: 200
    error: False
    message: ""
  field_size_test:
    graph_name: "MyGraph"
    params: "?number=9999999999999999999999999"
    status_code: 200
    error: False
    message: ""

## data for Run all loading job
start_all_loading_job:
  run_all_loading_job:
    graph_name: "MyGraph"
    json_data: '[{"name":"load_job_Case_csv_1586463563813","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"Case.csv"}]},{"name":"load_job_Weather_csv_1586464953049","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"Weather.csv"}]},{"name":"load_job_PatientInfo_csv_1586465876287","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"PatientInfo.csv"}]},{"name":"load_job_PatientRoute_csv_1586466773624","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"PatientRoute.csv"}]},{"name":"load_job_SearchTrend_csv_1586467063020","streaming":false,"dataSources":[{"filename":"MyDataSource","name":"file","path":"SearchTrend.csv"}]}]'
