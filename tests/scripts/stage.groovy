#!groovy

def init(){
  String stageName = 'init'
  stage(stageName) {
    echo "stage_e2e_test() - ${stage_name} - Start"
    withCredentials([
      [$class: 'AmazonWebServicesCredentialsBinding',
      credentialsId: 'qe@tigergraph',
      accessKeyVariable: 'AWS_ACCESS_KEY_ID',
      secretKeyVariable: 'AWS_SECRET_ACCESS_KEY']
    ]) {
      sh 'mkdir -p ~/.aws && echo "[qe_tigergraph]\naws_access_key_id=${AWS_ACCESS_KEY_ID}\naws_secret_access_key=${AWS_SECRET_ACCESS_KEY}" > ~/.aws/credentials '
      sh 'sudo mkdir -p /root/.aws && sudo cp ~/.aws/credentials /root/.aws/credentials '
    }
  }
}

def e2e_test(){
  String stageName = 'Run e2e test'
  stage(stageName) {
    echo "stage_e2e_test() - ${stage_name} - Start"
    sh " rm -rf tests/reports"
    add_HEADER=(HEADER.isEmpty())?"":"-H $HEADER"
    add_FoT_PARAM=(FoT_PARAM.isEmpty())?"":"-F $FoT_PARAM"
    add_CLOUD_PARAM=(CLOUD_PARAM.isEmpty())?"":"-C $CLOUD_PARAM"
    add_VERSION=(VERSION.isEmpty())?"":"-V $VERSION"
    add_BROWSER=(BROWSER.isEmpty())?"":"-B $BROWSER"
    command="${cloud_e2e_workspace}/tests/run.sh -E ${TEST_ENV} ${add_HEADER} -M \"${MARKER}\" -N ${N_CPUS} -R ${RERUNS} -J ${JOB_NAME} -I ${BUILD_NUMBER}  ${add_FoT_PARAM} ${add_CLOUD_PARAM} ${add_VERSION} ${add_BROWSER}"
    sh "sudo bash ${command}"
  }
}

def upload_result(){
  String stageName = 'Uplod the result'
  stage(stageName) {
    echo "stage_upload_result() - ${stage_name} - Start"
  }
}

def fail_on_error(message){
  String stageName = 'Check the error'
  stage(stageName) {
    echo "stage_fail_on_error() - ${stage_name} - Start"
    e2e_result_summary="/tmp/e2e/log/tools/tools_e2e_result_summary"
    def grep_info = "currentBuild.result='SUCCESS'"
    def result_log = readFile e2e_result_summary
    currentBuild.result=(result_log.contains(grep_info))?'SUCCESS':'FAILURE'
  }
}

def notice(){
  String stageName = 'Send notice'
  stage(stageName) {
    if (NOTICE_RESULT=="true")
    {
      echo "stage_notice() - ${stage_name} - Start"
      basepath="${cloud_e2e_workspace}/tests/scripts"
      jobprefix="http://**************:30080/job"
      joburl="tools_e2e/${BUILD_NUMBER}"
      allureurl="tools_e2e/${BUILD_NUMBER}/allure"
      command="${basepath}/send_tools_e2e_result_msg.sh ${basepath}/notice_message_block.json ${currentBuild.currentResult} $TEST_ENV \"$MARKER\" $joburl $allureurl $jobprefix"
      sh "sudo bash ${command}"
    } else {
      echo "skip stage_notice() - ${stage_name}"
    }

  }
}

return this
