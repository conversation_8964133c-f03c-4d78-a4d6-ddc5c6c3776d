import { DEFAULT_EXTENSIONS } from '@babel/core';
import babel from '@rollup/plugin-babel';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import url from '@rollup/plugin-url';
import svgr from '@svgr/rollup';
import css from 'rollup-plugin-css-only';

const extensions = [...DEFAULT_EXTENSIONS, '.ts', '.tsx'];

module.exports = {
  input: 'src/index.ts',
  output: [
    {
      format: 'cjs',
      dir: 'dist/',
      preserveModules: true,
      // refer
      // 1. https://rollupjs.org/configuration-options/#output-preservemodulesroot
      // 2. https://github.com/rollup/plugins/issues/321#issuecomment-858541214
      preserveModulesRoot: 'src',
      exports: 'auto',
    },
  ],
  plugins: [
    resolve({
      extensions,
    }),
    commonjs(),
    babel({
      extensions,
      babelHelpers: 'runtime',
    }),
    url(),
    svgr(),
    css({
      output: 'style.css',
    }),
  ],
  external: [/node_modules/, '@tigergraph/models', '*.stories.mdx', '*.stories.tsx'],
};
