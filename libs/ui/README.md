## Getting Started

These instructions will get you a copy of the project up and running on your local machine for development and testing purposes.

### Prerequisites

If you haven't installed the dependencies run `yarn`.

Run `yarn build` to build the project. The build artifacts will be stored in the `dist/` directory.

### Development

In the project directory, run `yarn link:ui`.

The app will automatically link the ui.
