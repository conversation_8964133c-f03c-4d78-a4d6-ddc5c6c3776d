import React from 'react';
import { CSSProperties } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Slider, SliderProps } from 'baseui/slider';
import StyledInput from './styledInput';
import { expand } from 'inline-style-expand-shorthand';
import { InputProps } from 'baseui/input';

export interface StyledSliderProps extends SliderProps {
  editable?: boolean;
}

export default function StyledSlider(props: StyledSliderProps) {
  const [css, theme] = useStyletron();
  const { min = 0, max = 100, step = 1, disabled = false, editable = true, onChange, onFinalChange } = props;
  const value = props.value.map((item) => Number(item));
  const topDistance = 45;
  const thumbSize = 12;

  const onChangeInputValue = (e: React.FormEvent<HTMLInputElement>, index: number) => {
    const curValue = e.currentTarget['value'];
    if (!isNaN(+curValue)) {
      const newValue = [...value];
      newValue[index] = Number(curValue);
      if (
        (newValue.length === 1 && newValue[index] <= max && newValue[index] >= min) ||
        (newValue.length === 2 && newValue[0] >= min && newValue[0] <= newValue[1] && newValue[1] <= max)
      ) {
        onChange && onChange({ value: newValue });
        onFinalChange && onFinalChange({ value: newValue });
      }
    }
  };

  return (
    <div
      className={css({
        width: '100%',
        display: 'flex',
        flexWrap: 'nowrap',
      })}
    >
      {editable && value.length > 1 && (
        <div className={css({ marginRight: '8px' })}>
          <StyledSliderInput value={value[0]} onChange={(e) => onChangeInputValue(e, 0)} disabled={disabled} />
        </div>
      )}
      <Slider
        {...props}
        value={value}
        overrides={{
          Thumb: {
            style: ({ $isDragged }) => ({
              width: `${thumbSize}px`,
              height: `${thumbSize}px`,
              backgroundColor: '#fff',
              borderWidth: '1.5px',
              borderStyle: 'solid',
              borderColor: $isDragged ? theme.colors.accent : theme.colors.gray400,
              boxShadow: $isDragged ? `0px 0px 0px 5px ${theme.colors.secondary800}20` : 'none',
              ':hover': {
                boxShadow: `0px 0px 0px 5px ${theme.colors.secondary800}20`,
                borderColor: theme.colors.accent,
              },
            }),
          },
          InnerThumb: {
            style: {
              backgroundColor: 'transparent',
            },
          },
          // @ts-ignore
          InnerTrack: {
            style: ({ $value }) => {
              const style: CSSProperties = {
                width: `calc(100% - ${thumbSize}px)`,
                height: '4px',
                marginLeft: `${thumbSize / 2}px`,
                ...expand({
                  borderRadius: '0',
                }),
              };
              if ($value.length === 1) {
                const percentage = Math.min((($value[0] - min) / (max - min)) * 100, 100);
                return {
                  ...style,
                  background: `linear-gradient(to right, ${theme.colors.accent} 0%, ${theme.colors.accent} ${percentage}%, ${theme.colors.gray200} ${percentage}%, ${theme.colors.gray200} 100%)`,
                };
              } else if ($value.length === 2) {
                const percentageLeft = Math.max(Math.min((($value[0] - min) / (max - min)) * 100, 100), 0);
                const percentageRight = Math.max(Math.min((($value[1] - min) / (max - min)) * 100, 100), 0);
                return {
                  ...style,
                  background: `linear-gradient(to right, ${theme.colors.gray200} 0%,${theme.colors.gray200} ${percentageLeft}%, ${theme.colors.accent} ${percentageLeft}%, ${theme.colors.accent} ${percentageRight}%, ${theme.colors.gray200} ${percentageRight}%, ${theme.colors.gray200} 100%)`,
                };
              }
            },
          },
          ThumbValue: ({ $value, $thumbIndex }) => (
            <div
              className={css({
                position: 'absolute',
                top: `-${topDistance}px`,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
              })}
            >
              <div
                className={css({
                  padding: '8px 16px',
                  fontWeight: 400,
                  fontSize: '12px',
                  lineHeight: '16px',
                  color: '#fff',
                  backgroundColor: theme.colors.gray1000,
                  borderRadius: '5px',
                })}
              >
                {$value[$thumbIndex]}
              </div>
              <div
                className={css({
                  borderLeft: '4px solid transparent',
                  borderRight: '4px solid transparent',
                  borderTop: `4px solid ${theme.colors.gray1000}`,
                })}
              />
            </div>
          ),
          TickBar: {
            style: {
              fontSize: '12px',
              marginTop: `-${topDistance - 4}px`,
              ...expand({
                padding: '0px',
              }),
            },
          },
          Track: {
            style: {
              backgroundImage:
                value.length === 1
                  ? `linear-gradient(to right, ${theme.colors.accent} 0,${theme.colors.accent} ${thumbSize / 2}px, ${
                      theme.colors.gray200
                    } ${thumbSize / 2}px, ${theme.colors.gray200} 100%)`
                  : 'none',
              backgroundColor: value.length === 2 ? theme.colors.gray200 : 'transparent',
              borderRadius: '4px',
              ...expand({
                padding: '0',
                margin: '16px 0px',
              }),
            },
          },
          ...props.overrides,
        }}
        min={Number(min)}
        max={Number(max)}
        step={Number(step)}
        disabled={disabled}
      />
      {editable && (
        <div className={css({ marginLeft: '8px' })}>
          {value.length === 1 ? (
            <StyledSliderInput value={value[0]} onChange={(e) => onChangeInputValue(e, 0)} disabled={disabled} />
          ) : (
            <StyledSliderInput value={value[1]} onChange={(e) => onChangeInputValue(e, 1)} disabled={disabled} />
          )}
        </div>
      )}
    </div>
  );
}

function StyledSliderInput(props: InputProps) {
  const { value } = props;

  return (
    <StyledInput
      value={value}
      overrides={{
        Root: {
          style: {
            width: '52px',
          },
        },
        InputContainer: {
          style: {
            justifyContent: 'center',
          },
        },
        Input: {
          style: {
            textAlign: 'center',
            fontSize: '14px',
            lineHeight: '16px',
            ...expand({
              padding: '7px 3px', // 1px for border
            }),
            maxWidth: '50px', // including padding width
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          },
        },
      }}
      {...props}
    />
  );
}
