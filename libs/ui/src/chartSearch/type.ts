export const DIRECTION = {
  left: -1,
  both: 0,
  right: 1,
};

export const SEARCH_KEYWORDS = {
  and: ',',
  showSchema: 'SHOW SCHEMA',
  func: 'FUNC',
  query: 'QUERY',
  param: 'PARAM',
  combo: 'combo',
  vertex: 'vertex',
  edge: 'edge',
};

export interface SearchItem {
  id: string;
  data: string;
  type: string;
  alias?: string;
  // 0: bidirectional,
  // 1: to the right,
  // 2: to the left
  direction?: 0 | 1 | 2;
  from?: string;
  to?: string;
  shouldBeBold?: string;
  edgePairs?: Array<any>;
  paramName?: string;
  paramType?: string;
  elementType?: string;
  paramTypeReadonly?: boolean;
  paramGlobalInput?: string;
  paramOperator?: string;
  paramList?: { value: string; isCreatable: boolean }[];
  vertexType?: string;
  vertexTypeGlobalInput?: string;
  orderBy?: { asc: boolean; expression: { type: string; value: string }; label: string }[];
}

export interface SearchLabelProps {
  item: SearchItem;
  color?: string;
  text?: string;
  shouldBeBold?: string;
  size?: string;
}
