import React from 'react';
import { useStyletron } from 'baseui';
import SearchBoldedText from './searchBoldedText';
import { getContrastTextColor, stringToColor } from '../utils';
import { DIRECTION, SearchLabelProps, SEARCH_KEYWORDS } from './type';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faCircleNodes, faCode } from '@fortawesome/free-solid-svg-icons';

export default function SearchLabel({
  item,
  color = '',
  text = '',
  shouldBeBold = '',
  size = 'default',
}: SearchLabelProps) {
  const [css] = useStyletron();
  const { data, type, direction } = item;
  const displayWord = text.length > 0 ? text : data;
  const height = size === 'compact' ? '20px' : '24px';
  const edgeHeight = size === 'compact' ? '14px' : '16px';
  const backgroundColor = color || stringToColor(data);
  const textColor = getContrastTextColor(backgroundColor);

  if (type === SEARCH_KEYWORDS.func) {
    shouldBeBold = shouldBeBold.replace(/\s+/g, ' ');
  } else {
    shouldBeBold = shouldBeBold.replace(/\s+/g, '_');
  }

  return (
    <div
      className={css({
        display: 'flex',
        height: height,
        alignItems: 'center',
        whiteSpace: 'nowrap',
      })}
    >
      {type === SEARCH_KEYWORDS.vertex && (
        <div
          className={css({
            display: 'flex',
            height: '100%',
            alignItems: 'center',
            padding: '0 12px',
            borderRadius: '50em',
            backgroundColor: backgroundColor,
            color: textColor,
            fontWeight: 400,
            maxWidth: '100%',
            boxSizing: 'border-box',
          })}
        >
          <SearchBoldedText text={displayWord} shouldBeBold={shouldBeBold} size={size} />
        </div>
      )}
      {type === SEARCH_KEYWORDS.edge && (
        <div
          className={css({
            display: 'flex',
            alignItems: 'center',
            color: textColor,
            fontWeight: 400,
          })}
        >
          {direction !== DIRECTION.right && (
            <span
              className={css({
                width: '0',
                height: '0',
                borderTop: '12px solid transparent',
                borderBottom: '12px solid transparent',
                borderRight: `20px solid ${backgroundColor}`,
              })}
            />
          )}
          <span
            className={css({
              display: 'flex',
              alignItems: 'center',
              height: edgeHeight,
              backgroundColor: backgroundColor,
              padding: '0 12px',
            })}
          >
            <SearchBoldedText text={displayWord} shouldBeBold={shouldBeBold} size={size} />
          </span>
          {direction !== DIRECTION.left && (
            <span
              className={css({
                width: '0',
                height: '0',
                borderTop: '12px solid transparent',
                borderBottom: '12px solid transparent',
                borderLeft: `20px solid ${backgroundColor}`,
              })}
            />
          )}
        </div>
      )}
      {type !== SEARCH_KEYWORDS.vertex && type !== SEARCH_KEYWORDS.edge && (
        <div
          className={css({
            display: 'flex',
            alignItems: 'center',
            paddingRight: '12px',
            paddingLeft: type === SEARCH_KEYWORDS.func || type === SEARCH_KEYWORDS.query ? '0px' : '12px',
          })}
        >
          {type === SEARCH_KEYWORDS.func && data === SEARCH_KEYWORDS.showSchema && (
            <div className={css({ marginRight: '8px' })}>
              <FontAwesomeIcon icon={faCircleNodes as IconProp} />
            </div>
          )}
          {type === SEARCH_KEYWORDS.query && (
            <div className={css({ marginRight: '8px' })}>
              <FontAwesomeIcon icon={faCode as IconProp} />
            </div>
          )}
          <SearchBoldedText
            text={type === SEARCH_KEYWORDS.param ? `${item.paramName}: ${displayWord}` : displayWord}
            shouldBeBold={shouldBeBold}
            size={size}
          />
        </div>
      )}
    </div>
  );
}
