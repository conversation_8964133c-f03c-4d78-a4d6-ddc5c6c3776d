import React from 'react';
import { Input } from '@tigergraph/app-ui-lib/input';
import { InputProps, SIZE } from 'baseui/input';
import { expand } from 'inline-style-expand-shorthand';
import { mergeOverrides } from 'baseui';
import { Overrides } from 'baseui/overrides';
import { CustomTheme } from '@tigergraph/app-ui-lib/Theme';

export default function StyledInput({ overrides, ...props }: InputProps) {
  return (
    <Input
      size={SIZE.compact}
      overrides={mergeOverrides(
        {
          Root: {
            style: (props) => {
              const { $isFocused, $theme } = props;
              let border = $isFocused ? `1px solid ${$theme.colors.accent}` : null;
              return {
                width: '100%',
                ...expand(border ? { border } : {}),
                fontSize: '14px',
                // @ts-ignore
                ...overrides?.Root?.['style'],
              };
            },
          },
          ClearIcon: {
            props: {
              overrides: {
                Svg: {
                  style: ({ $theme }: { $theme: CustomTheme }) => ({
                    color: $theme.colors.gray300,
                  }),
                },
              },
            },
          },
        },
        overrides as Overrides<any>
      )}
      {...props}
    />
  );
}
