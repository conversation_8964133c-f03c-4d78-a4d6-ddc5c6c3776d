import React from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { PLACEMENT, TRIGGER_TYPE } from 'baseui/popover';
import { MdHelp } from 'react-icons/md';
import { ReactNode } from 'react';

type Props = {
  children: ReactNode;
  content?: ReactNode;
};

export default function HelpText(props: Props) {
  const [css, theme] = useStyletron();

  const { children, content } = props;
  return (
    <div className={css({ display: 'flex', alignItems: 'center' })}>
      <div className={css({ marginRight: '4px' })}>{children}</div>
      {content && (
        <StatefulPopover
          placement={PLACEMENT.top}
          triggerType={TRIGGER_TYPE.hover}
          content={content}
          overrides={{
            Body: {
              style: {
                maxWidth: '273px',
              },
            },
          }}
        >
          <div className={css({ display: 'flex', alignItems: 'center' })}>
            <MdHelp
              size={13}
              className={css({ color: theme.colors.gray300, ':hover': { color: theme.colors.gray600 } })}
            />
          </div>
        </StatefulPopover>
      )}
    </div>
  );
}
