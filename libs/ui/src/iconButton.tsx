import { styled } from '@tigergraph/app-ui-lib/Theme';

const IconButton = styled('button', ({ $theme }) => {
  return {
    border: 'none',
    outline: 'none',
    cursor: 'pointer',
    padding: 0,
    lineHeight: 0,
    borderRadius: '4px',
    backgroundColor: 'transparent',
    ':hover': {
      backgroundColor: $theme.colors.gray50,
    },
    ':active': {
      backgroundColor: $theme.colors.gray200,
    },
    ':focus-visible': {
      outline: `3px solid ${$theme.colors.borderSelected}`,
    },
    ':disabled': {
      cursor: 'not-allowed',
    },
  };
});

export default IconButton;
