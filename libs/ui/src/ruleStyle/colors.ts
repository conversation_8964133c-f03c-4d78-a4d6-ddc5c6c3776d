//  https://github.com/d3/d3-scale-chromatic
//  https://colorbrewer2.org/#type=sequential&scheme=BuGn&n=3
//  https://www.d3indepth.com/scales/#scalesqrt

import {
  interpolateBlues,
  schemeBlues,
  interpolateGreens,
  schemeGreens,
  interpolateOranges,
  schemeOranges,
  interpolateReds,
  schemeReds,
  interpolateBrBG,
  schemeBrBG,
  interpolateRdYlGn,
  schemeRdYlGn,
} from 'd3-scale-chromatic';
import { scaleOrdinal, ScaleOrdinal } from 'd3-scale';
import { Palate } from './type';
import { Color } from '@tigergraph/models/gvis/insights';

let colors: string[] = [];
let color = new Color();
// we have at most 60 different color
for (let i = 0; i < 60; i++) {
  colors.push(color.getColor(`${i}`));
}

const categoricalColors: Palate[] = [
  {
    name: 'schemePaired',
    colors: colors,
    type: 'categorical',
  },
];

// sequential and diverging continual color
const continualColors: Palate[] = [
  // single hue
  {
    name: 'Blues',
    interpolate: interpolateBlues,
    colors: schemeBlues[9],
    type: 'sequential',
  },
  {
    name: 'Greens',
    interpolate: interpolateGreens,
    colors: schemeGreens[9],
    type: 'sequential',
  },
  {
    name: 'Oranges',
    interpolate: interpolateOranges,
    colors: schemeOranges[9],
    type: 'sequential',
  },
  {
    name: 'Red',
    interpolate: interpolateReds,
    colors: schemeReds[9],
    type: 'sequential',
  },
  {
    name: 'BrBG',
    interpolate: interpolateBrBG,
    colors: schemeBrBG[11],
    type: 'diverging',
  },
  {
    name: 'RdYlGn',
    interpolate: interpolateRdYlGn,
    colors: schemeRdYlGn[11],
    type: 'diverging',
  },
];

export function getContinualColorsPalates(): Palate[] {
  return continualColors;
}

export function getContinualPalate(name: string): Palate | null {
  const palates = getContinualColorsPalates();
  for (let palate of palates) {
    if (palate.name === name) {
      return palate;
    }
  }
  return null;
}

export function getCategoricalPalates(): Palate[] {
  return categoricalColors;
}

export function getCategoricalPalate(name: string): Palate | null {
  const palates = getCategoricalPalates();
  return palates[0];
}

// for categories color, we may not known/or calculate the domain in advance
const cachedScales: Record<string, ScaleOrdinal<string, string>> = {};

// for input, we convert it to string
// for output, it's color string
export function getScaleOrdinal(palateName: string): ScaleOrdinal<string, string> {
  if (cachedScales[palateName]) {
    return cachedScales[palateName];
  }
  const palate = getCategoricalPalate(palateName);
  const scale = scaleOrdinal<string, string>().range(palate?.colors || []);
  cachedScales[palateName] = scale;
  return scale;
}

export function getColorGradient(palate: Palate): string {
  if (!palate) {
    return '';
  }

  const { colors } = palate;

  const stops: string[] = [];
  const gap = 100 / (colors.length - 1);
  for (let i = 0; i < colors.length; i++) {
    stops.push(`${colors[i]} ${i * gap}%`);
  }

  return `linear-gradient(90deg, ${stops.join(',')})`;
}
