import { Rule, FieldType, Rules } from './type';
import { StyleObject } from 'styletron-standard';
import { getContinualPalate, getScaleOrdinal } from './colors';
import { scaleSqrt, scaleSequential } from 'd3-scale';
import { GlobalParams } from '../globalParams';

const NODE_RADIUS = 75;

// evaluate based on rules, return css style object
export function evaluateRules(object: any, rules: Rules = [], globalParams?: GlobalParams): StyleObject {
  let evaluatedStyle: StyleObject = {};
  for (let rule of rules) {
    const {
      fieldName,
      fieldType,
      condition,
      conditionValue,
      conditionStartValue,
      conditionEndValue,
      styleKey,
      styleType,
      styleValue,
      palateName,
      styleStartValue,
      styleEndValue,
    } = rule;

    const fieldValue = object[fieldName];

    if (!condition || fieldValue === undefined || fieldValue === null) {
      continue;
    }

    if (condition === 'range') {
      if (!styleStartValue || !styleEndValue) {
        continue;
      }
    } else if (condition === 'unique value' || 'always') {
      // no check for unique value
    } else {
      if (!conditionValue || !styleValue) {
        continue;
      }
    }

    // the first rule that matches will be used
    // @ts-ignore
    if (condition === 'always' || (!evaluateRules[styleKey] && evaluateCondition(fieldValue, fieldType, rule, globalParams))) {
      if (condition === 'range') {
        if (styleType === 'color') {
          const palate = getContinualPalate(palateName);
          if (!palate) {
            continue;
          }

          const scale = scaleSequential()
            .domain([conditionStartValue, conditionEndValue])
            .interpolator(palate.interpolate!);

          evaluatedStyle[styleKey] = scale(fieldValue);
        } else if (styleType === 'numeric' || styleType === 'edgetype') {
          // todo(lin): for now, we only support node-radius
          const scale = scaleSqrt()
            .domain([conditionStartValue, conditionEndValue])
            .range([
              Number(styleStartValue) * NODE_RADIUS * NODE_RADIUS,
              Number(styleEndValue) * NODE_RADIUS * NODE_RADIUS,
            ]);

          const areaSize = scale(fieldValue);
          // cytoscape only support width/height
          evaluatedStyle[styleKey] = Math.sqrt(areaSize);
        }
      } else if (condition === 'unique value') {
        const colorMapping = getScaleOrdinal(palateName);
        evaluatedStyle[styleKey] = colorMapping(`${fieldValue}`);
      } else {
        if (styleType === 'color') {
          evaluatedStyle[styleKey] = styleValue;
        } else if (styleType === 'numeric' || styleType === 'edgetype') {
          if (styleKey === 'node-radius') {
            evaluatedStyle[styleKey] = Math.sqrt(NODE_RADIUS * NODE_RADIUS * Number(styleValue));
          } else {
            evaluatedStyle[styleKey] = styleValue;
          }
        }
      }
    }
  }
  return evaluatedStyle;
}

// return whether the condition is met.
export function evaluateCondition(
  fieldValue: any,
  fieldType: FieldType,
  rule: Rule,
  globalParams?: GlobalParams
): boolean {
  let { condition, conditionValue, conditionStartValue, conditionEndValue } = rule;

  if (condition === 'unique value') {
    return true;
  }
  if (condition === 'range') {
    return fieldValue >= conditionStartValue && fieldValue <= conditionEndValue;
  }

  let realConditionValue: string | number = conditionValue;

  //  we want to support dynamic conditionValue like ${variableName}
  let str = conditionValue.trim();
  if (str.startsWith('${') && str.endsWith('}')) {
    let variableName = str.slice(2, str.length - 1).trim();
    let param = globalParams ? globalParams[variableName] : undefined;
    if (param) {
      switch (param.type) {
        case 'DATETIME':
        case 'NUMBER':
        case 'STRING':
          realConditionValue = param.value;
          break;

        default:
          break;
      }
    }
  }

  if (fieldType === 'number') {
    realConditionValue = Number(realConditionValue);

    fieldValue = Number(fieldValue);
  }

  switch (condition) {
    case '=':
      return fieldValue === realConditionValue;
    case '!=':
      return fieldValue !== realConditionValue;
    case '<':
      return fieldValue < realConditionValue;
    case '<=':
      return fieldValue <= realConditionValue;
    case '>':
      return fieldValue > realConditionValue;
    case '>=':
      return fieldValue >= realConditionValue;
    default:
      return false;
  }
}

export function ruleConditionDescription(rule: Rule): string {
  const { condition, conditionValue, conditionStartValue, conditionEndValue } = rule;

  if (condition === 'range') {
    return `${conditionStartValue.toFixed(3)}-${conditionEndValue.toFixed(3)} `;
  } else if (condition === 'unique value') {
    return condition;
  }

  return `${condition}${conditionValue}`;
}

// return undefined when is valid
export function validateRule(rule: Rule): string | undefined {
  const {
    fieldName,
    condition,
    conditionValue,
    conditionStartValue,
    conditionEndValue,
    styleKey,
    styleType,
    styleValue,
    styleStartValue,
    styleEndValue,
  } = rule;

  if (condition === 'always') {
    return;
  }

  if (!fieldName) {
    return 'condition is not valid.';
  }

  if (!styleKey) {
    return 'style is not valid.';
  }

  if (condition === 'range') {
    if (conditionStartValue < conditionEndValue && styleStartValue && styleEndValue) {
      return;
    }

    if (!styleStartValue || !conditionStartValue) {
      return `min point is not valid.`;
    }
    if (!styleEndValue || !conditionEndValue) {
      return `max point is not valid.`;
    }

    return `min point value should less than max point value`;
  } else if (condition === 'unique value') {
    if (styleType === 'color') {
      return;
    }
    return `unique value can only be use to custom color style`;
  }

  if (conditionValue && styleValue) {
    return;
  }

  if (!conditionValue) {
    return 'condition is not valid.';
  }
  if (!styleValue) {
    return 'style is not valid.';
  }

  return 'unknown error.';
}
