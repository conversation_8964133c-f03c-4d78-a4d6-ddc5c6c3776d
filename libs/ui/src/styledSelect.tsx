import React from 'react';
import { Select } from '@tigergraph/app-ui-lib/select';
import { SelectProps, SIZE } from 'baseui/select';
import { expand } from 'inline-style-expand-shorthand';
import { mergeOverrides } from 'baseui';
import { Overrides } from 'baseui/overrides';
import { CustomTheme } from '@tigergraph/app-ui-lib/Theme';

export default function StyledSelect({ overrides, ...props }: SelectProps) {
  return (
    <Select
      onBlurResetsInput={false}
      size={SIZE.compact}
      {...props}
      overrides={mergeOverrides(
        {
          DropdownListItem: {
            style: ({ $theme }) => ({
              ':hover': {
                backgroundColor: $theme.colors.gray100,
              },
              ':active': {
                backgroundColor: $theme.colors.secondary300,
              },
            }),
          },
          Popover: {
            props: {
              ignoreBoundary: true,
              popperOptions: {
                modifiers: {
                  preventOverflow: {
                    enabled: false,
                  },
                  hide: {
                    enabled: false,
                  },
                },
              },
              overrides: {
                Body: {
                  style: {
                    'z-index': 4,
                  },
                },
              },
            },
          },
          Root: {
            style: {
              display: 'inline-block',
              width: '100%',
              fontSize: '14px',
            },
          },
          ClearIcon: {
            props: {
              overrides: {
                Svg: {
                  style: ({ $theme }: { $theme: CustomTheme }) => ({
                    color: $theme.colors.gray300,
                  }),
                },
              },
            },
          },
          Dropdown: {
            style: {
              maxHeight: 'min(90vh, 400px)',
            },
          },
          IconsContainer: {
            style: ({ $theme }) => ({
              color: $theme.colors.accent,
            }),
          },
          Input: {
            style: {
              height: '16px',
              lineHeight: '16px',
            },
          },
          SelectArrow: {
            props: {
              overrides: {
                Svg: {
                  style: ({ $theme }: { $theme: CustomTheme }) => ({
                    backgroundColor: $theme.colors.secondary300,
                    borderRadius: '20px',
                    color: $theme.colors.accent,
                  }),
                },
              },
            },
          },
          ValueContainer: {
            style: {
              ...expand({
                padding: '5px 0 5px 15px',
              }),
            },
          },
          Tag: {
            props: {
              overrides: {
                Root: {
                  style: {
                    width: 'fit-content',
                    maxWidth: '100%',
                  },
                },
                Text: {
                  style: {
                    maxWidth: '100%',
                  },
                },
              },
            },
          },
        },
        overrides as Overrides<any>
      )}
    />
  );
}
