import { ExternalGraph } from '@tigergraph/models/gvis/insights';
import { KIND } from 'baseui/toast';
import { Spinner } from '@tigergraph/app-ui-lib/spinner';
import { Core, NodeCollection, NodeSingular, EventHandler, EventObject, CollectionReturnValue } from 'cytoscape';
import React, { useEffect, useState, useCallback, useMemo, MutableRefObject, ReactNode } from 'react';
import { NavigateFunction } from 'react-router';

import { showToast } from '../../styledToasterContainer';
import { useMutationExpansionNode, useMutationShortestPath } from '../api';
import { useGraphContext } from '../context';
import { addGraph, canonicalizeGraph, convertGraphToCytoscape, getRelatedNodeTypes } from '../data';
import { Schema, UndoRedoInstance } from '../type';
import { addElements, elementsToGraph, getElementAttributes, getErrorMessage } from '../util';
import { Menu, MenuItem } from './DropdownMenu';
import { useStyletron } from 'baseui';
import { runLouvain } from '../graphAnalysis/utils';
import { PageRankSettings } from '../graphAnalysis/types';
import { pageRankDefaultSettings } from '../graphAnalysis';
import { Action, ActionParam } from '../../action/type';
import { GlobalParams } from '../../globalParams';
import { buildLinkFromAction } from '../../action/util';
import { ExternalNode, ExternalLink } from '@tigergraph/models/gvis/insights';

function hightSelected(cy: Core) {
  const allSelectedNodes = cy.$('node:selected');
  allSelectedNodes.removeClass('inactive');
  cy.nodes().not(allSelectedNodes).addClass('inactive');

  const allSelectedEdges = cy.$('edge:selected');
  allSelectedEdges.removeClass('inactive');
  cy.edges().not(allSelectedEdges).addClass('inactive');
}

export default function ContextMenu({
  layout,
  actionsByType,
  navigate,
  links,
  getLink,
  globalParameters,
  isEditMode,
  undoRedoRef,
  settings,
  onSettingUpdate,
  onShowAnalysis,
  onVertexDoubleClick,
}: {
  layout: string;
  actionsByType: Record<string, Action[]>;
  navigate?: NavigateFunction;
  links?: { pageName: string; pageID: string; params: GlobalParams }[];
  getLink?: (pageID: string, params: Record<string, string>) => string;
  globalParameters?: GlobalParams;
  isEditMode: boolean;
  undoRedoRef: MutableRefObject<UndoRedoInstance | null>;
  settings: Record<string, any>;
  onSettingUpdate: (key: string, value: any) => void;
  onShowAnalysis: () => void;
  onVertexDoubleClick?: (element: ExternalNode | ExternalLink | undefined) => void;
}) {
  const { isSchemaGraph, schema, graphName, graphData, graphT } = useGraphContext();
  if (graphT.core.type !== 'canvas') {
    throw new Error('only support cytoscape!');
  }

  const cy = graphT.core.cy;

  const [css] = useStyletron();

  const [rightClickEvent, setRightClickEvent] = useState<MouseEvent | null>(null);
  const [clickedNode, setClickedNode] = useState<NodeSingular | null>(null);

  const { mutate: mutateExpandNode, isLoading: isExpandNodeLoading } = useMutationExpansionNode();
  const { mutate: mutateShortestPath, isLoading: isShortestPathLoading } = useMutationShortestPath();

  const [lastSelections, setLastSelections] = useState<string[]>([]);

  const { EdgeTypes: edgeTypes, VertexTypes: vertexTypes, DiscriminatorMap: discriminatorMap } = schema;

  const vTypes = useMemo(() => {
    return vertexTypes.reduce((acc, v) => {
      acc.push(v['Name']);
      return acc;
    }, [] as string[]);
  }, [vertexTypes]);

  const eTypes = useMemo(() => {
    return edgeTypes.reduce((acc, e) => {
      acc.push(e['Name']);
      if (e['Config']['REVERSE_EDGE']) {
        acc.push(e['Config']['REVERSE_EDGE']);
      }
      return acc;
    }, [] as string[]);
  }, [edgeTypes]);

  const onResponse = useCallback(
    (newGraph: ExternalGraph, target: NodeCollection, highLightAll: boolean) => {
      newGraph = canonicalizeGraph(newGraph, schema);
      // no need to call removeDuplicateEdges as later the addGraph/addElements will prevent duplicate node/edges

      const newElements = convertGraphToCytoscape(newGraph);
      const expandedElements = addElements(cy, newElements);

      if (expandedElements.length > 0 || (highLightAll && newElements.length > 0)) {
        if (expandedElements.length + cy.elements.length > 5000) {
          showToast({
            kind: KIND.warning,
            message: `This widget may take longer to load due to a volume of vertices and edges exceeding 5000.`,
          });
        }

        if (!undoRedoRef.current) {
          return;
        }

        const { deltaGraph } = addGraph(graphData, newGraph);
        undoRedoRef.current.do('add', {
          eles: expandedElements,
          deltaGraph,
        });

        // highligh the expansion starting node
        target.style({
          'underlay-color': 'rgb(251, 154, 68)',
        });

        target.one('unselect', (evt) => {
          evt.target.removeStyle('underlay-color');
        });

        // wait until cytoscape get latest elements
        setTimeout(() => {
          graphT.runLayout(
            {
              randomize: false,
              fit: false,
              centerGraph: false,
            },
            () => {
              setTimeout(
                () => {
                  const zoom = cy.zoom();
                  cy.maxZoom(zoom);

                  if (target.length === 1) {
                    // !! we need to get the latest target position
                    target = cy.getElementById(target[0].id());
                    cy.animate({
                      center: {
                        eles: target,
                      },
                      duration: 300,
                      complete: () => {
                        cy.maxZoom(10);
                      },
                    });
                  } else {
                    // get the latest position for eles
                    let collection = cy.collection();
                    target.forEach((ele) => {
                      collection = collection.union(cy.getElementById(ele.id()));
                    });
                    for (let element of expandedElements) {
                      collection = collection.union(cy.getElementById(element.data.id!));
                    }

                    cy.animate({
                      fit: {
                        eles: collection,
                        padding: 24,
                      },
                      duration: 300,
                      complete: () => {
                        cy.maxZoom(10);
                      },
                    });
                  }
                },
                // for infinitive force layout, the onLayoutStop is called at the very beginning
                // so we need to wait extra time so the layout is stable
                layout === 'cola' ? 100 : 100
              );
            }
          );
        });

        // need setTimeout so select can take effect
        setTimeout(() => {
          const collection = cy.collection();
          // we need to highlight the new expanded elements or all new elements.
          const highLightElements = highLightAll ? newElements : expandedElements;
          for (let ele of highLightElements) {
            collection.merge(cy.getElementById(ele.data.id!));
          }

          // perf optimization: only select when the collection is small
          if (collection.length <= 100) {
            collection.select();
          }
        });
      } else {
        showToast({
          kind: KIND.info,
          message: 'Empty result',
        });
      }
    },
    [cy, graphT, graphData, undoRedoRef, layout, schema]
  );

  const onExpandNode = useCallback(
    (target: NodeCollection, relatedType?: string) => {
      const nodeID = target.first().data('nodeID');
      const type = target.first().data('type') as string;

      mutateExpandNode(
        {
          graphName,
          id: nodeID,
          vTypes,
          eTypes,
          type,
          relatedType,
          discriminatorMap,
        },
        {
          onSuccess: (newGraph) => {
            onResponse(newGraph, target, false);
          },
          onError: (err) => {
            showToast({
              kind: KIND.negative,
              message: `Vertex expansion failed: \n${getErrorMessage(err)}`,
            });
          },
        }
      );
    },
    [eTypes, vTypes, graphName, discriminatorMap, mutateExpandNode, onResponse]
  );

  const onFindShortestPath = useCallback(
    (algorithm: 'oneShortestPath' | 'allShortestPath') => {
      if (!cy) {
        return;
      }

      const currentSelection = cy.$('node:selected');
      if (currentSelection.length !== 2) {
        showToast({
          kind: KIND.warning,
          message:
            currentSelection.length < 2
              ? `please first use shift/ctrl + click to select source and target vertex`
              : 'there are more than two vertices selected, make sure only select two vertices.',
        });
        return;
      }

      let source = currentSelection[0];
      let target = currentSelection[1];

      // we only track the last two selection
      // check if target' id is equal to last selection
      // if not, swap source and target
      if (target.id() !== lastSelections[1]) {
        const temp = source;
        source = target;
        target = temp;
      }

      mutateShortestPath(
        {
          graphName,
          vTypes,
          eTypes,
          sourceID: source.data('nodeID'),
          sourceType: source.data('type') as string,
          targetID: target.data('nodeID'),
          targetType: target.data('type') as string,
          allShortestPaths: algorithm === 'allShortestPath',
          allPath: false,
          discriminatorMap,
        },
        {
          onSuccess: (newGraph) => {
            onResponse(newGraph, currentSelection, true);
          },
          onError: (err) => {
            showToast({
              kind: KIND.negative,
              message: `Find shortest path failed: \n${getErrorMessage(err)}`,
            });
          },
        }
      );
    },
    [cy, eTypes, vTypes, graphName, lastSelections, discriminatorMap, mutateShortestPath, onResponse]
  );

  const onFindCommunity = useCallback(() => {
    if (!undoRedoRef.current) {
      return;
    }
    const louvainOutput = runLouvain(graphData);
    undoRedoRef.current.do('applyLouvainResult', {
      louvainOutput,
    });
  }, [graphData, undoRedoRef]);

  const onRunPageRank = useCallback(
    (pageRankSettings: Partial<PageRankSettings> = {}) => {
      const currentAlgorithmSettings = settings.algorithm;
      if (currentAlgorithmSettings?.type !== 'pageRank') {
        onSettingUpdate('algorithm', { ...pageRankDefaultSettings, ...pageRankSettings });
      } else {
        onSettingUpdate('algorithm', { ...currentAlgorithmSettings, ...pageRankSettings });
      }
      onShowAnalysis();
    },
    [onSettingUpdate, onShowAnalysis, settings.algorithm]
  );

  const onRemoveNode = useCallback(
    (target) => {
      if (!undoRedoRef.current) {
        return;
      }

      const deltaGraph = elementsToGraph(graphData, target);

      undoRedoRef.current.do('remove', {
        eles: target,
        deltaGraph,
      });

      // run layout after remove elements
      setTimeout(() => {
        graphT.runLayout({
          randomize: false,
          fit: false,
          centerGraph: false,
        });
      });
    },
    [graphData, undoRedoRef, graphT]
  );

  useEffect(() => {
    cy.on('select', 'node', (event) => {
      const id = event.target.first().id();
      setLastSelections((lastSelections) => {
        return lastSelections.concat(id).slice(0, 2);
      });
    });
  }, [cy, setLastSelections]);

  useEffect(() => {
    const onDoubleTap = (evt: EventObject) => {
      if (isSchemaGraph || onVertexDoubleClick) {
        return;
      }
      onExpandNode(evt.target);
      return cy;
    };

    cy.on('dbltap', 'node', onDoubleTap);

    return () => {
      cy.off('dbltap', 'node', onDoubleTap);
    };
  }, [cy, isSchemaGraph, onExpandNode, onVertexDoubleClick]);

  useEffect(() => {
    const onCxttap: EventHandler = (event) => {
      setRightClickEvent(event.originalEvent);

      const isClickOnNode = event.target.isNode && event.target.isNode();
      if (isClickOnNode) {
        const node = (event.target as NodeCollection).first();
        setClickedNode(node);
      } else {
        setClickedNode(null);
      }
    };
    cy.on('cxttap', onCxttap);

    const onViewport: EventHandler = () => {
      setRightClickEvent(null);
    };
    cy.on('viewport', onViewport);

    return () => {
      cy.off('ctxtap', onCxttap);
      cy.off('viewport', onViewport);
    };
  }, [cy]);
  // for click out side of context menu is already handled by Menu component.

  const loading = (
    <div
      className={css({
        position: 'absolute',
        top: '12px',
        left: 0,
        right: 0,
        display: 'flex',
        justifyContent: 'center',
      })}
    >
      {isExpandNodeLoading || isShortestPathLoading ? <Spinner $size="small" /> : null}
    </div>
  );

  if (!rightClickEvent) {
    return <>{loading}</>;
  }

  if (isSchemaGraph) {
    return null;
  }

  let relatedNodeType: string[] = [];
  let actions: Action[] = [];
  const nodeType = clickedNode ? (clickedNode.data('type') as string) : '';
  if (clickedNode) {
    relatedNodeType = getRelatedNodeTypes(schema, nodeType);
    actions = actionsByType[nodeType] || [];
  }

  return (
    <>
      {loading}
      <Menu event={rightClickEvent}>
        {clickedNode ? (
          <>
            <Menu label="Explore Neighbours" key="Neighbours" showSeparator>
              <MenuItem label="All" onClick={() => onExpandNode(clickedNode)} showSeparator />
              {relatedNodeType.map((type) => (
                <MenuItem label={type} key={type} onClick={() => onExpandNode(clickedNode, type)} />
              ))}
            </Menu>
          </>
        ) : null}
        <Menu label="PathFinding" key="PathFinding">
          <MenuItem label="One shortest path" onClick={() => onFindShortestPath('oneShortestPath')} />
          <MenuItem label="All shortest paths" onClick={() => onFindShortestPath('allShortestPath')} />
        </Menu>
        <Menu label="Graph Analytics (Preview)" key="Analytics" showSeparator={clickedNode ? false : true}>
          <MenuItem label="Louvain" onClick={() => onFindCommunity()} />
          <MenuItem
            label="Page Rank"
            onClick={() => onRunPageRank(clickedNode ? { vertexType: clickedNode.data('type') } : {})}
          />
        </Menu>
        {clickedNode
          ? generateActionMenuItems(
              nodeType,
              clickedNode,
              isSchemaGraph,
              schema,
              actions,
              globalParameters,
              isEditMode,
              navigate,
              links,
              getLink
            )
          : null}
        {clickedNode ? (
          <>
            <Menu label="Highlight" key="Highlight" showSeparator>
              <MenuItem
                label="Show Up stream"
                onClick={() => {
                  clickedNode.union(clickedNode.predecessors()).select();

                  hightSelected(cy);
                }}
              />
              <MenuItem
                label="Show Down stream"
                onClick={() => {
                  clickedNode.union(clickedNode.successors()).select();

                  hightSelected(cy);
                }}
              />
              <MenuItem
                label="Show Up-Down stream"
                onClick={() => {
                  clickedNode.union(clickedNode.predecessors()).union(clickedNode.successors()).select();

                  hightSelected(cy);
                }}
              />
            </Menu>
          </>
        ) : null}
        <MenuItem
          label="Remove"
          key="Remove"
          onClick={() => {
            const allSelectedNodes = cy.$('node:selected');
            const connectedEdges = allSelectedNodes.connectedEdges();
            const allSelectedEdges = cy.$('edge:selected');

            onRemoveNode(allSelectedNodes.union(allSelectedEdges).union(connectedEdges));
          }}
        />
      </Menu>
    </>
  );
}

function generateActionMenuItems(
  nodeType: string,
  clickedNode: NodeSingular,
  isSchemaGraph: boolean,
  schema: Schema,
  actions: Action[],
  globalParameters: GlobalParams = {},
  isEditMode: boolean,
  navigate?: NavigateFunction,
  links: { pageName: string; pageID: string; params: GlobalParams }[] = [],
  getLink: (pageID: string, params: Record<string, string>) => string = () => ''
): ReactNode {
  if (isSchemaGraph || actions.length === 0) {
    return null;
  }

  if (!links || !getLink) {
    return null;
  }

  return (
    <>
      <Menu label="Actions" key="Actions" showSeparator>
        {actions.map((action, index) => {
          return (
            <MenuItem
              label={action.text}
              key={index}
              onClick={() => {
                const { attributes } = getElementAttributes(clickedNode.data(), isSchemaGraph, schema, true);

                const params: GlobalParams = links.find((link) => link.pageID === action.pageID)?.params || {};

                const getParam = (param: ActionParam) => {
                  // hand vertex type
                  if (params[param.name]?.type === 'VERTEX') {
                    const key = param.name;
                    return {
                      [key + '.type']: nodeType,
                      [key + '.id']: attributes['id'],
                    };
                  }

                  if (param.paramGlobalInput) {
                    return {
                      [param.name]: globalParameters[param.paramGlobalInput].value,
                    };
                  }
                  return {
                    [param.name]: param.isCreatable ? param.value : attributes[param.value],
                  };
                };
                const href = buildLinkFromAction(action, getLink, getParam, isEditMode);

                // if in edit mode, we want to open new tab
                if (navigate && action.pageID && !isEditMode) {
                  navigate(href);
                } else {
                  window.open(href, '_blank');
                }
              }}
            />
          );
        })}
      </Menu>
    </>
  );
}
