import * as React from 'react';
import { mergeRefs } from 'react-merge-refs';
import {
  useFloating,
  offset,
  flip,
  shift,
  useListNavigation,
  useHover,
  useTypeahead,
  useInteractions,
  useRole,
  useClick,
  useDismiss,
  autoUpdate,
  safePolygon,
  useFloatingTree,
  useFloatingNodeId,
  useFloatingParentNodeId,
  FloatingNode,
  FloatingTree,
  FloatingFocusManager,
} from '@floating-ui/react-dom-interactions';
import { BsChevronRight } from 'react-icons/bs';

import { injectCSS } from '../util';

interface MenuItemProps {
  label: React.ReactNode;
  disabled?: boolean;
  showSeparator?: boolean;
}

export const MenuItem = React.forwardRef<
  HTMLButtonElement,
  MenuItemProps & React.ButtonHTMLAttributes<HTMLButtonElement>
>(({ label, disabled, showSeparator, className, ...props }, ref) => {
  return (
    <button
      {...props}
      ref={ref}
      role="menuitem"
      disabled={disabled}
      className={`${className} ${showSeparator ? 'MenuItemSeparator' : ''}`}
    >
      {label}
    </button>
  );
});

interface MenuProps {
  label?: string;
  event?: MouseEvent;
  nested?: boolean;
  showSeparator?: boolean;
  children?: React.ReactNode;
}

export const MenuComponent = React.forwardRef<HTMLButtonElement, MenuProps & React.HTMLProps<HTMLButtonElement>>(
  ({ children, label, event, showSeparator, ...props }, forwardedRef) => {
    const [open, setOpen] = React.useState(false);
    const [activeIndex, setActiveIndex] = React.useState<number | null>(null);
    const [allowHover, setAllowHover] = React.useState(false);

    const listItemsRef = React.useRef<Array<HTMLButtonElement | null>>([]);
    const listContentRef = React.useRef(
      React.Children.map(children, (child) => (React.isValidElement(child) ? child.props.label : null)) as Array<
        string | null
      >
    );

    const tree = useFloatingTree();
    const nodeId = useFloatingNodeId();
    const parentId = useFloatingParentNodeId();
    const nested = parentId != null;

    const { x, y, reference, floating, strategy, context } = useFloating<HTMLButtonElement>({
      open,
      nodeId,
      onOpenChange: setOpen,
      placement: nested ? 'right-start' : 'bottom-start',
      middleware: [offset({ mainAxis: 4, alignmentAxis: nested ? -5 : 0 }), flip(), shift()],
      whileElementsMounted: autoUpdate,
    });

    const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([
      useHover(context, {
        handleClose: safePolygon({ restMs: 25 }),
        enabled: nested && allowHover,
        delay: { open: 75 },
      }),
      useClick(context, {
        toggle: !nested || !allowHover,
        event: 'mousedown',
        ignoreMouse: nested,
      }),
      useRole(context, { role: 'menu' }),
      useDismiss(context),
      useListNavigation(context, {
        listRef: listItemsRef,
        activeIndex,
        nested,
        onNavigate: setActiveIndex,
      }),
      useTypeahead(context, {
        listRef: listContentRef,
        onMatch: open ? setActiveIndex : undefined,
        activeIndex,
      }),
    ]);

    // Event emitter allows you to communicate across tree components.
    // This effect closes all menus when an item gets clicked anywhere
    // in the tree.
    React.useEffect(() => {
      function handleTreeClick() {
        setOpen(false);
      }

      tree?.events.on('click', handleTreeClick);
      return () => {
        tree?.events.off('click', handleTreeClick);
      };
    }, [tree]);

    // Determine if "hover" logic can run based on the modality of input. This
    // prevents unwanted focus synchronization as menus open and close with
    // keyboard navigation and the cursor is resting on the menu.
    React.useEffect(() => {
      function onPointerMove({ pointerType }: PointerEvent) {
        if (pointerType === 'mouse') {
          setAllowHover(true);
        }
      }

      function onKeyDown() {
        setAllowHover(false);
      }

      window.addEventListener('pointermove', onPointerMove, {
        once: true,
        capture: true,
      });
      window.addEventListener('keydown', onKeyDown, true);
      return () => {
        window.removeEventListener('pointermove', onPointerMove, {
          capture: true,
        });
        window.removeEventListener('keydown', onKeyDown, true);
      };
    }, [allowHover]);

    const referenceRef = React.useMemo(() => mergeRefs([reference, forwardedRef]), [reference, forwardedRef]);

    // for root menu, set bounding client rect for references
    React.useEffect(() => {
      if (nested) {
        return;
      }

      if (!event) {
        return;
      }

      function onContextMenu(e: MouseEvent) {
        e.preventDefault();
        referenceRef({
          // @ts-ignore
          getBoundingClientRect() {
            return {
              x: e.clientX,
              y: e.clientY,
              width: 0,
              height: 0,
              top: e.clientY,
              right: e.clientX,
              bottom: e.clientY,
              left: e.clientX,
            };
          },
        });
        setOpen(true);
      }

      onContextMenu(event);
    }, [referenceRef, nested, event]);

    return (
      <FloatingNode id={nodeId}>
        {/* hide button for root context menu  */}
        {nested ? (
          <button
            ref={referenceRef}
            {...getReferenceProps({
              ...props,
              className: `MenuItem ${open ? 'open' : ''} ${showSeparator ? 'MenuItemSeparator' : ''}`,
              onClick(event) {
                event.stopPropagation();
              },
              ...(nested && {
                // Indicates this is a nested <Menu /> acting as a <MenuItem />.
                role: 'menuitem',
              }),
            })}
          >
            {label}
            <span aria-hidden="true" style={{ marginLeft: 10 }}>
              <BsChevronRight />
            </span>
          </button>
        ) : null}
        {open && (
          <FloatingFocusManager
            context={context}
            // Prevent outside content interference.
            modal={!nested}
            // Only initially focus the root floating menu.
            initialFocus={nested ? -1 : 0}
            // Only return focus to the root menu when menus close.
            returnFocus={!nested}
            // Allow touch screen readers to escape the modal root menu
            // without selecting anything.
            visuallyHiddenDismiss
          >
            <div
              ref={floating}
              className="Menu"
              style={{
                position: strategy,
                top: y ? y : 0,
                left: x ? x : 0,
                width: 'max-content',
              }}
              {...getFloatingProps({
                // Pressing tab dismisses the menu and places focus
                // back on the trigger.
                onKeyDown(event) {
                  if (event.key === 'Tab') {
                    setOpen(false);
                  }
                },
              })}
            >
              {React.Children.map(
                children,
                (child, index) =>
                  React.isValidElement(child) &&
                  React.cloneElement(
                    child,
                    getItemProps({
                      tabIndex: activeIndex === index ? 0 : -1,
                      role: 'menuitem',
                      className: 'MenuItem',
                      ref(node: HTMLButtonElement) {
                        listItemsRef.current[index] = node;
                      },
                      onClick(event) {
                        child.props.onClick?.(event);
                        tree?.events.emit('click');
                      },
                      // Allow focus synchronization if the cursor did not move.
                      onPointerEnter() {
                        if (allowHover) {
                          setActiveIndex(index);
                        }
                      },
                    })
                  )
              )}
            </div>
          </FloatingFocusManager>
        )}
      </FloatingNode>
    );
  }
);

export const Menu = React.forwardRef<HTMLButtonElement, MenuProps & React.HTMLProps<HTMLButtonElement>>(
  (props, ref) => {
    const parentId = useFloatingParentNodeId();

    if (parentId == null) {
      return (
        <FloatingTree>
          <MenuComponent {...props} ref={ref} />
        </FloatingTree>
      );
    }

    return <MenuComponent {...props} ref={ref} />;
  }
);

injectCSS(`
  .RootMenu {
    padding: 6px 14px;
    border: none;
    font-size: 16px;
    background: none;
    border-radius: 6px;
    border: 1px solid #d7dce5;
  }

  .RootMenu.open,
  .RootMenu:hover {
    background: #eaecef;
  }

  .Menu {
    background: white;
    padding: 4px;
    border: 1px solid #d7dce5;
    border-radius: 6px;
    box-shadow: 2px 4px 12px rgba(0, 0, 0, 0.1);
    outline: 0;
    width: max-content;
  }

  .MenuItem {
    display: flex;
    justify-content: space-between;
    width: 100%;
    background: white;
    border: none;
    font-size: 16px;
    text-align: left;
    line-height: 1.8;
    min-width: 110px;
    outline: 0;
  }

  .MenuItem.open {
    background: #eaecef;
  }

  .MenuItem:focus,
  .MenuItem:not([disabled]):active {
    background: #eaecef;
  }

  button.MenuItemSeparator,
  .MenuItem.MenuItemSeparator {
    border-bottom: 1px solid #d4dadf;
  }
`);
