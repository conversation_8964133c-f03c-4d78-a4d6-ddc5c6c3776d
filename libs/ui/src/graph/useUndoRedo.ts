import { ExternalGraph } from '@tigergraph/models/gvis/insights';
import { useCallback, useEffect, useRef, useState } from 'react';

import cytoscape, { Ext, Core, ElementDefinition, CollectionReturnValue } from 'cytoscape';
// @ts-ignore
import undoRedo from 'cytoscape-undo-redo';

import { DetailedLouvainOutput } from 'graphology-communities-louvain';
import { applyLouvainResult, applyPageRankResult, PageRankResult } from './graphAnalysis/utils';
import { addGraph, removeGraph } from './data';
import { CytoscapeExtensions } from './type';

cytoscape.use(undoRedo as Ext);

export type UndoRedoInstance = {
  isUndoStackEmpty: () => boolean;
  isRedoStackEmpty: () => boolean;
  reset: () => void;
  undo: () => void;
  redo: () => void;
  do: (actionName: string, args: object) => object;
  action: (
    actionName: string,
    actionFunction: (args: object) => object,
    undoFunction: (args: object) => object
  ) => void;
};

function initUnDoRedo(
  undoRedoInstance: UndoRedoInstance,
  cy: Core,
  externalGraph: ExternalGraph,
  onSetGraph: (graph: ExternalGraph) => void,
  onAddGraph: (graph: ExternalGraph) => void,
  onRemoveGraph: (graph: ExternalGraph) => void,
  settings: Record<string, any>,
  onSettingUpdate: (key: string, value: any) => void
) {
  // 1 add
  undoRedoInstance.action(
    'add',
    (args) => {
      let { firstTime, eles, deltaGraph } = args as {
        firstTime: boolean;
        eles: ElementDefinition[] | CollectionReturnValue;
        deltaGraph: ExternalGraph;
      };
      // wait until undo/redo stack take effect
      setTimeout(() => {
        onAddGraph(deltaGraph);
      });
      if (firstTime) {
        // todo(lin)
        // add will take 5 - 10s if num of eles is large than 3000
        // but we haven't found any method to optimize this.
        return {
          eles,
          deltaGraph,
        };
      } else {
        return {
          eles,
          deltaGraph,
        };
      }
    },
    (args) => {
      let { deltaGraph } = args as {
        eles: CollectionReturnValue;
        deltaGraph: ExternalGraph;
      };
      setTimeout(() => {
        onRemoveGraph(deltaGraph);
      });
      return args;
    }
  );

  // 2 remove
  undoRedoInstance.action(
    'remove',
    (args) => {
      let { deltaGraph } = args as {
        eles: CollectionReturnValue;
        deltaGraph: ExternalGraph;
      };
      setTimeout(() => {
        onRemoveGraph(deltaGraph);
      });
      return {
        deltaGraph,
      };
    },
    (args) => {
      let { deltaGraph } = args as {
        eles: CollectionReturnValue;
        deltaGraph: ExternalGraph;
      };
      setTimeout(() => {
        onAddGraph(deltaGraph);
      });
      return args;
    }
  );

  undoRedoInstance.action(
    'applyLouvainResult',
    (args) => {
      let { louvainOutput } = args as {
        louvainOutput: DetailedLouvainOutput;
      };

      applyLouvainResult(cy, externalGraph, louvainOutput, onSetGraph, settings, onSettingUpdate, false);

      return {
        louvainOutput,
      };
    },
    (args) => {
      let { louvainOutput } = args as {
        louvainOutput: DetailedLouvainOutput;
      };

      applyLouvainResult(cy, externalGraph, louvainOutput, onSetGraph, settings, onSettingUpdate, true);

      return {
        louvainOutput,
      };
    }
  );

  undoRedoInstance.action(
    'applyPageRankResult',
    (args) => {
      let { pageRankResult } = args as {
        pageRankResult: PageRankResult;
      };

      applyPageRankResult(cy, externalGraph, pageRankResult, onSetGraph, settings, onSettingUpdate, false);

      return {
        pageRankResult,
      };
    },
    (args) => {
      let { pageRankResult } = args as {
        pageRankResult: PageRankResult;
      };

      applyPageRankResult(cy, externalGraph, pageRankResult, onSetGraph, settings, onSettingUpdate, true);

      return {
        pageRankResult,
      };
    }
  );
}

export default function useUndoRedo(
  cy: (Core & CytoscapeExtensions) | null,
  graph: ExternalGraph,
  onGraphChange: (graph: ExternalGraph) => void,
  settings: Record<string, any>,
  onSettingUpdate: (key: string, value: any) => void,
  runLayout: (extraParams?: object, onLayoutStop?: () => void) => void
) {
  const onAddGraph = useCallback(
    (deltaGraph: ExternalGraph) => {
      const { finalGraph } = addGraph(graph, deltaGraph);
      onGraphChange(finalGraph);
    },
    [graph, onGraphChange]
  );

  const onRemoveGraph = useCallback(
    (deltaGraph: ExternalGraph) => {
      const { finalGraph } = removeGraph(graph, deltaGraph);
      onGraphChange(finalGraph);
    },
    [graph, onGraphChange]
  );

  const undoRedoRef = useRef<UndoRedoInstance | null>(null);
  const [undoRedoState, setUndoRedoState] = useState<{
    isUndoStackEmpty: boolean;
    isRedoStackEmpty: boolean;
    undo: () => void;
    redo: () => void;
  }>({
    isUndoStackEmpty: true,
    isRedoStackEmpty: true,
    undo: () => {},
    redo: () => {},
  });

  const updateUndoRedoState = useCallback(() => {
    if (!undoRedoRef.current) {
      return;
    }
    setUndoRedoState({
      isUndoStackEmpty: undoRedoRef.current.isUndoStackEmpty(),
      isRedoStackEmpty: undoRedoRef.current.isRedoStackEmpty(),
      undo: () => {
        if (!undoRedoRef.current) {
          return;
        }
        undoRedoRef.current.undo();
        updateUndoRedoState();
      },
      redo: () => {
        if (!undoRedoRef.current) {
          return;
        }
        undoRedoRef.current.redo();
        updateUndoRedoState();
      },
    });
  }, [setUndoRedoState]);

  useEffect(() => {
    if (!cy) {
      return;
    }

    undoRedoRef.current = cy.undoRedo({ undoableDrag: false });
    initUnDoRedo(undoRedoRef.current, cy, graph, onGraphChange, onAddGraph, onRemoveGraph, settings, onSettingUpdate);

    const onAfterDo = () => {
      // for do, we need more control on layout (like center/fit elements)
      // so we will not run general layout here.
      updateUndoRedoState();
    };

    const onAfterUndoRedo = () => {
      updateUndoRedoState();
      // after redo/undo, we return layout
      setTimeout(() => {
        runLayout({
          randomize: false,
          fit: false,
          centerGraph: false,
        });
      });
    };

    // update undo/redo toolbar
    cy.on('afterDo', onAfterDo);
    cy.on('afterRedo', onAfterUndoRedo);
    cy.on('afterUndo', onAfterUndoRedo);
    return () => {
      cy.off('afterDo', onAfterDo);
      cy.off('afterRedo', onAfterUndoRedo);
      cy.off('afterUndo', onAfterUndoRedo);
    };
  }, [cy, graph, onGraphChange, onAddGraph, onRemoveGraph, updateUndoRedoState, settings, onSettingUpdate, runLayout]);

  return {
    undoRedoRef,
    undoRedoState,
  };
}
