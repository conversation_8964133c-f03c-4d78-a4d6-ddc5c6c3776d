import { Stylesheet, EdgeSingular, NodeSingular } from 'cytoscape';
import { iconMap } from './icons/iconMap';
import { Schema } from './type';
import { CustomTheme } from '@tigergraph/app-ui-lib/Theme';
import GsFileIcon from './icons/265-order-1.png';
import GsS3Icon from './icons/296-s3-file.png';
import GsGcsIcon from './icons/297-gcs-file.png';
import GsAbsIcon from './icons/298-abs-file.png';
import { Color } from '@tigergraph/models/gvis/insights';

// TODO(yuli): add underscores at the front of data source types, need sync with GST side.
export const dataSourceVertexTypes: {
  [type: string]: string;
} = {
  gs_file: GsFileIcon, // __gs_file
  gs_url: GsFileIcon, // __gs_url
  gs_s3: GsS3Icon, // __gs_s3
  gs_gcs: GsGcsIcon, // __gs_gcs
  gs_abs: GsAbsIcon, // __gs_abs
};
export const dataSourceEdgeType = 'gs_data_source_map'; // __gs_data_source_map

// !!!NOTE!!!
// For a given style property for a given element, the last matching selector wins.

export function createStyleSheet(schema: Schema, theme: CustomTheme, userUploadedIconPathPrefix?: string) {
  const { VertexTypes: vertexTypes, EdgeTypes: edgeTypes } = schema;
  const color = new Color();

  const vertexStyles: Stylesheet[] = vertexTypes.map((vertexType) => ({
    selector: `node[type = "${vertexType.Name}"]`,
    style: {
      'background-image':
        vertexType.style?.icon && iconMap[vertexType.style?.icon]
          ? `/studio/assets/gvis/icons/builtin/${iconMap[vertexType.style?.icon]}`
          : userUploadedIconPathPrefix && vertexType.style?.icon
          ? `${userUploadedIconPathPrefix}/${vertexType.style?.icon}`
          : 'none',
      'background-fit': 'cover',
      'background-repeat': 'no-repeat',
      'background-clip': 'none',
      'background-color': vertexType.style?.fillColor || color.getColor(vertexType.Name),
    },
  }));

  const edgeStyles: Stylesheet[] = edgeTypes.map((edgeType) => ({
    selector: `edge[type = "${edgeType.Name}"]`,
    style: {
      'line-color': edgeType.style?.fillColor || color.getColor(edgeType.Name),
      'target-arrow-color': edgeType.style?.fillColor || color.getColor(edgeType.Name),
    },
  }));

  const dataSourceVertexStyles: Stylesheet[] = Object.keys(dataSourceVertexTypes).map((type: string) => ({
    selector: `node[type = "${type}"]`,
    style: {
      // Display the source file name rather than the node id
      label: (element: NodeSingular) => {
        const label = element.data().__others?.items?.[0]?.text || element.data('nodeID');
        return label;
      },
      shape: 'rectangle',
      width: '34px',
      height: '42px',
      'underlay-padding': 20,
      'background-width': '80px',
      'background-height': '80px',
      'background-image': dataSourceVertexTypes[type],
      'background-repeat': 'no-repeat',
      'background-clip': 'none',
      'background-color': '#000',
    },
  }));

  const edgeHandlesStyles: Stylesheet[] = [
    {
      selector: '.eh-hover',
      style: {
        // @ts-ignore
        'underlay-color': '#A8A8A8',
        'underlay-padding': 10,
        'underlay-opacity': 0.6,
        'underlay-shape': 'ellipse',
      }
    },
    {
      selector: '.eh-source',
      style: {
        // @ts-ignore
        'underlay-color': '#A8A8A8',
        'underlay-padding': 10,
        'underlay-opacity': 0.6,
        'underlay-shape': 'ellipse',
      }
    },
    {
      selector: '.eh-target',
      style: {
        // @ts-ignore
        'underlay-color': '#A8A8A8',
        'underlay-padding': 10,
        'underlay-opacity': 0.6,
        'underlay-shape': 'ellipse',
      }
    },
    {
      selector: '.eh-preview, .eh-ghost-edge',
      style: {
        'line-color': '#686868',
        'target-arrow-color': '#686868',
        'source-arrow-color': '#686868',
      }
    },
    {
      selector: '.eh-ghost-edge',
      style: {
        width: 4,
        opacity: 0.6,
      }
    },
    {
      selector: '.eh-ghost-edge.eh-preview-active',
      style: {
        opacity: 0,
      }
    },
    // Edges support self loop
    {
      selector: '.edge-handles',
      style: {
        'curve-style': 'bezier',
        'control-point-step-size': 80,
      }
    },
    // change the aux node size
    {
      selector: `node[edgeId !^= "${dataSourceEdgeType}"].aux-node-handles`,
      style: {
        width: 10,
        height: 10,
      }
    },
  ];

  return [
    {
      selector: 'node',
      style: {
        width: '80px',
        height: '80px',
        label: 'data(nodeID)',
        'text-margin-y': 4,
        'text-valign': 'bottom',
        'font-size': '14px',
        'font-weight': 500,
        color: theme.colors.bodyText,
        'min-zoomed-font-size': 11,
      },
    },
    ...vertexStyles,
    {
      selector: 'node:parent',
      style: {
        // node style
        'compound-sizing-wrt-labels': 'exclude',
        'border-width': '0',
        'background-opacity': '0',
        events: 'no',
        // label style
        label: 'data(label)',
        color: 'white',
        'text-margin-y': 0,
        'text-valign': 'top',
        'font-size': '14px',
        'font-weight': 500,
        'min-zoomed-font-size': 11,
        'z-compound-depth': 'top',
        // label background style
        'text-background-color': '#009688',
        'text-background-shape': 'round-rectangle',
        'text-background-opacity': 1,
        // label border style
        'text-border-width': '5px',
        'text-border-color': '#009688',
        'text-border-opacity': 1,
      },
    },
    {
      selector: 'node:active',
      style: {
        'overlay-shape': 'ellipse',
      },
    },
    {
      selector: 'node:selected',
      style: {
        'underlay-color': '#ccc',
        'underlay-padding': 10,
        'underlay-opacity': 1,
        'underlay-shape': 'ellipse',
      },
    },
    {
      selector: 'node.inactive',
      style: {
        opacity: 0.15,
      },
    },
    {
      selector: 'node.filterInactive',
      style: {
        opacity: 0.15,
      },
    },
    {
      selector: 'node.aux-node',
      style: {
        label: '',
        width: 1,
        height: 1,
        events: 'no',
      },
    },
    ...dataSourceVertexStyles,
    {
      selector: 'edge',
      style: {
        width: 2,
        'curve-style': 'haystack',
        'source-distance-from-node': 0,
        'target-distance-from-node': 0,
        'arrow-scale': 1,
        'font-size': '14px',
        'font-weight': 500,
      },
    },
    {
      selector: 'edge[directed]',
      style: {
        'curve-style': 'straight',
        'target-arrow-shape': 'triangle',
      },
    },
    {
      selector: 'edge[__curve]',
      style: {
        'curve-style': 'bezier',
        'control-point-step-size': 80,
      },
    },
    ...edgeStyles,
    {
      selector: 'edge[type]',
      css: {
        // refer: https://github.com/cytoscape/cytoscape.js/issues/2329
        label: (edge: EdgeSingular) => (edge.data('type') ? `\u2060${edge.data('type')}\n\n\u2060` : ''),
        color: theme.colors.bodyText,
        'text-rotation': 'autorotate',
        'text-wrap': 'wrap',
        'text-background-opacity': 0,
        'min-zoomed-font-size': 11,
      },
    },
    {
      // Select the edge type starts with gs_data_source_map for data mapping edge
      selector: `edge[type ^= "${dataSourceEdgeType}"]`,
      style: {
        'line-style': 'dashed',
        label: '',
        'line-color': '#009688',
        'target-arrow-color': '#009688',
        'target-arrow-shape': 'triangle',
      },
    },
    {
      selector: 'edge:selected',
      style: {
        'underlay-color': '#ccc',
        'underlay-padding': '10',
        'underlay-opacity': 1,
      },
    },
    {
      // Selected style of the data-mapping edge
      selector: `edge[type ^= "${dataSourceEdgeType}"]:selected`,
      style: {
        'line-color': '#EF6C02',
        'target-arrow-color': '#EF6C02',
      },
    },
    {
      selector: 'edge.inactive',
      style: {
        opacity: 0.15,
      },
    },
    {
      selector: 'edge.filterInactive',
      style: {
        opacity: 0.15,
      },
    },
    ...edgeHandlesStyles,
  ];
}
