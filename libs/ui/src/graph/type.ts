import { GS<PERSON><PERSON><PERSON><PERSON><PERSON>son, GSQLEdgeJson, VertexStyle, EdgeStyle } from '@tigergraph/models/topology';
import { ExternalGraph, ExternalNode, ExternalLink } from '@tigergraph/models/gvis/insights';
import React from 'react';

import { ElementDefinition, CollectionReturnValue } from 'cytoscape';
import { ContextMenus, MenuItem } from 'cytoscape-context-menus';
import { NodeHtmlLabel } from 'cytoscape-node-html-label';
import { Action } from '../action/type';
import { Rules } from '../ruleStyle/type';
import { KIND } from 'baseui/toast';
import { LayoutType } from './layouts';
import { GRAPH_ICON } from './popper/useGraphIconPopper';

export interface VertexType extends GSQLVertexJson {
  style: VertexStyle;
}

export interface EdgeType extends GSQLEdgeJson {
  style: EdgeStyle;
}

export interface Schema {
  GraphName: string;
  VertexTypes: VertexType[];
  EdgeTypes: EdgeType[];
  DiscriminatorMap?: {
    [key: string]: string[];
  };
}

export interface NodePositions {
  [key: string]: {
    x: number;
    y: number;
  };
}

export type FilterItem = {
  type: string;
  num: number;
  color: string;
};

export type TableData = {
  columns: string[];
  data: React.ReactNode[][];
};

export type ContextMenuInstance = {
  removeMenuItem: (itemID: string) => void;
  insertBeforeMenuItem: (item: MenuItem, existingItemID: string) => void;
  appendMenuItem: (item: MenuItem, parentID?: string) => void;
  isActive: () => boolean;
};

export type UndoRedoInstance = {
  isUndoStackEmpty: () => boolean;
  isRedoStackEmpty: () => boolean;
  reset: () => void;
  undo: () => void;
  redo: () => void;
  do: (actionName: string, args: object) => object;
  action: (
    actionName: string,
    actionFunction: (args: object) => object,
    undoFunction: (args: object) => object
  ) => void;
};

export type NavigatorInstance = {
  destroy: () => void;
};

export type EdgeConnectionInstance = {
  addEdges: (edges: ElementDefinition[]) => void;
  addEdge: (edge: ElementDefinition) => void;
  auxNode: (edge: CollectionReturnValue) => CollectionReturnValue;
  isAuxNode: (node: CollectionReturnValue) => boolean;
  edgeId: (edge: CollectionReturnValue) => string;
  edge: (node: CollectionReturnValue) => CollectionReturnValue;
};

export type CytoscapeExtensions = {
  contextMenus?: (params: ContextMenus | string) => ContextMenuInstance;
  nodeHtmlLabel?: (params: NodeHtmlLabel[]) => void;
  undoRedo: (options: object) => UndoRedoInstance;
  navigator: (options: object) => NavigatorInstance;
  edgeConnections: () => EdgeConnectionInstance;
};

export type GraphSelections = {
  selectElements: (graph: ExternalGraph) => void;
  selectNodes: (nodes: ExternalNode[]) => void;
  selectNodesByTypes: (nodeTypes: string[]) => void;
  selectEdges: (edges: ExternalLink[]) => void;
  selectEdgesByTypes: (edgeTypes: string[]) => void;
  unselectElements: (graph?: ExternalGraph) => void;
  unselectNodes: (nodes?: ExternalNode[]) => void;
  unselectEdges: (edges?: ExternalLink[]) => void;
  selectedElements: () => ExternalGraph;
  selectedNodes: () => ExternalNode[];
  selectedEdges: () => ExternalLink[];
  getNodes: () => ExternalNode[];
  getNodesByTypes: (types: string[]) => ExternalNode[];
  getLinks: () => ExternalLink[];
  getLinksByTypes: (types: string[]) => ExternalLink[];
};

export type GraphCenter = {
  centerGraph: (graph?: ExternalGraph) => void;
  centerNodes: (nodes: ExternalNode[]) => void;
  centerLinks: (edges: ExternalLink[]) => void;
  centerNodesByTypes: (types: string[]) => void;
  centerLinksByTypes: (types: string[]) => void;
};

export type GraphPositions = {
  graphPositions: (nodes?: ExternalNode[]) => [string, number, number][]; // be consistent with gst
  lockNodes: (nodes?: ExternalNode[]) => void;
  unlockNodes: (nodes?: ExternalNode[]) => void;
  setPositions: (positions: [string, number, number][]) => void;
};

export type GraphEvents = {
  // undefined stands for clicking the canvas
  onClick?: (element: ExternalNode | ExternalLink | undefined, position?: { x: number, y: number }) => void;
  onClickDown?: (element: ExternalNode | ExternalLink | undefined) => void;
  onDoubleClick?: (element: ExternalNode | ExternalLink | undefined, position?: { x: number, y: number }) => void;
  onMouseOver?: (element: ExternalNode | ExternalLink) => void;
  onMouseOut?: (element: ExternalNode | ExternalLink) => void;
  onSelect?: (graph: ExternalGraph) => void;
  onSelectNodes?: (nodes: ExternalNode[]) => void;
  onSelectEdges?: (edges: ExternalLink[]) => void;
  onPositionChange?: () => void;
  onDelete?: (element: ExternalNode | ExternalLink) => void;
} & EdgeHandleEvents;

export type GraphHighlightedLabel = {
  highlightLabel: (node: ExternalNode, label: string) => void;
  removeHighlightedLabel: (node: ExternalNode) => void;
};

export interface NodeMessageProps {
  node: ExternalNode;
  message: string;
  isLoading?: boolean;
  kind?: (typeof KIND)[keyof typeof KIND];
}

export interface LinkMessageProps {
  link: ExternalLink;
  message: string;
  isLoading?: boolean;
  kind?: (typeof KIND)[keyof typeof KIND];
}

export type GraphMessages = {
  displayNodeMessage: (data: NodeMessageProps) => void;
  displayLinkMessage: (data: LinkMessageProps) => void;
  displayNodeProgress: (node: ExternalNode, value: number, message?: string) => void;
  displayNodeInfiniteProgress: (node: ExternalNode, message?: string) => void;
  removeNodeMessage: (node: ExternalNode) => void;
  removeLinkMessage: (link: ExternalLink) => void;
  removeAllMessages: () => void;
  removeNodeProgress: (node: ExternalNode) => void;
  removeAllProgress: () => void;
};

export type GraphIcons = {
  displayNodeIcon: (node: ExternalNode, iconTypes: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON]) => void;
  displayLinkIcon: (link: ExternalLink, iconTypes: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON]) => void;
  displayNodesIcon: (nodes: ExternalNode[], iconTypes: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON]) => void;
  displayLinksIcon: (links: ExternalLink[], iconTypes: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON]) => void;
  removeNodeIcon: (node: ExternalNode) => void;
  removeLinkIcon: (link: ExternalLink) => void;
  removeAllIcons: () => void;
};

export type GraphStyles = {
  setNodeStyle: (node: ExternalNode, style: Record<string, string | number | Function>) => void;
  setNodesStyle: (data: { node: ExternalNode, style: Record<string, string | number | Function> }[]) => void;
  setNodesStyleByType: (type: string, style: Record<string, string | number | Function>) => void;
  setNodesStyleByTypes: (data: { type: string, style: Record<string, string | number | Function> }[]) => void;
  setLinkStyle: (link: ExternalLink, style: Record<string, string | number | Function>) => void;
  setLinksStyle: (data: { link: ExternalLink, style: Record<string, string | number | Function> }[]) => void;
  setLinksStyleByType: (type: string, style: Record<string, string | number | Function>) => void;
  setLinksStyleByTypes: (data: { type: string, style: Record<string, string | number | Function> }[]) => void;
};

export type GraphExport = {
  exportPng: (width?: number, height?: number) => string;
};

export type EdgeHandleEvents = {
  onCreateLink?: (source: ExternalNode, target: ExternalNode | ExternalLink) => void;
  onCreateLinkCancelled?: (source: ExternalNode, position: { x: number, y: number }) => void;
  showEdgeHandler?: (node: ExternalNode) => boolean;
};

export type EdgeHandles = {
  setDrawMode: (enable: boolean) => void;
}

export interface GraphRef extends
  GraphSelections,
  GraphCenter,
  GraphPositions,
  GraphHighlightedLabel,
  GraphMessages,
  GraphIcons,
  GraphStyles,
  GraphExport,
  EdgeHandles {
  runLayout: (extraParams?: object, onLayoutStop?: () => void) => void;
}

export type SettingType = {
  // keep consistent with insights's graph widget layout setting value
  layout?: LayoutType;

  // Force Cola
  edgeLength?: number;

  // Radial && Layer
  spacingFactor?: number;

  // Tree
  nodeSep?: number;
  edgeSep?: number;
  rankSep?: number;
  rankDir?: string;
  align?: string;

  showThumbNail?: boolean;
  showUndoRedo?: boolean;

  // action menu
  actionsByType?: Record<string, Action[]>;
  // conditional styling
  rulesByType?: Record<string, Rules>;
  attrMapGraphLabel?: Record<string, string>;
};
