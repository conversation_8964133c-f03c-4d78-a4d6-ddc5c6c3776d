import axios, { AxiosError } from 'axios';
import { useMutation } from 'react-query';

import { ExternalGraph, parseGraph } from '@tigergraph/models/gvis/insights';

export function useMutationExpansionNode() {
  const { mutate, isLoading } = useMutation<
    ExternalGraph,
    AxiosError,
    {
      graphName: string;
      eTypes: string[];
      vTypes: string[];
      id: any;
      type: string;
      relatedType?: string;
      discriminatorMap?: { [key: string]: string[] };
    }
  >(async ({ graphName, eTypes, vTypes, id, type, relatedType, discriminatorMap }) => {
    const response = await axios.post(`/api/restpp/kstep_expansion/${graphName}`, {
      expansion_steps: [
        {
          eTypes,
          // graph studio set limit to 200
          local_limit: 100,
          vTypes: relatedType ? [relatedType] : vTypes,
        },
      ],
      source_vertices: [
        {
          id,
          type,
        },
      ],
    });

    if (!response.data['error']) {
      const graph = parseGraph(response.data, discriminatorMap);
      return graph;
    } else {
      const error = response.data['message'];
      throw new Error(error);
    }
  });
  return { mutate, isLoading };
}

export function useMutationShortestPath() {
  const { mutate, isLoading } = useMutation<
    ExternalGraph,
    AxiosError,
    {
      graphName: string;
      eTypes: string[];
      vTypes: string[];
      sourceID: any;
      sourceType: string;
      targetID: any;
      targetType: string;
      allShortestPaths: boolean;
      allPath: boolean;
      discriminatorMap?: { [key: string]: string[] };
    }
  >(async ({ graphName, eTypes, vTypes, sourceID, sourceType, targetID, targetType, allShortestPaths, allPath, discriminatorMap }) => {
    const response = await axios.post(`/api/restpp/${allPath ? 'allpaths' : 'shortestpath'}/${graphName}`, {
      allShortestPaths: allShortestPaths || allPath,
      edgeFilters: eTypes.map((eType) => ({
        type: eType,
        condition: '',
      })),
      vertexFilters: vTypes.map((vType) => ({
        type: vType,
        condition: '',
      })),
      maxLength: 6,
      sources: [
        {
          id: sourceID,
          type: sourceType,
        },
      ],
      targets: [
        {
          id: targetID,
          type: targetType,
        },
      ],
    });

    if (!response.data['error']) {
      const graph = parseGraph(response.data, discriminatorMap);
      return graph;
    } else {
      const error = response.data['message'];
      throw new Error(error);
    }
  });
  return { mutate, isLoading };
}
