import React from 'react';
import { BiUndo, BiRedo } from 'react-icons/bi';
import { Button } from '@tigergraph/app-ui-lib/button';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { KIND, SHAPE } from 'baseui/button';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE, PLACEMENT } from 'baseui/popover';
import { expand } from 'inline-style-expand-shorthand';

type Props = {
  isUndoStackEmpty: boolean;
  isRedoStackEmpty: boolean;
  undo: () => void;
  redo: () => void;
};

export default function UndoRedo({ isUndoStackEmpty, isRedoStackEmpty, undo, redo }: Props) {
  const [css, theme] = useStyletron();
  return (
    <div
      className={css({
        position: 'absolute',
        right: '8px',
        top: '8px',
        display: 'flex',
      })}
      data-testid="undo-redo"
    >
      <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.bottom} content="Undo">
        <Button
          kind={KIND.tertiary}
          shape={SHAPE.square}
          overrides={{
            BaseButton: {
              style: {
                ...expand({
                  border: '1px solid #C2BFBB',
                }),
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
              },
            },
          }}
          onClick={() => {
            if (!isUndoStackEmpty) {
              undo();
            }
          }}
        >
          <BiUndo size={24} color={!isUndoStackEmpty ? undefined : theme.colors.gray400} />
        </Button>
      </StatefulPopover>

      <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.bottom} content="Redo">
        <Button
          kind={KIND.tertiary}
          shape={SHAPE.square}
          overrides={{
            BaseButton: {
              style: {
                ...expand({
                  border: '1px solid #C2BFBB',
                }),
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
                borderLeftWidth: 0,
              },
            },
          }}
          onClick={() => {
            if (!isRedoStackEmpty) {
              redo();
            }
          }}
        >
          <BiRedo size={24} color={!isRedoStackEmpty ? undefined : theme.colors.gray400} />
        </Button>
      </StatefulPopover>
    </div>
  );
}
