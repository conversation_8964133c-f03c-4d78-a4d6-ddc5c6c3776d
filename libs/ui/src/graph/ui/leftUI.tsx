import React, { useState, useMemo } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE } from 'baseui/popover';
import { KIND, SHAPE } from 'baseui/button';
import { expand } from 'inline-style-expand-shorthand';

import Tables from './tables';
import { Schema, UndoRedoInstance } from '../type';
import GraphAnalysis from '../graphAnalysis';
import { ExternalGraph } from '@tigergraph/models/gvis/insights';
import { Core } from 'cytoscape';
import { getFilterAndTableData } from '../data';
import { TableIcon, AlgorithmIcon } from '../icons/icon';

type Props = {
  cy: Core;
  graph: ExternalGraph;
  schema: Schema;
  settings: Record<string, any>; // Chart setting
  onSettingUpdate: (key: string, value: any) => void; // Chart can update setting.

  onElementSelect: (id: string) => void;

  showAnalysis: boolean;
  setShowAnalysis(value: boolean): void;
  undoRedoInstance: UndoRedoInstance;
};

export default function LeftUI({
  cy,
  graph,
  schema,
  settings,
  onSettingUpdate,
  onElementSelect,
  showAnalysis,
  setShowAnalysis,
  undoRedoInstance,
}: Props) {
  const [css] = useStyletron();
  const [showTable, setShowTable] = useState(false);

  const [selectNodeType, setSelectNodeType] = useState<string>();
  const [selectEdgeType, setSelectEdgeType] = useState<string>();

  // generate filter items
  const { nodeItems, edgeItems, nodeTablesMap, edgeTablesMap } = useMemo(() => {
    setSelectNodeType(undefined);
    setSelectEdgeType(undefined);
    return getFilterAndTableData(graph, schema);
  }, [schema, graph]);

  return (
    <div
      className={css({
        position: 'absolute',
        left: '8px',
        top: '8px',
        display: 'flex',
        flexDirection: 'column',
      })}
      data-testid="left-ui"
    >
      <StatefulPopover triggerType={TRIGGER_TYPE.hover} content="Tabular data for vertices and edges">
        <Button
          kind={KIND.tertiary}
          shape={SHAPE.square}
          overrides={{
            BaseButton: {
              style: {
                ...expand({
                  border: '1px solid #C2BFBB',
                  borderBottomLeftRadius: 0,
                  borderBottomRightRadius: 0,
                }),
                borderBottomWidth: 0,
              },
            },
          }}
          onClick={() => setShowTable(true)}
        >
          <TableIcon />
        </Button>
      </StatefulPopover>

      <StatefulPopover triggerType={TRIGGER_TYPE.hover} content="Run graph analytics algorithm">
        <Button
          kind={KIND.tertiary}
          shape={SHAPE.square}
          overrides={{
            BaseButton: {
              style: {
                ...expand({
                  border: '1px solid #C2BFBB',
                  borderTopLeftRadius: 0,
                  borderTopRightRadius: 0,
                }),
              },
            },
          }}
          onClick={() => setShowAnalysis(true)}
        >
          <AlgorithmIcon />
        </Button>
      </StatefulPopover>

      <Tables
        isOpen={showTable}
        onClose={() => {
          setTimeout(() => {
            setShowTable(false);
          }, 200);
        }}
        nodeItems={nodeItems}
        edgeItems={edgeItems}
        nodeTablesMap={nodeTablesMap}
        edgeTablesMap={edgeTablesMap}
        selectNodeType={selectNodeType}
        onSelectNodeTypeChanged={setSelectNodeType}
        selectEdgeType={selectEdgeType}
        onSelectEdgeTypeChanged={setSelectEdgeType}
        onElementSelect={onElementSelect}
      >
        <span
          className={css({
            position: 'absolute',
            top: 0,
            left: '32px',
          })}
        />
      </Tables>

      <GraphAnalysis
        cy={cy}
        isOpen={showAnalysis}
        onClose={() => {
          setTimeout(() => {
            setShowAnalysis(false);
          }, 200);
        }}
        nodeItems={nodeItems}
        edgeItems={edgeItems}
        graph={graph}
        schema={schema}
        settings={settings}
        onSettingUpdate={onSettingUpdate}
        undoRedoInstance={undoRedoInstance}
      >
        <span
          className={css({
            position: 'absolute',
            top: 0,
            left: '32px',
          })}
        />
      </GraphAnalysis>
    </div>
  );
}
