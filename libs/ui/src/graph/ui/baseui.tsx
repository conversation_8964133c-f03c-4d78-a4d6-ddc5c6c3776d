import { TabOverrides } from 'baseui/tabs-motion';
import { PopoverOverrides } from 'baseui/popover';
import { CustomTheme } from '@tigergraph/app-ui-lib/Theme';

export const tabOverrides: TabOverrides = {
  Tab: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
      paddingTop: 0,
      paddingBottom: 0,
      fontSize: '16px',
    },
  },
  TabPanel: { style: { paddingLeft: 0, paddingRight: 0 } },
};

export const popoverOverrides: PopoverOverrides = {
  Body: {
    style: {
      backgroundColor: '#fff',
    },
  },
  Inner: {
    style: ({ $theme }) => {
      const customTheme = $theme as CustomTheme;
      return {
        backgroundColor: '#fff',
        color: customTheme.colors.bodyText,
        ...customTheme.typography.Body2,
      };
    },
  },
};
