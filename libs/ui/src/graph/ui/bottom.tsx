import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@tigergraph/app-ui-lib/button';
import { KIND, SHAPE } from 'baseui/button';
import { StatefulPopover } from '@tigergraph/app-ui-lib/popover';
import { TRIGGER_TYPE, PLACEMENT } from 'baseui/popover';
import { faPlus, faMinus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { expand } from 'inline-style-expand-shorthand';
import cytoscape from 'cytoscape';
import navigator from '../cytoscape-navigator/cytoscape-navigator';
import clsx from 'clsx';

import { ActionPopover } from '../../actionsPopover';
import { LayoutType, layouts } from '../layouts';
import { useGraphContext } from '../context';
import { NavigatorInstance } from '../type';
import Search from './search';

import { injectCSS } from '../util';
import { ResetIcon, ThumbNailIcon, ForceIcon } from '../icons/icon';

// register navigator plugin
navigator(cytoscape);

type Props = {
  layout: LayoutType;
  onLayoutChange: (layout: LayoutType) => void;

  hideThumbNailButton?: boolean;
  hideLayoutsButton?: boolean;
  showThumbNail: boolean;
  onToggleThumbNail: () => void;
};

export default function BottomUI({
  layout,
  onLayoutChange,
  hideThumbNailButton,
  hideLayoutsButton,
  showThumbNail,
  onToggleThumbNail,
}: Props) {
  const [css] = useStyletron();
  const [showPopover, setShowPopover] = useState(false);
  const { graphT, id, graphData, isSchemaGraph } = useGraphContext();

  const navigatorRef = useRef<NavigatorInstance | null>(null);

  useEffect(() => {
    if (showThumbNail) {
      navigatorRef.current = graphT.navigator({
        container: `#${id}_thumbnail`,
        removeCustomContainer: false,
        // performance optimization for large graphs
        viewLiveFramerate: 24, // default is 60
        rerenderDelay: 1000, // default value is 500ms
      });
      // force render to make thumbnail work
      graphT.forceRender();
    } else {
      if (navigatorRef.current) {
        // hack to fix navigator render bug
        // @ts-ignore
        navigatorRef.current['_onRenderHandler']?.cancel();
        navigatorRef.current.destroy();
        navigatorRef.current = null;
      }
    }
  }, [showThumbNail, id, graphT]);

  // clean up thumbnail
  useEffect(() => {
    return () => {
      if (navigatorRef.current) {
        // @ts-ignore
        navigatorRef.current['_onRenderHandler']?.cancel();
        navigatorRef.current.destroy();
        navigatorRef.current = null;
      }
    };
  }, []);

  let LayoutIcon = ForceIcon;
  for (let l of layouts) {
    if (l.label === layout) {
      LayoutIcon = l.iconComponent;
      break;
    }
  }

  return (
    <>
      <div
        className={css({
          position: 'absolute',
          right: '8px',
          bottom: '8px',
          display: 'flex',
          flexDirection: 'column',
          '@media screen and (max-width: 600px)': {
            display: 'none',
          },
        })}
        data-testid="bottom-ui"
      >
        <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.top} content="Zoom in">
          <Button
            kind={KIND.tertiary}
            shape={SHAPE.square}
            onClick={() => {
              graphT.zoomIn();
            }}
            overrides={{
              BaseButton: {
                style: {
                  ...expand({
                    border: '1px solid #C2BFBB',
                    borderBottomLeftRadius: 0,
                    borderBottomRightRadius: 0,
                  }),
                  borderBottomWidth: 0,
                },
              },
            }}
          >
            <FontAwesomeIcon icon={faPlus as IconProp} size="sm" />
          </Button>
        </StatefulPopover>
        <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.top} content="Zoom out">
          <Button
            kind={KIND.tertiary}
            shape={SHAPE.square}
            onClick={() => {
              graphT.zoomOut();
            }}
            overrides={{
              BaseButton: {
                style: {
                  ...expand({
                    border: '1px solid #C2BFBB',
                    borderTopLeftRadius: 0,
                    borderTopRightRadius: 0,
                  }),
                  borderBottomLeftRadius: 0,
                  borderBottomRightRadius: 0,
                  borderBottomWidth: 0,
                },
              },
            }}
          >
            <FontAwesomeIcon icon={faMinus as IconProp} size="sm" />
          </Button>
        </StatefulPopover>
        <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.top} content="Fit graph into viewport">
          <Button
            kind={KIND.tertiary}
            shape={SHAPE.square}
            onClick={() => {
              graphT.fit();
            }}
            overrides={{
              BaseButton: {
                style: {
                  ...expand({
                    border: '1px solid #C2BFBB',
                    borderTopLeftRadius: 0,
                    borderTopRightRadius: 0,
                  }),
                  borderBottomLeftRadius: hideThumbNailButton && hideLayoutsButton ? '5px' : 0,
                  borderBottomRightRadius: hideThumbNailButton && hideLayoutsButton ? '5px' : 0,
                  borderBottomWidth: hideThumbNailButton && hideLayoutsButton ? '1px' : 0,
                },
              },
            }}
          >
            <ResetIcon />
          </Button>
        </StatefulPopover>
        {!hideThumbNailButton ? (
          <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.top} content="Show graph thumbnail">
            <Button
              kind={KIND.tertiary}
              shape={SHAPE.square}
              onClick={onToggleThumbNail}
              isSelected={showThumbNail}
              overrides={{
                BaseButton: {
                  style: {
                    ...expand({
                      border: '1px solid #C2BFBB',
                      borderTopLeftRadius: 0,
                      borderTopRightRadius: 0,
                    }),
                    borderBottomLeftRadius: hideLayoutsButton ? '5px' : 0,
                    borderBottomRightRadius: hideLayoutsButton ? '5px' : 0,
                    borderBottomWidth: hideLayoutsButton ? '1px' : 0,
                  },
                },
              }}
            >
              <ThumbNailIcon />
            </Button>
          </StatefulPopover>
        ) : null}
        {!hideLayoutsButton && (
          <StatefulPopover triggerType={TRIGGER_TYPE.hover} placement={PLACEMENT.top} content="Change graph layout">
            <Button
              kind={KIND.tertiary}
              shape={SHAPE.square}
              onClick={() => setShowPopover(true)}
              overrides={{
                BaseButton: {
                  style: {
                    ...expand({
                      borderTopLeftRadius: 0,
                      borderTopRightRadius: 0,
                      border: '1px solid #C2BFBB',
                    }),
                  },
                },
              }}
            >
              <LayoutIcon />
            </Button>
          </StatefulPopover>
        )}
        <ActionPopover
          isOpen={showPopover}
          onClose={() => {
            setTimeout(() => {
              setShowPopover(false);
            }, 200);
          }}
          placement={PLACEMENT.topRight}
          items={layouts.map((layout) => ({
            ...layout,
            id: layout.label,
          }))}
          highLightId={layout}
          onItemClicked={(id) => {
            onLayoutChange(id as LayoutType);
          }}
          renderItem={({ id, label }) => {
            let LayoutIcon = ForceIcon;
            for (let l of layouts) {
              if (l.label === id) {
                LayoutIcon = l.iconComponent;
                break;
              }
            }

            return (
              <>
                <span
                  className={css({
                    color: '#333',
                    width: '72px',
                    textAlign: 'left',
                  })}
                >
                  {label}
                </span>
                <LayoutIcon />
              </>
            );
          }}
        >
          <span
            className={css({
              position: 'absolute',
              bottom: 0,
              right: '36px',
            })}
          />
        </ActionPopover>
        {isSchemaGraph && (
          <div
            className={css({
              position: 'absolute',
              right: '40px',
              bottom: '0',
              width: '150px',
            })}
          >
            <Search />
          </div>
        )}
      </div>
      <div
        className={css({
          position: 'absolute',
          width: '264px',
          maxWidth: '35%',
          height: '158px',
          padding: '4px',
          boxSizing: 'border-box',
          bottom: '12px',
          right: showThumbNail ? '48px' : '10000px',
          borderRadius: '4px',
          boxShadow: '2px 2px 4px rgba(38, 88, 138, 0.1), -2px -2px 4px rgba(38, 88, 138, 0.1)',
          backgroundColor: '#fff',
        })}
      >
        <div
          id={`${id}_thumbnail`}
          className={clsx({
            'cytoscape-navigator': true,
            hide_image: graphData.nodes.length === 0,
          })}
          style={{
            // override style in .cytoscape-navigator
            zIndex: 'initial',
            position: 'absolute',
            left: '4px',
            top: '4px',
            width: 'calc(100% - 8px)',
            height: 'calc(100% - 8px)',
            border: 'none',
          }}
        />
      </div>
    </>
  );
}

injectCSS(`
  div.cytoscape-navigatorView {
    z-index: initial;
  }
  div.cytoscape-navigator.hide_image > img {
    display: none;
  }
  div.cytoscape-navigatorOverlay {
    z-index: initial;
  }
`);
