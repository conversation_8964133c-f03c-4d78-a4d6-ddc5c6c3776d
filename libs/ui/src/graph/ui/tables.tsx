import React from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { PLACEMENT } from 'baseui/popover';
import { Popover } from '@tigergraph/app-ui-lib/popover';
import { Button } from '@tigergraph/app-ui-lib/button';
import { KIND, SIZE } from 'baseui/button';
import { popoverOverrides, tabOverrides } from './baseui';
import { Tab, Tabs } from '@tigergraph/app-ui-lib/tab';
import { Table } from '@tigergraph/app-ui-lib/table';
import { SIZE as TableSIZE } from 'baseui/table-semantic';
import { Pagination } from '@tigergraph/app-ui-lib/pagination';

import { FilterItem, TableData } from '../type';
import { useState } from 'react';
import { expand } from 'inline-style-expand-shorthand';

const pageSize = 100;

type TablesProps = {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;

  nodeItems: FilterItem[];
  edgeItems: FilterItem[];

  nodeTablesMap: Map<string, TableData>;
  edgeTablesMap: Map<string, TableData>;

  selectNodeType?: string;
  onSelectNodeTypeChanged: (selectNodeType: string) => void;

  selectEdgeType?: string;
  onSelectEdgeTypeChanged: (selectEdgeType: string) => void;

  onElementSelect: (id: string) => void;
};

export default function Tables({
  isOpen,
  onClose,
  children,
  nodeItems,
  edgeItems,
  nodeTablesMap,
  edgeTablesMap,
  selectNodeType,
  onSelectNodeTypeChanged,
  selectEdgeType,
  onSelectEdgeTypeChanged,
  onElementSelect,
}: TablesProps) {
  const [css, theme] = useStyletron();
  const [activeKey, setActiveKey] = useState('node');
  const [pageNumber, setPageNumber] = useState(1);

  selectNodeType = selectNodeType || nodeItems[0]?.type;
  selectEdgeType = selectEdgeType || edgeItems[0]?.type;

  const render = (
    items: FilterItem[],
    selectType: string,
    onSelectTypeChanged: (selectType: string) => void,
    tableMap: Map<string, TableData>,
    isNodeType: boolean,
    close: () => void
  ) => {
    const tableData = tableMap.get(selectType);
    if (!tableData) {
      return null;
    }
    const { columns, data } = tableData;

    return (
      <>
        <div
          className={css({
            display: 'flex',
            flexWrap: 'wrap',
            marginBottom: '8px',
            maxHeight: '120px',
            overflowY: 'auto',
          })}
        >
          {items.map((item) => {
            const { type, num } = item;
            return (
              <Button
                size={SIZE.compact}
                key={type}
                onClick={() => {
                  onSelectTypeChanged(type);
                  setPageNumber(1);
                }}
                kind={type === selectType ? KIND.primary : KIND.tertiary}
                overrides={{
                  BaseButton: {
                    style: {
                      marginRight: '16px',
                      marginBottom: '8px',
                      ...expand({
                        borderWidth: 0,
                      }),
                      ...(type !== selectType
                        ? {
                            backgroundColor: theme.colors.gray50,
                          }
                        : {}),
                    },
                  },
                }}
              >
                {type} {num}
              </Button>
            );
          })}
        </div>
        <Table
          size={TableSIZE.compact}
          // the last column is for cytoscape id, will not show in table.
          columns={columns.slice(0, columns.length - 1)}
          data={data.slice((pageNumber - 1) * pageSize, pageNumber * pageSize)}
          overrides={{
            Root: { style: { maxHeight: '360px' } },
            TableBodyRow: {
              style: {
                cursor: 'pointer',
              },
              // @ts-ignore
              props: (props) => ({
                ...props,
                onClick: () => {
                  const row = props['$row'];
                  let id = row[row.length - 1];
                  close();
                  onElementSelect(id);
                },
              }),
            },
          }}
        />
        {data.length > 0 && (
          <div
            className={css({
              padding: '8px 8px 0',
              display: 'flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
            })}
          >
            <div className={css({ marginRight: '10px' })}>
              {`${(pageNumber - 1) * pageSize + 1} - ${Math.min(pageNumber * pageSize, data.length)} of ${data.length}`}
            </div>
            <Pagination
              totalPage={Math.ceil(data.length / pageSize)}
              pageNumber={pageNumber}
              setPageNumber={(number) => {
                setPageNumber(number);
              }}
              type="default"
            />
          </div>
        )}
      </>
    );
  };

  return (
    <Popover
      isOpen={isOpen}
      onClickOutside={onClose}
      onEsc={onClose}
      showArrow={false}
      autoFocus={false}
      content={() => {
        return (
          <div
            className={css({
              display: 'flex',
              flexDirection: 'column',
              width: '600px',
            })}
          >
            <Tabs
              activeKey={activeKey}
              onChange={({ activeKey }) => {
                setActiveKey(activeKey as string);
                setPageNumber(1);
              }}
              activateOnFocus
            >
              <Tab title="Vertices" overrides={tabOverrides} key="node">
                {render(nodeItems, selectNodeType!, onSelectNodeTypeChanged, nodeTablesMap, true, onClose)}
              </Tab>
              <Tab title="Edges" overrides={tabOverrides} key="edge">
                {render(edgeItems, selectEdgeType!, onSelectEdgeTypeChanged, edgeTablesMap, false, onClose)}
              </Tab>
            </Tabs>
          </div>
        );
      }}
      overrides={popoverOverrides}
      placement={PLACEMENT.rightTop}
    >
      {children}
    </Popover>
  );
}
