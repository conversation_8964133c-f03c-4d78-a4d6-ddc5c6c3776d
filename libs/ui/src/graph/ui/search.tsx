import React, { useMemo } from 'react';
import { useState } from 'react';
import { Value, OnChangeParams } from 'baseui/select';
import StyledSelect from './styledSelect';
import { useGraphContext } from '../context';
import { dataSourceVertexTypes, dataSourceEdgeType } from '../stylesheet';

export default function Search() {
  const [value, setValue] = useState<Value>([]);
  const { graphT, graphData } = useGraphContext();

  const options = useMemo(() => {
    function uniqTypes(arr: { type: string }[]) {
      var seen = new Set();
      return arr
        .filter((item) => {
          if (seen.has(item.type) || item.type in dataSourceVertexTypes || item.type.startsWith(dataSourceEdgeType)) {
            return false;
          }
          seen.add(item.type);
          return true;
        })
        .sort((a, b) => a.type.localeCompare(b.type));
    }

    return {
      __ungrouped: [],
      'Vertex type': uniqTypes(graphData.nodes),
      'Edge type': uniqTypes(graphData.links),
    };
  }, [graphData]);

  const onChange = ({ value }: OnChangeParams) => {
    setValue(value);
    if (value.length && graphT.core.type === 'canvas') {
      const { cy } = graphT.core;
      const elements = cy.elements(`node[type='${value[0].type}'], edge[type='${value[0].type}']`);
      cy.elements().unselect();
      cy.maxZoom(2);
      cy.fit(elements, 100);
      cy.maxZoom(10);
      elements.select();
    }
  };

  return (
    <StyledSelect
      value={value}
      options={options}
      onChange={onChange}
      labelKey="type"
      valueKey="type"
      placeholder="Search type"
      overrides={{
        Dropdown: {
          style: {
            maxHeight: '240px',
            minHeight: '184px',
          },
        },
      }}
    />
  );
}
