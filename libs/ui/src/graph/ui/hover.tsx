import React, { useEffect } from 'react';
import ReactDOM from 'react-dom';

import '../cytoscape-navigator/cytoscape.js-navigator.css';
import { useGraphContext } from '../context';
import ElementDetail from './elementDetail';
import { dataSourceVertexTypes, dataSourceEdgeType } from '../stylesheet';
import { AbstractEventObject } from 'cytoscape';

export default function Hover({ hoverTooltipDelay }: { hoverTooltipDelay: number }) {
  const { schema, isSchemaGraph, globalTypes = [], graphData, graphT } = useGraphContext();
  const { core } = graphT;

  useEffect(() => {
    if (core.type === 'webgl') {
      return;
    }

    const cy = core.cy;
    let popperRefObj: any = null;
    let showDetailTimer: NodeJS.Timeout | null = null;
    let content: HTMLDivElement | null = null;

    const destroyPopper = () => {
      if (showDetailTimer) {
        clearTimeout(showDetailTimer);
        showDetailTimer = null;
      }
      // destroy the popper object
      const elements = document.getElementsByClassName('popper-div');
      for (let i = 0; i < elements.length; i++) {
        ReactDOM.unmountComponentAtNode(elements[i]);
        elements[i].remove();
      }
      content?.remove();
      if (popperRefObj) {
        popperRefObj.state.elements.popper.remove();
        popperRefObj.destroy();
        popperRefObj = null;
      }
    };

    cy.on('viewport tapstart cxttap', destroyPopper);

    const onShowDetail = (event: AbstractEventObject) => {
      if (showDetailTimer) {
        clearTimeout(showDetailTimer);
        showDetailTimer = null;
      }

      if (!shouldHandleEvent(event)) {
        return;
      }

      const target = event.target;

      showDetailTimer = setTimeout(() => {
        // destroy previous popper
        destroyPopper();

        target.popperRefObj = target.popper({
          content: () => {
            content = document.createElement('div');
            content.classList.add('popper-div');
            content.style['zIndex'] = '2';

            const stopPropagation = (event: MouseEvent) => {
              event.stopPropagation();
              return false;
            };

            // stop trigger mouse event below on cy.container when user viewing on the hover tips
            (
              ['mousedown', 'mouseenter', 'mouseleave', 'mousemove', 'mouseover', 'mouseout', 'mouseup'] as const
            ).forEach((event) => {
              content?.addEventListener(event, stopPropagation, true);
            });
            cy.container()?.appendChild(content);

            ReactDOM.render(
              <ElementDetail
                elementData={target.data()}
                isSchemaGraph={isSchemaGraph || false}
                globalTypes={globalTypes}
                onWheel={destroyPopper}
                schema={schema}
              />,
              content
            );

            return content;
          },
          popper: {
            placement: 'left',
            modifiers: [
              {
                name: 'preventOverflow',
              },
            ],
          },
        });

        popperRefObj = target.popperRefObj;
      // Display tooltip after the delay
      }, hoverTooltipDelay);
    };

    const onHideDetail = (event: any) => {
      if (showDetailTimer) {
        clearTimeout(showDetailTimer);
        showDetailTimer = null;
      }
      if (event.target.popperRefObj) {
        destroyPopper();
        event.target.popperRefObj = null;
        // event.target.popperRefObj.state.elements.popper.remove();
        // event.target.popperRefObj.destroy();
        // event.target.popperRefObj = null;
        // popperRefObj = null;
      }
    };

    // 1 register listener for current elements
    cy.on('mouseover', onShowDetail);
    cy.on('mouseout', onHideDetail);

    return () => {
      cy.off('viewport tapstart cxttap', destroyPopper);
      // @ts-ignore
      cy.off('mouseover', onShowDetail);
      // @ts-ignore
      cy.off('mouseout', onHideDetail);

      destroyPopper();
    };
  }, [isSchemaGraph, schema, graphData, core, globalTypes, hoverTooltipDelay]);

  return null;
}

export function shouldHandleEvent(event: AbstractEventObject) {
  const target = event.target;

  // Don't show the hovering tooltip for:
  // 1. Aux node and edge;
  // 2. S3, GCS, and ABS data sources;
  if (
    !target.data('type') ||
    target.data('type').startsWith(dataSourceEdgeType) ||
    (target.data('type') in dataSourceVertexTypes && target.data('type') !== 'gs_file')
  ) {
    return false;
  }

  return true;
}
