import React, { CSSProperties, WheelEvent } from 'react';
import { HelperFunctions } from '@tigergraph/models/utils';
import useClipboard from 'react-use-clipboard';
import { useEffect } from 'react';
import { KIND } from 'baseui/toast';

import { showToast } from '../../styledToasterContainer';
import { Schema } from '../type';
import { getElementAttributes } from '../util';

type Props = {
  elementData: Record<string, any>;
  isSchemaGraph: boolean;
  onWheel?: () => void;
  schema: Schema;
  globalTypes: string[];
  showAsPopover?: boolean;
};

export default function ElementDetail({
  elementData,
  isSchemaGraph,
  globalTypes,
  onWheel,
  schema,
  showAsPopover = true,
}: Props) {
  const isNode = !!elementData['nodeID'];
  const title = isNode ? 'Vertex' : 'Edge';
  const type = elementData['type'];
  const globalTypesSet = new Set(globalTypes);

  const { attributes, showTitle } = getElementAttributes(elementData, isSchemaGraph, schema);

  // filter out internal props
  let keys = Object.keys(attributes).filter((key) => !key.startsWith('__') && elementData[key] !== undefined);

  // we need to sort the keys based on schema attribute
  // 1 Update to cytoscape element' data is not guarantee attribute order,
  //   as we cannot complete delete the key, only make the key undefined.
  // 2 We rely on stable sort
  //   as of version 10 (or ECMAScript 2019), the specification requires Array.prototype.sort to be stable.
  const { VertexTypes: vertexTypes, EdgeTypes: edgeTypes } = schema;
  if (isNode) {
    const vertexType = vertexTypes.find((vertexType) => vertexType.Name === type);
    if (vertexType) {
      // Display the primary id in the hovering context menu
      const idIndex = keys.indexOf('id');
      if (idIndex !== -1) {
        keys[idIndex] = `(PRIMARY ID) ${vertexType.PrimaryId.AttributeName}`;
        attributes[keys[idIndex]] = attributes['id'];
        if (vertexType.PrimaryId.AttributeName in attributes) {
          delete attributes[vertexType.PrimaryId.AttributeName];
        }
      }
      keys.sort((k1, k2) => {
        const k1Index = vertexType.Attributes.findIndex((attr) => attr.AttributeName === k1);
        const k2Index = vertexType.Attributes.findIndex((attr) => attr.AttributeName === k2);

        // if both are attributes, let schema decide the order
        if (k1Index >= 0 && k2Index >= 0) {
          return k1Index - k2Index;
        }
        // rely on stable sort
        return 0;
      });
    }
  } else {
    const edgeType = edgeTypes.find((edgeType) => edgeType.Name === type);
    if (edgeType) {
      keys.sort((k1, k2) => {
        const k1Index = edgeType.Attributes.findIndex((attr) => attr.AttributeName === k1);
        const k2Index = edgeType.Attributes.findIndex((attr) => attr.AttributeName === k2);

        if (k1Index >= 0 && k2Index >= 0) {
          return k1Index - k2Index;
        }
        return 0;
      });
    }
  }

  const thStyle: CSSProperties = {
    textAlign: 'left',
    padding: '5px 3px',
    minWidth: '72px',
    fontWeight: 500,
    borderBottom: '1px solid #ccc',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    boxSizing: 'border-box',
  };

  const typeLabel = `${isSchemaGraph && globalTypesSet.has(type) ? '(Global) ' : ''}` + title;

  return (
    <div
      onWheel={(e: WheelEvent<HTMLDivElement>) => {
        let canScroll = e.currentTarget.clientHeight < e.currentTarget.scrollHeight;
        // only proxy onWheel to cytoscape when the detail panel is not scrollable.
        if (!canScroll) {
          onWheel && onWheel();
        }
      }}
      style={{
        maxHeight: showAsPopover ? '400px' : 'none',
        maxWidth: showAsPopover ? '400px' : '100%',
        width: showAsPopover ? 'none' : '100%',
        overflowY: 'auto',
        overscrollBehavior: 'contain',
        backgroundColor: 'white',
        boxShadow: showAsPopover ? '-2px -2px 6px rgba(0, 0, 0, 0.1), 2px 2px 6px rgba(0, 0, 0, 0.1)' : 'none',
        border: 'none',
        padding: showAsPopover ? '7px 12px' : 0,
        fontSize: '12px',
      }}
    >
      <table
        style={{
          borderCollapse: 'collapse',
        }}
      >
        {showTitle ? (
          <thead>
            <tr>
              <th
                style={{
                  ...thStyle,
                  width: showAsPopover ? 'none' : '96px',
                  maxWidth: showAsPopover ? '160px' : '96px',
                }}
                title={typeLabel}
              >
                {typeLabel}
              </th>
              <th
                style={{
                  ...thStyle,
                  width: showAsPopover ? 'none' : '144px',
                  maxWidth: showAsPopover ? '240px' : '144px',
                }}
                title={type}
              >
                {type}
              </th>
            </tr>
          </thead>
        ) : null}
        <tbody>
          {keys.map((key, index) =>
            attributes[key] === undefined ? null : (
              <ListItem
                name={key}
                key={key}
                value={
                  HelperFunctions.isObject(attributes[key])
                    ? JSON.stringify(attributes[key], null, 2)
                    : String(attributes[key])
                }
                isSchemaGraph={isSchemaGraph}
                last={index === keys.length - 1}
                index={index}
                showAsPopover={showAsPopover}
              />
            )
          )}
        </tbody>
      </table>
    </div>
  );
}

function ListItem({
  name,
  value,
  isSchemaGraph,
  last,
  index,
  showAsPopover,
}: {
  name: string;
  value: string;
  isSchemaGraph: boolean;
  last: boolean;
  index: number;
  showAsPopover: boolean;
}) {
  const [linkCopied, setLinkCopied] = useClipboard(value, {
    successDuration: 1000,
  });

  const tdStyle: CSSProperties = {
    borderWidth: 0,
    borderBottom: !last ? '1px solid #ccc' : 'none',
    padding: '5px 3px',
    cursor: !isSchemaGraph ? 'copy' : 'auto',
    wordBreak: 'break-all',
    backgroundColor: index % 2 === 0 ? '#F4F5F6' : 'white',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    boxSizing: 'border-box',
  };

  useEffect(() => {
    if (linkCopied) {
      showToast({
        kind: KIND.positive,
        message: `${name} copied successfully.`,
      });
    }
  }, [linkCopied, name]);

  return (
    <tr onClick={!isSchemaGraph ? setLinkCopied : undefined}>
      <td
        style={{
          ...tdStyle,
          width: showAsPopover ? 'none' : '96px',
          maxWidth: showAsPopover ? '160px' : '96px',
        }}
        title={name}
      >
        {name}
      </td>
      <td
        style={{
          ...tdStyle,
          width: showAsPopover ? 'none' : '144px',
          maxWidth: showAsPopover ? '240px' : '144px',
        }}
        title={value}
      >
        {value}
      </td>
    </tr>
  );
}
