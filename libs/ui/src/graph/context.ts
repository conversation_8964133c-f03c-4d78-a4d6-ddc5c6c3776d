import { ExternalGraph } from '@tigergraph/models/gvis/insights';
import { Core } from 'cytoscape';
import { createContext, useContext } from 'react';
import { CytoscapeExtensions, NavigatorInstance, Schema } from './type';
import { fitView } from './util';

interface GraphT {
  fit: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
  runLayout: (extraParams?: object, onLayoutStop?: () => void) => void;

  // thumbnail
  navigator: (options: object) => NavigatorInstance;
  forceRender: () => void;

  // need to access underlying instances
  core: { type: 'canvas'; cy: Core & CytoscapeExtensions } | { type: 'webgl'; gl: unknown };
}

export function createGraphForCytoscape(
  cy: Core & CytoscapeExtensions,
  runLayout: (extraParams?: object, onLayoutStop?: () => void) => void
): GraphT {
  return {
    fit: () => fitView(cy),

    zoomIn: () => {
      cy.zoom({
        level: cy.zoom() * 1.1,
        position: {
          x: cy.pan().x + cy.width() / 2,
          y: cy.pan().y + cy.height() / 2,
        },
      });
    },

    zoomOut: () => {
      cy.zoom({
        level: cy.zoom() / 1.1,
        position: {
          x: cy.pan().x + cy.width() / 2,
          y: cy.pan().y + cy.height() / 2,
        },
      });
    },

    runLayout,

    navigator: (options) => {
      return cy.navigator(options);
    },

    forceRender: () => {
      cy.forceRender();
    },

    core: { type: 'canvas', cy },
  };
}

type GraphContextT = {
  id: string;
  schema: Schema;
  graphName: string;
  isSchemaGraph: boolean;
  globalTypes?: string[];
  graphData: ExternalGraph;
  graphT: GraphT;
};

const GraphContext = createContext<GraphContextT | undefined>(undefined);

export const Provider = GraphContext.Provider;

export const useGraphContext = () => {
  const context = useContext(GraphContext);
  if (context === undefined) {
    throw new Error('useGraphContext must be within GraphContext.Provider');
  }

  return context;
};
