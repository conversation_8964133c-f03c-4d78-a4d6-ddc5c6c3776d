import { ElementDefinition } from 'cytoscape';
import React from 'react';
import isEqual from 'lodash/isEqual';

import { ExternalGraph, ExternalNode, ExternalLink, getLinkID, addMissingNode, getNodeID, Color } from '@tigergraph/models/gvis/insights';

import { FilterItem, TableData, Schema, NodePositions } from './type';

const dataSourceEdgeType = 'gs_data_source_map';

// check if two graph is the same
export function isSameGraph(graph1: ExternalGraph, graph2: ExternalGraph) {
  if (graph1.nodes.length !== graph2.nodes.length || graph1.links.length !== graph2.links.length) {
    return false;
  }

  const nodesCache = new Map<string, ExternalNode>();
  const linksCache = new Map<string, ExternalLink>();

  for (let node of graph1.nodes) {
    nodesCache.set(getNodeID(node), node);
  }

  for (let edge of graph1.links) {
    linksCache.set(getLinkID(edge), edge);
  }

  for (let node of graph2.nodes) {
    const node2 = nodesCache.get(getNodeID(node));
    if(!node2) {
      return false;
    }

    if (!isEqual(node, node2)) {
      return false;
    }
  }

  for (let link of graph2.links) {
    const link2 = linksCache.get(getLinkID(link));
    if(!link2) {
      return false;
    }

    if (!isEqual(link, link2)) {
      return false;
    }
  }

  return true;
}

export function convertGraphToCytoscape(
  graph: ExternalGraph,
  presetNodePositions?: NodePositions
): ElementDefinition[] {
  const { nodes, links } = graph;

  let elements: ElementDefinition[] = [];

  for (let node of nodes) {
    const id = getNodeID(node);

    let element: ElementDefinition = {
      data: {
        ...node.attrs,
        id,
        nodeID: node.id,
        type: node.type,
        __others: node.others,
      },
    };

    if (presetNodePositions && presetNodePositions[id]) {
      element.position = presetNodePositions[id];
    }
    elements.push(element);
  }

  let nodePairMap = new Map<string, number>();

  for (let link of links) {
    const source = `${link.source.type}#${link.source.id}`;
    const target = `${link.target.type}#${link.target.id}`;

    const id = source < target ? `${source}#${target}` : `${target}#${source}`;
    const num = nodePairMap.get(id) || 0;
    nodePairMap.set(id, num + 1);
  }

  for (let link of links) {
    const source = `${link.source.type}#${link.source.id}`;
    const target = `${link.target.type}#${link.target.id}`;

    let curve = false;
    if (source === target) {
      // self loop
      curve = true;
    } else {
      // check if there are more than one edge between nodes.
      const id = source < target ? `${source}#${target}` : `${target}#${source}`;
      const num = nodePairMap.get(id)!;
      if (num >= 2) {
        curve = true;
      }
    }

    elements.push({
      data: {
        ...link.attrs,
        id: getLinkID(link),
        source,
        // special handing for aux node, the target is aux node, so it should point to the the corresponding edge
        target: link.target.type === '__aux_node' ? link.target.id : target,
        sourceType: link.source.type,
        targetType: link.target.type,
        type: link.type,
        // todo(lin)
        // only add `directed` to data when link is directed
        // because `edge[directed = true]` as edge selector is not work
        ...(link.directed ? { directed: true } : null),
        __curve: curve,
      },
    });
  }

  return elements;
}

export function convertSchemaToGraph(schema: Schema): ExternalGraph {
  let nodes: ExternalNode[] = [];
  let links: ExternalLink[] = [];

  const { VertexTypes: vertexTypes, EdgeTypes: edgeTypes } = schema;

  if (!vertexTypes) {
    return {
      nodes,
      links,
    };
  }

  for (const vertexType of vertexTypes) {
    const attributes = getSchemaAttributes(vertexType['Attributes']);

    const node: ExternalNode = {
      attrs: attributes,
      id: vertexType['Name'],
      type: vertexType['Name'],
    };

    // Display primary id regardless of whether PrimaryIdAsAttribute is true
    if (vertexType['PrimaryId'] && 
      vertexType['PrimaryId']['AttributeName'] &&
      vertexType['PrimaryId']['AttributeType']?.['Name']) {
        const name = `(PRIMARY ID) ${vertexType['PrimaryId']['AttributeName']}`;
        node.attrs = {
          [name]: vertexType['PrimaryId']['AttributeType']['Name'],
          ...node.attrs,
        };
    }

    nodes.push(node);
  }

  for (const edgeType of edgeTypes) {
    let attributes = getSchemaAttributes(edgeType['Attributes']);
    // Display reverse edge if it exists
    if (edgeType.Config && edgeType.Config['REVERSE_EDGE']) {
      // Put the reverse edge in the first row
      attributes = {
        'reverse edge': edgeType.Config['REVERSE_EDGE'],
        ...attributes,
      };
    }
    if (edgeType['EdgePairs']) {
      edgeType['EdgePairs'].forEach((edgePair) => {
        const link: ExternalLink = {
          source: {
            id: edgePair['From'],
            type: edgePair['From'],
          },
          target: {
            id: edgePair['To'],
            type: edgePair['To'],
          },
          type: edgeType['Name'],
          attrs: attributes,
          ...(edgeType['IsDirected'] ? { directed: true } : null),
        };
        links.push(link);
      });
    } else {
      const link: ExternalLink = {
        source: {
          id: edgeType['FromVertexTypeName'],
          type: edgeType['FromVertexTypeName'],
        },
        target: {
          id: edgeType['ToVertexTypeName'],
          type: edgeType['ToVertexTypeName'],
        },
        type: edgeType['Name'],
        attrs: attributes,
        ...(edgeType['IsDirected'] ? { directed: true } : null),
      };
      links.push(link);
    }
  }

  const graph = {
    nodes,
    links,
  };

  const patchedGraph = addMissingNode(graph);
  return patchedGraph;
}

export function getRelatedNodeTypes(schema: Schema, vType: string): string[] {
  const { EdgeTypes: edgeTypes } = schema;

  const relatedNodeTypes = new Set<string>();

  // ignore direction, consider both source and target
  for (let eType of edgeTypes) {
    if (eType['EdgePairs']) {
      eType['EdgePairs'].forEach((edgePair) => {
        if (edgePair['From'] === vType) {
          relatedNodeTypes.add(edgePair['To']);
        } else if (edgePair['To'] === vType) {
          relatedNodeTypes.add(edgePair['From']);
        }
      });
    } else {
      if (eType['FromVertexTypeName'] === vType) {
        relatedNodeTypes.add(eType['ToVertexTypeName']);
      } else if (eType['ToVertexTypeName'] === vType) {
        relatedNodeTypes.add(eType['FromVertexTypeName']);
      }
    }
  }

  return Array.from(relatedNodeTypes);
}

export function getSchemaAttributes(
  schemaAttributes: {
    AttributeName: string;
    AttributeType: {
      Name: string;
    };
  }[]
): Record<string, string> {
  return schemaAttributes.reduce((acc, schemaAttribute) => {
    return {
      ...acc,
      [schemaAttribute.AttributeName]: schemaAttribute.AttributeType.Name,
    };
  }, {});
}

export function getFilterAndTableData(
  graph: ExternalGraph,
  schema: Schema
): {
  nodeItems: FilterItem[];
  edgeItems: FilterItem[];
  nodeTablesMap: Map<string, TableData>;
  edgeTablesMap: Map<string, TableData>;
} {
  if (!graph) {
    return {
      nodeItems: [],
      edgeItems: [],
      nodeTablesMap: new Map(),
      edgeTablesMap: new Map(),
    };
  }

  const { nodes, links } = graph;

  const nodeTablesMap = new Map<string, TableData>();
  // node type => node's total number
  const nodeTypeMap = new Map<string, number>();

  const deduplicateNodeSet = new Set<string>();
  for (let node of nodes) {
    const type = node.type;

    const deduplicateID = `${type}#${node.id}`;
    if (deduplicateNodeSet.has(deduplicateID)) {
      continue;
    }
    deduplicateNodeSet.add(deduplicateID);

    if (!nodeTypeMap.get(type)) {
      nodeTypeMap.set(type, 1);
    } else {
      nodeTypeMap.set(type, nodeTypeMap.get(type)! + 1);
    }

    let table = nodeTablesMap.get(type);
    if (!table) {
      table = {
        columns: [],
        data: [],
      };
    }

    if (table.columns.length === 0) {
      let keys = Object.keys(node.attrs || {}).filter((key) => key !== 'id');
      // __cyID is used to get cytoscape id for table row.
      table.columns = ['id', ...keys, '__cyID'];
    }

    const object = {
      ...node.attrs,
      id: node.id,
      __cyID: deduplicateID,
    };

    table.data.push(getTableRowValue(object, table.columns));

    nodeTablesMap.set(type, table);
  }

  const edgeTablesMap = new Map<string, TableData>();
  const edgeTypeMap = new Map<string, number>();

  const deduplicateEdgeSet = new Set<string>();
  for (let link of links) {
    const type = link.type;

    const deduplicateID = getLinkID(link);
    if (deduplicateEdgeSet.has(deduplicateID)) {
      continue;
    }
    deduplicateEdgeSet.add(deduplicateID);

    if (!edgeTypeMap.get(type)) {
      edgeTypeMap.set(type, 1);
    } else {
      edgeTypeMap.set(type, edgeTypeMap.get(type)! + 1);
    }

    let table = edgeTablesMap.get(type);
    if (!table) {
      table = {
        columns: [],
        data: [],
      };
    }

    if (table.columns.length === 0) {
      let keys = Object.keys(link.attrs || {}).filter((key) => key !== 'source' && key !== 'target');
      table.columns = ['source', 'target', ...keys, '__cyID'];
    }

    const object = {
      ...link.attrs,
      source: link.source.id,
      target: link.target.id,
      __cyID: deduplicateID,
    };

    table.data.push(getTableRowValue(object, table.columns));

    edgeTablesMap.set(type, table);
  }

  const { VertexTypes: vertexTypes, EdgeTypes: edgeTypes } = schema;

  const nodeColorMap = new Map<string, string>();
  for (let vertex of vertexTypes) {
    nodeColorMap.set(vertex['Name'], vertex.style?.fillColor);
  }

  const edgeColorMap = new Map<string, string>();
  for (let edge of edgeTypes) {
    edgeColorMap.set(edge['Name'], edge.style?.fillColor);
  }

  const nodeItems: FilterItem[] = [];
  const edgeItems: FilterItem[] = [];
  const color = new Color();

  for (let [type, num] of nodeTypeMap.entries()) {
    nodeItems.push({
      type,
      num,
      // If the color map doesn't contain the type
      // It will get a value of a bulit-in color array from the Color instance
      color: nodeColorMap.get(type) || color.getColor(type),
    });
  }

  for (let [type, num] of edgeTypeMap.entries()) {
    edgeItems.push({
      type,
      num,
      // If the color map doesn't contain the type
      // It will get a value of a bulit-in color array from the Color instance
      color: edgeColorMap.get(type) || color.getColor(type),
    });
  }

  return {
    nodeItems,
    edgeItems,
    nodeTablesMap,
    edgeTablesMap,
  };
}

function getTableRowValue(obj: Record<string, any>, columns: string[]) {
  const values: React.ReactNode[] = [];

  for (let column of columns) {
    let value = obj[column];

    const type = typeof value;

    if (value === null || value === undefined) {
      // no op
    } else if (type === 'bigint' || type === 'boolean' || type === 'number' || type === 'string') {
      // no op
    } else {
      value = JSON.stringify(value, null, 4);
    }
    values.push(value);
  }

  return values;
}

export function getNodesAttributes(graph: ExternalGraph) {
  const nodesAttributes = new Map<string, Map<string, Set<any>>>();

  const { nodes } = graph;

  for (let node of nodes) {
    let nodeAttributes = nodesAttributes.get(node.type);
    if (!nodeAttributes) {
      nodeAttributes = new Map<string, Set<any>>();
    }

    const obj: { [index: string]: any } = {
      id: node.id,
      ...node.attrs,
    };

    for (let key of Object.keys(obj)) {
      const value = obj[key];
      let nodeAttribute = nodeAttributes.get(key);
      if (!nodeAttribute) {
        nodeAttribute = new Set<any>();
      }
      nodeAttribute.add(value);

      nodeAttributes.set(key, nodeAttribute);
    }

    nodesAttributes.set(node.type, nodeAttributes);
  }

  return nodesAttributes;
}

export function addGraph(
  graph1: ExternalGraph,
  graph2: ExternalGraph
): {
  finalGraph: ExternalGraph;
  deltaGraph: ExternalGraph;
} {
  const deltaNodes: ExternalNode[] = [];
  const deltaLinks: ExternalLink[] = [];

  const nodeCache = new Set<string>();
  for (let node of graph1.nodes) {
    nodeCache.add(`${node.type}#${node.id}`);
  }
  for (let node of graph2.nodes) {
    if (!nodeCache.has(`${node.type}#${node.id}`)) {
      deltaNodes.push(node);
    }
  }
  const finalNodes = graph1.nodes.concat(deltaNodes);

  const linkCache = new Set<string>();
  for (let link of graph1.links) {
    linkCache.add(getLinkID(link));
  }
  for (let link of graph2.links) {
    if (!linkCache.has(getLinkID(link))) {
      deltaLinks.push(link);
    }
  }
  const finalLinks = graph1.links.concat(deltaLinks);

  return {
    finalGraph: {
      nodes: finalNodes,
      links: finalLinks,
    },
    deltaGraph: {
      nodes: deltaNodes,
      links: deltaLinks,
    },
  };
}

export function removeGraph(
  graph1: ExternalGraph,
  graph2: ExternalGraph
): {
  finalGraph: ExternalGraph;
  deltaGraph: ExternalGraph;
} {
  const finalNodes: ExternalNode[] = [];
  const finalLinks: ExternalLink[] = [];

  const nodeCache = new Set<string>();
  for (let node of graph2.nodes) {
    nodeCache.add(`${node.type}#${node.id}`);
  }
  for (let node of graph1.nodes) {
    if (!nodeCache.has(`${node.type}#${node.id}`)) {
      finalNodes.push(node);
    }
  }

  const linkCache = new Set<string>();
  for (let link of graph2.links) {
    linkCache.add(getLinkID(link));
  }
  for (let link of graph1.links) {
    if (!linkCache.has(getLinkID(link))) {
      finalLinks.push(link);
    }
  }

  return {
    finalGraph: {
      nodes: finalNodes,
      links: finalLinks,
    },
    deltaGraph: graph2,
  };
}

export function updateGraphData(graph: ExternalGraph, updateNode: (node: ExternalNode) => ExternalNode): ExternalGraph {
  const { nodes, links } = graph;
  const updatedNodes: ExternalNode[] = [];

  for (let node of nodes) {
    let newNode = updateNode(node);
    updatedNodes.push(newNode);
  }

  return {
    nodes: updatedNodes,
    links,
  };
}

// remove duplicate edge for the initial graph data

// background
// 1 graph parser will remove duplicate node/edge, but not consider reverse edge
//      the graph data and graph schema is load concurrently, so we can't handle reverse edge in graph parser
// 2 in addGraph
//      when add node/edge to the existing graph
export function removeDuplicateEdges(graph: ExternalGraph): ExternalGraph {
  const deduplicateLinks: ExternalLink[] = [];
  const linkCache = new Set<string>();
  for (let link of graph.links) {
    if (!linkCache.has(getLinkID(link))) {
      deduplicateLinks.push(link);
      linkCache.add(getLinkID(link));
    }
  }
  return {
    nodes: graph.nodes,
    links: deduplicateLinks,
  };
}

// keep consistent with graph studio
// reverse the reverse edge
// add missing edge discriminator
// add missing vertices
export function canonicalizeGraph(graph: ExternalGraph, schema: Schema): ExternalGraph {
  const schemaReverseEdgeMap = getSchemaReverseEdgeMap(schema);
  const nodeCache = new Set<string>();
  graph.nodes.forEach((node) => {
    nodeCache.add(getNodeID(node));
  });

  graph.links.forEach((link) => {
    if (schemaReverseEdgeMap.has(link.type)) {
      [link.source, link.target] = [link.target, link.source];
      link.type = schemaReverseEdgeMap.get(link.type)!;
    }
    if (link.type.startsWith(dataSourceEdgeType)) {
      return;
    }
    if (!nodeCache.has(getNodeID(link.source))) {
      graph.nodes.push(link.source);
      nodeCache.add(getNodeID(link.source));
    }
    if (!nodeCache.has(getNodeID(link.target))) {
      graph.nodes.push(link.target);
      nodeCache.add(getNodeID(link.target));
    }
  });

  if (!schema.DiscriminatorMap) {
    schema.DiscriminatorMap = getDiscriminatorMap(schema);
  }

  return graph;
}

export function getDiscriminatorMap(schema: Schema): { [key: string]: string[] } {
  const discriminatorMapMap = {} as { [key: string]: string[] };
  schema['EdgeTypes'].forEach((edgeType) => {
    if (edgeType['CompositeDiscriminator']) {
      discriminatorMapMap[edgeType['Name']] = edgeType['CompositeDiscriminator'];
    }
  });
  return discriminatorMapMap;
}

function getSchemaReverseEdgeMap(schema: Schema): Map<string, string> {
  const schemaReverseEdgeMap = new Map<string, string>();
  schema['EdgeTypes'].forEach((edgeType) => {
    if (edgeType['Config']?.['REVERSE_EDGE']) {
      schemaReverseEdgeMap.set(edgeType['Config']['REVERSE_EDGE'], edgeType['Name']);
    }
  });
  return schemaReverseEdgeMap;
}
