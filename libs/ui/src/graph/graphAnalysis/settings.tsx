import React from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { KIND } from 'baseui/toast';
import { useEffect, useMemo } from 'react';
import { ExternalGraph } from '@tigergraph/models/gvis/insights';
import StyledSelect from '../../styledSelect';
import { StyledToast } from '../../styledToasterContainer';
import { SEARCH_KEYWORDS, SearchItem } from '../../chartSearch/type';
import SearchTag from '../../chartSearch/searchTag';
import ChartSettingItemContainer from '../../chartSetting/chartSettingItemContainer';
import TemplateDrivenChartSettings from '../../chartSetting/templateDrivenChartSettings';
import { getNumTypeAttrsFromElem } from './utils';
import { FilterItem, Schema } from '../type';
import { PageRankSettings } from './types';

export type AlgorithmnSettingsProps = {
  nodeItems: FilterItem[];
  edgeItems: FilterItem[];
  graph: ExternalGraph;
  schema: Schema;
  settings: PageRankSettings;
  updateSettings: (key: string, value: any) => void;
};

export default function AlgorithmSettings({
  nodeItems,
  edgeItems,
  graph,
  schema,
  settings,
  updateSettings,
}: AlgorithmnSettingsProps) {
  const [css] = useStyletron();

  let { weighted, edgeType, vertexType, edgeAttr } = settings;

  const vertexTypeOptions = useMemo(() => {
    return schema.EdgeTypes.reduce((options, edge) => {
      const fromVertexType = edge.FromVertexTypeName;
      return fromVertexType === edge.ToVertexTypeName &&
        fromVertexType !== '*' &&
        nodeItems.find((nodeItem) => nodeItem.type === fromVertexType) &&
        !options.find((vertexType) => vertexType === fromVertexType)
        ? [...options, fromVertexType]
        : options;
    }, [] as string[]);
  }, [schema, nodeItems]);

  useEffect(() => {
    if (vertexTypeOptions.length && (!vertexType || !vertexTypeOptions.includes(vertexType))) {
      updateSettings('vertexType', vertexTypeOptions[0]);
    }
  }, [updateSettings, vertexTypeOptions, vertexType]);

  const getVertexLabel = ({ option }: any) => {
    if (!option || !option.id) {
      return null;
    }
    const item: SearchItem = {
      id: option.id,
      type: SEARCH_KEYWORDS.vertex,
      data: option.id,
    };
    const color = schema.VertexTypes.find((v) => v.Name === item.data)?.style.fillColor;
    return <SearchTag item={item} color={color} />;
  };

  const edgeTypeOptions = useMemo(() => {
    return schema.EdgeTypes.filter((edge) => {
      return (
        edge.FromVertexTypeName === vertexType &&
        edge.ToVertexTypeName === vertexType &&
        edgeItems.find((edgeItem) => edgeItem.type === edge.Name)
      );
    }).map((edge) => {
      return edge.Name;
    });
  }, [schema, vertexType, edgeItems]);

  useEffect(() => {
    if (edgeTypeOptions.length) {
      if (!edgeType || !edgeTypeOptions.includes(edgeType)) {
        updateSettings('edgeType', edgeTypeOptions[0]);
      }
    } else if (edgeType) {
      updateSettings('edgeType', '');
    }
  }, [edgeType, edgeTypeOptions, updateSettings]);

  const edgeAttrs: string[] = useMemo(() => {
    if (!weighted || !edgeType) {
      return [];
    }
    // todo: use schema info rather than instance info
    const elem = graph.links.find((link) => link.type === edgeType);
    return elem ? getNumTypeAttrsFromElem(elem) : [];
  }, [weighted, edgeType, graph]);

  useEffect(() => {
    if (edgeAttrs.length) {
      if (!edgeAttr || !edgeAttrs.includes(edgeAttr)) {
        updateSettings('edgeAttr', edgeAttrs[0]);
      }
    } else if (edgeAttr) {
      updateSettings('edgeAttr', '');
    }
  }, [edgeAttr, edgeAttrs, updateSettings]);

  const getEdgeLabel = ({ option }: any) => {
    if (!option || !option.id) {
      return null;
    }
    const item: SearchItem = {
      id: option.id,
      type: SEARCH_KEYWORDS.edge,
      data: option.id,
      direction: 1,
    };
    const color = schema.EdgeTypes.find((v) => v.Name === item.data)?.style.fillColor;
    return <SearchTag item={item} color={color} />;
  };

  return (
    <>
      {edgeTypeOptions.length === 0 && (
        <div className={css({ marginTop: '8px' })}>
          <StyledToast
            size="compact"
            kind={KIND.info}
            closeable={false}
            message="This graph can't run PageRank because it doesn't have any self-edges."
          />
        </div>
      )}
      <ChartSettingItemContainer label={'Vertex type'} helpText={''} style={{ marginBottom: '16px', marginTop: '8px' }}>
        <StyledSelect
          clearable={false}
          value={[{ id: vertexType }]}
          options={vertexTypeOptions.map((option) => ({ id: option, label: option }))}
          onChange={({ value }) => {
            updateSettings('vertexType', value[0].id);
          }}
          getOptionLabel={getVertexLabel}
          getValueLabel={getVertexLabel}
        />
      </ChartSettingItemContainer>
      <ChartSettingItemContainer label={'Edge type'} helpText={''} style={{ marginBottom: '10px' }}>
        <StyledSelect
          clearable={false}
          value={[{ id: edgeType }]}
          options={edgeTypeOptions.map((option) => ({ id: option, label: option }))}
          onChange={({ value }) => {
            updateSettings('edgeType', value[0].id);
          }}
          getOptionLabel={getEdgeLabel}
          getValueLabel={getEdgeLabel}
        />
      </ChartSettingItemContainer>
      <TemplateDrivenChartSettings
        settingsTemplate={{
          weighted: {
            label: 'Weighted',
            type: 'toggle',
            default: false,
            helpText: '',
          },
        }}
        settings={settings}
        updateSetting={updateSettings}
      />
      {weighted && (
        <TemplateDrivenChartSettings
          settingsTemplate={{
            edgeAttr: {
              label: 'Weight attribute',
              type: 'list',
              default: '',
              values: edgeAttrs,
              helpText: '',
            },
          }}
          settings={settings}
          updateSetting={updateSettings}
        />
      )}
    </>
  );
}
