import louvain, { DetailedLouvainOutput } from 'graphology-communities-louvain';
import toUndirected from 'graphology-operators/to-undirected';
import Graph from 'graphology';
import { Rule } from '../../ruleStyle/type';
import { AlgorithmResult, PageRankSettings, StyleName } from './types';
import { ExternalGraph, ExternalNode, ExternalLink } from '@tigergraph/models/gvis/insights';
import { Core, SearchPageRankOptions, SearchPageRankResult } from 'cytoscape';
import { updateGraphData } from '../data';

export type PageRankResult = {
  vertexType: string;
  rank: SearchPageRankResult;
};

export function runPageRank(cy: Core, settings: PageRankSettings): PageRankResult | undefined {
  const { vertexType, edgeType, weighted, edgeAttr } = settings;
  if (!vertexType || !edgeType || (weighted && !edgeAttr)) {
    return undefined;
  }

  const eles = cy.filter((ele) => {
    return ele.data('type') === vertexType || ele.data('type') === edgeType;
  });
  const options = weighted
    ? {
        weight: (edge: any) => {
          return edge.data(edgeAttr);
        },
      }
    : {};
  const pageRankResult = eles.pageRank(options as SearchPageRankOptions);
  return {
    vertexType,
    rank: pageRankResult,
  };
}

export function runLouvain(externalGraph: ExternalGraph): DetailedLouvainOutput {
  let graph = new Graph({
    allowSelfLoops: true,
    multi: true, // support parallel edges
    type: 'mixed',
  });

  const { nodes, links } = externalGraph;
  for (let node of nodes) {
    const id = `${node.type}#${node.id}`;
    graph.addNode(id);
  }

  for (let link of links) {
    const source = `${link.source.type}#${link.source.id}`;
    const target = `${link.target.type}#${link.target.id}`;

    if (link.directed) {
      graph.addDirectedEdge(source, target);
    } else {
      graph.addUndirectedEdge(source, target);
    }
  }

  // convert to undirected graph
  graph = toUndirected(graph);

  // ignore weights
  const louvainOutput = louvain.detailed(graph, {
    getEdgeWeight: null,
  });
  return louvainOutput;
}

function applyGraphResultToStyling(
  styleRules: AlgorithmResult,
  settings: Record<string, any>,
  onSettingUpdate: (key: string, value: any) => void,
  undo: boolean
) {
  let rulesByType = settings.rulesByType || {};
  const keys = Object.keys(styleRules);
  keys.forEach((key) => {
    const newRule = styleRules[key];
    const originRules: Rule[] = rulesByType[key]?.slice() || [];
    const oldIndex = originRules.findIndex((rule) => rule.fieldName === newRule.fieldName);
    oldIndex !== -1 && originRules.splice(oldIndex, 1);
    if (!undo) {
      originRules.push(newRule);
    }
    rulesByType = { ...rulesByType, [key]: originRules };
  });
  if (keys.length) {
    onSettingUpdate('rulesByType', { ...rulesByType });
  }
}

export function getAlgorithmStyleRule(
  algorithm: string,
  styleBy: StyleName,
  range?: { min: number; max: number }
): Rule {
  if (range) {
    const { min, max } = range;
    return styleBy === 'color'
      ? {
          fieldType: 'number',
          fieldName: algorithm,
          condition: 'range',
          conditionValue: '',
          conditionStartValue: min,
          conditionEndValue: max,
          styleKey: 'background-color',
          styleType: 'color',
          styleLabel: 'Vertex color',
          styleValue: '#ff0000',
          palateName: 'Blues',
          styleStartValue: '#f7fbff',
          styleStartLabel: '',
          styleEndValue: '#08306b',
          styleEndLabel: '',
        }
      : {
          fieldType: 'number',
          fieldName: algorithm,
          condition: 'range',
          conditionValue: '',
          conditionStartValue: min,
          conditionEndValue: max,
          styleKey: 'node-radius',
          styleType: 'numeric',
          styleLabel: 'Vertex size',
          styleValue: '#ff0000',
          palateName: 'Blues',
          styleStartValue: '0.25',
          styleStartLabel: '0.25x',
          styleEndValue: '2',
          styleEndLabel: '2x',
        };
  }

  return {
    fieldType: 'string',
    fieldName: algorithm,
    condition: 'unique value',
    conditionValue: '',
    conditionStartValue: 0,
    conditionEndValue: 0,
    styleKey: 'background-color',
    styleType: 'color',
    styleLabel: 'Vertex color',
    styleValue: '#ff0000',
    palateName: 'schemePaired',
    styleStartValue: '',
    styleStartLabel: '',
    styleEndValue: '',
    styleEndLabel: '',
  };
}

export function applyLouvainResult(
  cy: Core,
  externalGraph: ExternalGraph,
  louvainOutput: DetailedLouvainOutput,
  onSetGraph: (graph: ExternalGraph) => void,
  settings: Record<string, any>,
  onSettingUpdate: (key: string, value: any) => void,
  undo: boolean
) {
  // 1 apply `louvain` attr to cytoscape
  const { communities } = louvainOutput;
  const nodeIDs = Object.keys(communities);
  cy.batch(() => {
    for (let node of nodeIDs) {
      let community = communities[node];
      let target = cy.getElementById(node);
      if (target && community !== undefined) {
        if (undo) {
          target.removeData('louvain');
        } else {
          target.data('louvain', `${community}`);
        }
      }
    }
  });

  // 2 apply `louvain` attr to ExternalGraph
  const newGraph = updateGraphData(externalGraph, (node) => {
    let nodeID = `${node.type}#${node.id}`;
    if (communities[nodeID] !== undefined) {
      if (undo) {
        let attr = { ...node.attrs };
        delete attr.louvain;
        return {
          ...node,
          attrs: {
            ...attr,
          },
        };
      } else {
        return {
          ...node,
          attrs: {
            ...node.attrs,
            louvain: communities[nodeID],
          },
        };
      }
    } else {
      return node;
    }
  });

  setTimeout(() => {
    onSetGraph(newGraph);
  });

  // 3 generate style rule
  const { nodes } = externalGraph;
  const nodeTypes: string[] = [];
  for (let node of nodes) {
    if (!nodeTypes.includes(node.type)) {
      nodeTypes.push(node.type);
    }
  }

  const styleRules: Record<string, Rule> = {};
  nodeTypes.forEach((type) => {
    styleRules[type] = getAlgorithmStyleRule('louvain', 'color');
  });

  // 4 apply style rule to setting
  applyGraphResultToStyling(styleRules, settings, onSettingUpdate, undo);
}

export function applyPageRankResult(
  cy: Core,
  externalGraph: ExternalGraph,
  pageRankResult: PageRankResult,
  onSetGraph: (graph: ExternalGraph) => void,
  settings: Record<string, any>,
  onSettingUpdate: (key: string, value: any) => void,
  undo: boolean
) {
  const { vertexType, rank } = pageRankResult;

  const eles = cy.filter((ele) => {
    return ele.data('type') === vertexType;
  });

  let min = 1;
  let max = 0;
  let rankMapping = new Map<string, number>();
  eles.forEach((node) => {
    const value = rank.rank(node);
    if (value) {
      rankMapping.set(node.id(), value);

      min = Math.min(min, value);
      max = Math.max(max, value);

      if (undo) {
        node.removeData('pageRank');
      } else {
        node.data('pageRank', value);
      }
    }
  });

  // @ts-ignore
  const newGraph = updateGraphData(externalGraph, (node) => {
    let nodeID = `${node.type}#${node.id}`;
    if (rankMapping.has(nodeID)) {
      if (undo) {
        let attr = { ...node.attrs };
        delete attr.pageRank;
        return {
          ...node,
          attrs: {
            ...attr,
          },
        };
      } else {
        return {
          ...node,
          attrs: {
            ...node.attrs,
            pageRank: rankMapping.get(nodeID),
          },
        };
      }
    } else {
      return node;
    }
  });

  setTimeout(() => {
    onSetGraph(newGraph);
  });

  const styleRules = { [vertexType]: getAlgorithmStyleRule('pageRank', 'size', { min, max }) };

  applyGraphResultToStyling(styleRules, settings, onSettingUpdate, undo);
}

export function getNumTypeAttrsFromElem(element: ExternalNode | ExternalLink): string[] {
  return Object.entries(element.attrs || {})
    .filter(([, value]) => typeof value === 'number')
    .map(([key]) => key);
}
