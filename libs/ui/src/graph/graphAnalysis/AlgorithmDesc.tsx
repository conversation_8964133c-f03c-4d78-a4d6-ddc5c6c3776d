import React from 'react';
import <PERSON>Link from '../../docLink';
import { AlgorithmName } from './types';

function AlgorithmDesc({ type }: { type: AlgorithmName }) {
  return (
    <>
      {type === 'pageRank' && (
        <>
          The PageRank algorithm measures the influence of each vertex on every other vertex. PageRank influence is
          defined recursively: a vertex's influence is based on the influence of the vertices which refer to it.&nbsp;
          <DocLink href="https://docs.tigergraph.com/graph-ml/current/centrality-algorithms/pagerank" />
        </>
      )}
      {type === 'louvain' && (
        <>
          The Louvain Method for community detection partitions the vertices in a graph by approximately maximizing the
          graph's modularity score.&nbsp;
          <DocLink href="https://docs.tigergraph.com/graph-ml/current/community-algorithms/louvain" />
        </>
      )}
    </>
  );
}

export default AlgorithmDesc;
