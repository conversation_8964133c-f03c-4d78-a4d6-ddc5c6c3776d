import { CollectionReturnValue, Core, ElementDefinition } from 'cytoscape';
import { AxiosError } from 'axios';

import { ExternalGraph, ExternalNode, ExternalLink, getLinkID } from '@tigergraph/models/gvis/insights';
import { Rules } from '../ruleStyle/type';
import { GlobalParams } from '../globalParams';
import { evaluateRules } from '../ruleStyle/evaluator';
import { Schema, SettingType } from './type';

// fit with clamped max zoom
export function fitView(cy: Core) {
  cy.maxZoom(2);

  try {
    cy.fit(undefined, 24);
  } catch (error) {}

  cy.maxZoom(10);
}

export function addElements(cy: Core, newElements: ElementDefinition[]) {
  let results: ElementDefinition[] = [];

  for (let element of newElements) {
    if (!cy.hasElementWithId(element.data.id!)) {
      results.push(element);
    }
  }

  return results;
}

// assume all elements are in graph
export function elementsToGraph(graph: ExternalGraph, collection: CollectionReturnValue): ExternalGraph {
  const nodes: ExternalNode[] = [];
  const links: ExternalLink[] = [];

  const nodeMapping = new Map<string, ExternalNode>();
  for (let node of graph.nodes) {
    const id = `${node.type}#${node.id}`;
    nodeMapping.set(id, node);
  }
  const linkMapping = new Map<string, ExternalLink>();
  for (let link of graph.links) {
    const id = getLinkID(link);
    linkMapping.set(id, link);
  }

  for (let ele of collection.toArray()) {
    if (nodeMapping.has(ele.data('id'))) {
      nodes.push(nodeMapping.get(ele.data('id'))!);
    } else if (linkMapping.has(ele.data('id'))) {
      links.push(linkMapping.get(ele.data('id'))!);
    } else {
      console.warn('could not find data in graph', ele);
    }
  }

  return {
    nodes,
    links,
  };
}

export function getRefreshSettingForGraph(cy: Core) {
  const length = cy.elements().length;

  if (length < 100) {
    return 1;
  }
  if (length < 200) {
    return 2;
  }
  if (length < 1000) {
    return 3;
  }
  if (length < 2000) {
    return 4;
  }
  if (length < 3000) {
    return 5;
  }
  if (length < 4000) {
    return 6;
  }
  if (length < 5000) {
    return 8;
  }
  return 10;
}
export function getErrorMessage(error: AxiosError<{ message?: string }> | Error) {
  let message = error.message;

  if ('response' in error) {
    // check for axios error
    // @ts-ignore
    if (error.response['error']) {
      // @ts-ignore
      message = error.response['error'];
    } else if (error?.response?.data?.message) {
      // error message from rest api
      message = error.response?.data?.message;
    }
  }

  if (!message) {
    message = '';
  }

  return message;
}

export function updateGraphLabel(
  cy: Core, attrMapGraphLabel: Record<string, string>, schema: Schema
) {
  const { VertexTypes: vertexTypes } = schema;
  if (!attrMapGraphLabel) {
    return;
  } else {
    cy.batch(() => {
      cy.nodes().forEach((node) => {
        const nodeType = node.data().type;
        const attr = attrMapGraphLabel[nodeType];
        const vertexType = vertexTypes.find((vertexType) => vertexType.Name === nodeType);
        if(attrMapGraphLabel[nodeType] && vertexType && (vertexType.PrimaryId.AttributeName !== attr)) {
          node.style({
            label: `${node.data(attr)}`,
            'text-opacity': `${node.data(attr)}` === '' ? 0 : 1,
          });
        } else {
          node.style({
            label: node.data('nodeID'),
            'text-opacity': 1,
          });
        }
      });
    });
  }
}

export function updateGraphStyle(
  cy: Core,
  rulesByType: Record<string, Rules> | undefined,
  globalParams?: GlobalParams
) {
  cy.batch(() => {
    cy.nodes().forEach((node) => {
      const type = node.data('type');
      node.removeStyle('background-color width height');
      if (rulesByType && rulesByType[type]) {
        const rules = rulesByType[type];
        let data = {
          ...node.data(),
        };
        // original id is for cytoscape
        // but we need id data from tigergraph to do vertex styling.
        data['id'] = data['nodeID'];
        const style = evaluateRules(data, rules, globalParams);

        let nodeStyle: Record<string, any> = {};
        if (style['background-color']) {
          nodeStyle['background-color'] = style['background-color'];
        }
        if (style['node-radius']) {
          nodeStyle['width'] = `${style['node-radius']}px`;
          nodeStyle['height'] = `${style['node-radius']}px`;
        }
        node.style(nodeStyle);
      }
    });
    cy.edges().forEach((edge) => {
      const type = edge.data('type');
      edge.removeStyle('line-color line-style width');
      if (rulesByType && rulesByType[type]) {
        const rules = rulesByType[type];
        const style = evaluateRules(edge.data(), rules, globalParams);
        let edgeStyle: Record<string, any> = {};
        if (style['line-color']) {
          edgeStyle['line-color'] = style['line-color'];
        }
        if (style['line-style']) {
          edgeStyle['line-style'] = style['line-style'];
        }
        if (style['edge-width']) {
          edgeStyle['width'] = `${style['edge-width']}px`;
        }
        edge.style(edgeStyle);
      }
    });
  });
}

export function getElementAttributes(
  elementData: Record<string, any>,
  isSchemaGraph: boolean,
  schema: Schema,
  alwaysKeepID?: boolean
) {
  const isNode = !!elementData['nodeID'];
  const type = elementData['type'];
  let showTitle = true;

  let attributes = { ...elementData };

  delete attributes['id'];
  delete attributes['type'];

  // 1. delete unwanted attributes
  // 2. put id first
  if (isNode) {
    const { VertexTypes: vertexTypes } = schema;
    const vertexType = vertexTypes.find((vertexType) => vertexType.Name === type);

    if (!vertexType) {
      // no schema type for this node, means this is a csv file node, just show attrs
      delete attributes['nodeID'];
      showTitle = false;
    } else {
      const { AttributeName, PrimaryIdAsAttribute } = vertexType.PrimaryId;
      const hideNodeID = PrimaryIdAsAttribute && AttributeName in attributes;

      const id = attributes['nodeID'];
      delete attributes['nodeID'];
      // 1 for schema, id is faked.
      // 2 hide primary id if needed
      if (!isSchemaGraph && (!hideNodeID || alwaysKeepID)) {
        attributes['id'] = id;
        attributes = {
          id,
          ...attributes,
        };
      }
    }
  } else {
    delete attributes['directed'];
    delete attributes['source'];
    delete attributes['target'];
    delete attributes['sourceType'];
    delete attributes['targetType'];
  }

  return {
    attributes,
    showTitle,
  };
}

// for thumbnail, we need to overwrite some global style
export const injectCSS = (css: string) => {
  let el = document.createElement('style');
  el.innerText = css;
  document.head.appendChild(el);
  return el;
};
