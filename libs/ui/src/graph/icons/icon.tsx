import React from 'react';

export function TableIcon() {
  return (
    <svg width={20} height={20} viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M8.438 8.453h3.906v8.578H8.438V8.453zm5.468 8.578h2.344c.86 0 1.563-.703 1.563-1.562V8.438h-3.907v8.593zM16.25 2.97H4.531c-.86 0-1.562.703-1.562 1.562v2.344h14.844V4.531c0-.86-.704-1.562-1.563-1.562zm-13.281 12.5c0 .86.703 1.562 1.562 1.562h2.344V8.438H2.969v7.03z" />
    </svg>
  );
}

export function AlgorithmIcon() {
  return (
    <svg width={18} height={18} viewBox="0 0 18 18" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <circle cx={13.125} cy={1.875} r={1.875} />
      <circle cx={4.5} cy={2.25} r={2.25} />
      <circle cx={2.625} cy={10.875} r={2.625} />
      <circle cx={11.25} cy={14.25} r={3.75} />
      <circle cx={16.125} cy={9.375} r={1.875} />
      <circle cx={11.7133} cy={4.96661} r={1.125} transform="rotate(-4.696 11.713 4.967)" />
      <circle cx={8.625} cy={7.875} r={1.875} />
      <path d="M2.75 1.5l3.524.747S4.584 4.881 4.19 6.735c-.393 1.853.084 4.946.084 4.946L.75 10.934s1.579-2.657 1.972-4.51C3.115 4.57 2.75 1.5 2.75 1.5z" />
      <path d="M2.273 13.266l1.813-4.19s1.33 2.106 2.552 2.634c1.222.529 3.667.057 3.667.057l-1.812 4.19s-1.388-1.973-2.61-2.501c-1.222-.529-3.61-.19-3.61-.19zM14.14.75l.085 2.525s-2.951-.679-4.845-.615c-1.894.063-4.793.938-4.793.938l-.085-2.525s2.949.598 4.843.535C11.238 1.544 14.14.75 14.14.75z" />
      <path d="M17 8.438l-1.93.853s-.338-2.37-.94-3.733C13.525 4.195 12 2.351 12 2.351l1.93-.854s.4 2.342 1.002 3.706C15.536 6.566 17 8.437 17 8.437zM11.129 4.242l1.142 1.256s-1.035.236-1.477.638c-.443.402-.775 1.41-.775 1.41L8.877 6.291s.998-.276 1.44-.678c.443-.403.812-1.37.812-1.37z" />
      <path d="M9.004 9.344l.554-2.229s1.963 1.216 3.344 1.559c1.38.343 3.685.187 3.685.187l-.554 2.229s-1.98-1.145-3.362-1.488c-1.38-.343-3.667-.258-3.667-.258z" />
    </svg>
  );
}

export function ResetIcon() {
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1167_1367)">
        <path d="M12 8.25A3.749 3.749 0 008.25 12 3.749 3.749 0 0012 15.75 3.749 3.749 0 0015.75 12 3.749 3.749 0 0012 8.25zm-7.5 6.563a.94.94 0 00-.938.937v2.813a1.88 1.88 0 001.876 1.875H8.25a.94.94 0 00.938-.938.94.94 0 00-.938-.938H6.375a.94.94 0 01-.938-.937V15.75a.94.94 0 00-.937-.938zm.938-8.438a.94.94 0 01.937-.938H8.25a.94.94 0 00.938-.937.94.94 0 00-.938-.938H5.437a1.88 1.88 0 00-1.875 1.876V8.25a.94.94 0 00.938.938.94.94 0 00.938-.938V6.375zm13.125-2.813H15.75a.94.94 0 00-.938.938.94.94 0 00.938.938h1.875a.94.94 0 01.938.937V8.25a.94.94 0 00.937.938.94.94 0 00.938-.938V5.437a1.88 1.88 0 00-1.875-1.875zm0 14.063a.94.94 0 01-.938.938H15.75a.94.94 0 00-.938.937.94.94 0 00.938.938h2.813a1.88 1.88 0 001.875-1.875V15.75a.94.94 0 00-.938-.938.94.94 0 00-.938.938v1.875z" />
      </g>
      <defs>
        <clipPath id="clip0_1167_1367">
          <path fill="#fff" transform="translate(.75 .75)" d="M0 0H22.5V22.5H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ThumbNailIcon() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21 3H3C1.9 3 1 3.9 1 5V19C1 20.1 1.9 21 3 21H21C22.1 21 23 20.1 23 19V5C23 3.9 22.1 3 21 3ZM20 19.01H4C3.45 19.01 3 18.56 3 18.01V5.99C3 5.44 3.45 4.99 4 4.99H20C20.55 4.99 21 5.44 21 5.99V18.01C21 18.56 20.55 19.01 20 19.01Z"
        fill="currentColor"
      />
      <rect x="5.5" y="8.5" width="13" height="7" stroke="currentColor" strokeDasharray="2 2" />
    </svg>
  );
}

export function ForceIcon() {
  return (
    <svg width={25} height={23} viewBox="0 0 25 23" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.02 21.98l6.675-19.845M3.84 13.983l5.548-4.268M10.388 8.49l10.587 4.53M10.304 8.123l9.39-5.989M10.725 8.682l2.196 12.107M2.988 4.44l-.427 9.809M2.749 4.431l7.682 3.841"
        stroke="currentColor"
        strokeWidth={0.6}
      />
      <circle cx={3.85989} cy={4.17043} r={2.58059} />
      <circle cx={2.58059} cy={14.77} r={2.58059} />
      <circle cx={10.9282} cy={8.37747} r={2.58059} />
      <circle cx={13.5923} cy={19.77} r={2.58059} />
      <circle cx={21.4204} cy={13.4634} r={2.58059} />
      <path d="M22.294 2.58a2.58 2.58 0 11-5.161 0 2.58 2.58 0 015.161 0z" />
    </svg>
  );
}

export function RadialIcon() {
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.846 21.572l7.442-5.565M2.608 15.734l10.239 5.487M18.745 4.261l1.05 9.804M9.29 3.592l9.454 2.517M3.25 5.889l-.675 9.768M3.482 6.903l16.456 8.753M2.081 15.79l17.156-9.944M3.059 7.473l6.548-4.07M9.434 3.401l3.502 17.857"
        stroke="currentColor"
        strokeWidth={0.6}
      />
      <circle cx={4.47408} cy={7.2944} r={2.50924} />
      <circle cx={3.4233} cy={15.9897} r={2.50924} />
      <circle cx={10.271} cy={3.74362} r={2.50924} />
      <circle cx={13.8979} cy={21.4917} r={2.50924} />
      <circle cx={20.978} cy={16.0581} r={2.50924} />
      <path d="M22.388 6.246a2.51 2.51 0 11-5.019 0 2.51 2.51 0 015.019 0zM13.575 10.744a2.51 2.51 0 11-5.018 0 2.51 2.51 0 015.018 0z" />
    </svg>
  );
}

export function TreeIcon() {
  return (
    <svg width={22} height={24} viewBox="0 0 22 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9.133 20.58l3.88-7.762M8.272 2.353l5.606 9.375M13.37 12.006l5.04 8.192M2.785 13.341L8.703 2.354"
        stroke="currentColor"
        strokeWidth={0.6}
      />
      <circle cx={3.02327} cy={13.7752} r={2.63069} />
      <circle cx={8.84358} cy={21.369} r={2.63069} />
      <circle cx={8.37874} cy={2.63069} r={2.63069} />
      <circle cx={18.9764} cy={21.1365} r={2.63069} />
      <path d="M16.002 12.05a2.63 2.63 0 11-5.262 0 2.63 2.63 0 015.262 0z" />
    </svg>
  );
}

export function CircleIcon() {
  return (
    <svg width={24} height={24} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.544 21.354l7.367-5.508M3.408 15.575l10.136 5.433M19.382 4.22l1.04 9.704M10.024 3.556l9.358 2.492M4.043 5.829l-.669 9.67M4.274 6.835l16.29 8.665M9.325 3.33L3.086 15.461M2.887 15.632L19.87 5.788M13.037 20.66L3.33 6.45M3.855 7.397l6.482-4.03M10.165 3.368l3.467 17.677"
        stroke="currentColor"
        strokeWidth={0.6}
      />
      <circle cx={3.71531} cy={6.80906} r={2.43015} />
      <circle cx={3.38328} cy={16.1235} r={2.43015} />
      <circle cx={10.1626} cy={4.00047} r={2.43015} />
      <circle cx={13.7524} cy={21.5688} r={2.43015} />
      <circle cx={20.7622} cy={16.1919} r={2.43015} />
      <path d="M22.104 6.477a2.43 2.43 0 11-4.86 0 2.43 2.43 0 014.86 0z" />
    </svg>
  );
}

export function GridIcon() {
  return (
    <svg width={24} height={22} viewBox="0 0 24 22" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2.469 18.348L20.833 2.023M20.086 1.232l.131 16.854M2.606 2.157l9.25.283M2.558 2.366l.904 15.915M2.67 18.223l17.955-.41M11.855 18.347V1.616M2.296 2.09l9.559 16.256"
        stroke="currentColor"
        strokeWidth={0.6}
      />
      <circle cx={3.35287} cy={2.81967} r={2.69076} />
      <circle cx={3.35287} cy={18.4662} r={2.69076} />
      <circle cx={12.3314} cy={2.81967} r={2.69076} />
      <circle cx={12.2787} cy={18.4662} r={2.69076} />
      <circle cx={21.2572} cy={18.4662} r={2.69076} />
      <path d="M23.999 2.82a2.69 2.69 0 11-5.382 0 2.69 2.69 0 015.382 0z" />
    </svg>
  );
}

export function LayerIcon() {
  return (
    <svg width={30} height={26} viewBox="0 0 30 26" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10.582 22.754l2.834-8.03M14.059 2.922l10.863 10.627M14.06 2.685v10.863M14.362 13.634l4.723 8.647M3.195 13.077l10.864-9.92M3.025 22.28l10.864-9.446M13.89 12.879L27 22"
        stroke="currentColor"
        strokeWidth={0.6}
      />
      <circle cx={3.83398} cy={22.5488} r={3} />
      <circle cx={3.36133} cy={13} r={3} />
      <path d="M29.639 22.549a3 3 0 11-6 0 3 3 0 016 0z" />
      <circle cx={11.5234} cy={22.5488} r={3} />
      <path d="M27 13a3 3 0 11-6 0 3 3 0 016 0z" />
      <circle cx={14.2852} cy={3.45117} r={3} />
      <circle cx={19.082} cy={22.5488} r={3} />
      <path d="M17.285 13a3 3 0 11-6 0 3 3 0 016 0z" />
    </svg>
  );
}
