import { ForwardedRef, useImperativeHandle } from 'react';
import { Core } from 'cytoscape';
import { CytoscapeExtensions, GraphRef } from '../type';
import { Props } from '..';
import { usePopper } from '../popper/usePopper';
import { useGraphCenter } from './useGraphCenter';
import { useGraphClickEvents } from './useGraphClickEvents';
import { useGraphHighlightedLabel } from './useGraphHighlightedLabel';
import { useGraphPositionChange } from './useGraphPositionChange';
import { useGraphSelection } from './useGraphSelection';
import { useGraphStyles } from './useGraphStyles';
import { useGraphExport } from './useGraphExport';
import { useEdgeHandles } from './useEdgeHandles';

export const useGraphEvents = (
  cy: (Core & CytoscapeExtensions) | null,
  ref: ForwardedRef<GraphRef>,
  props: Props,
  runLayout: (extraParams?: object, onLayoutStop?: () => void) => void,
) => {
  useGraphClickEvents(cy, props);
  const graphSelectionFunctions = useGraphSelection(cy, props);
  const graphCenter = useGraphCenter(cy, props);
  const graphPositions = useGraphPositionChange(cy, props);
  const graphHighlightedLabel = useGraphHighlightedLabel(cy);
  const graphPopper = usePopper(cy, props);
  const graphStyles = useGraphStyles(cy);
  const graphExport = useGraphExport(cy);
  const edgeHandles = useEdgeHandles(cy, props);

  // Expose methods to the ref
  useImperativeHandle(
    ref,
    (): GraphRef => ({
      ...graphSelectionFunctions,
      ...graphCenter,
      ...graphPositions,
      ...graphHighlightedLabel,
      ...graphPopper,
      ...graphStyles,
      ...graphExport,
      ...edgeHandles,
      runLayout,
    })
  );
};
