import { ExternalGraph, ExternalNode, ExternalLink } from '@tigergraph/models/gvis/insights';
import { Core, CollectionReturnValue } from 'cytoscape';
import { CytoscapeExtensions, GraphCenter } from '../type';
import { Props } from '..';
import { getCytocapeLinks, getCytocapeNodes } from './util';

export const useGraphCenter = (cy: (Core & CytoscapeExtensions) | null, props: Props): GraphCenter => {
  const fitViewWithAnimation = (elements?: CollectionReturnValue) => {
    if (cy) {
      cy.minZoom(0.1);
      cy.maxZoom(2);
      cy.animate(
        {
          fit: {
            eles: elements ? elements : '*',
            padding: elements ? 80 : 50,
          },
        },
        {
          duration: 500,
        }
      );
      cy.maxZoom(10);
      cy.minZoom(0.01);
    }
  };

  const centerGraph = (graph?: ExternalGraph) => {
    if (cy) {
      // Center the graph elements
      if (graph) {
        const nodes = getCytocapeNodes(cy, graph.nodes);
        const links = getCytocapeLinks(cy, graph.links);
        let elements = cy.collection();
        if (nodes) {
          elements = elements.add(nodes);
        }
        if (links) {
          elements = elements.add(links);
        }
        fitViewWithAnimation(elements);
        // Center the entire graph
      } else {
        fitViewWithAnimation();
      }
    }
  };

  const centerNodes = (nodes: ExternalNode[]) => {
    if (cy) {
      const elements = getCytocapeNodes(cy, nodes);
      fitViewWithAnimation(elements);
    }
  };

  const centerLinks = (links: ExternalLink[]) => {
    if (cy) {
      const elements = getCytocapeLinks(cy, links);
      fitViewWithAnimation(elements);
    }
  };

  const centerNodesByTypes = (types: string[]) => {
    if (cy) {
      let elements = cy.collection();
      types.forEach(type => {
        const nodes = cy.elements(`node[type='${type}']`);
        elements = elements.add(nodes);
      });
      fitViewWithAnimation(elements);
    }
  };

  const centerLinksByTypes = (types: string[]) => {
    if (cy) {
      let elements = cy.collection();
      types.forEach(type => {
        const edges = cy.elements(`edge[type='${type}']`);
        elements = elements.add(edges);
      });
      fitViewWithAnimation(elements);
    }
  };

  return {
    centerGraph,
    centerNodes,
    centerLinks,
    centerNodesByTypes,
    centerLinksByTypes,
  };
};
