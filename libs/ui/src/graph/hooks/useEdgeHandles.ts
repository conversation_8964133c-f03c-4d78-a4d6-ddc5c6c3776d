import { useEffect, useRef } from 'react';
import { Core, EventObject, NodeSingular, EdgeSingular } from 'cytoscape';
import { CytoscapeExtensions } from '../type';
import { Props } from '..';
import { useDataTransform } from './useDataTransform';
import { EdgeHandlesOptions, EdgeHandlesInstance } from 'cytoscape-edgehandles';
import { ExternalLink, ExternalNode } from '@tigergraph/models';
import { dataSourceVertexTypes, dataSourceEdgeType } from '../stylesheet';
import { Instance } from '@popperjs/core';
import ReactDOM from 'react-dom';

export const useEdgeHandles = (cy: (Core & CytoscapeExtensions) | null, props: Props) => {
  const { onCreateLink, onCreateLinkCancelled, showEdgeHandler, showGhostNode = false } = props;
  const { getExternalElement } = useDataTransform(props);
  const ehRef = useRef<EdgeHandlesInstance>();
  const popperRef = useRef<Instance>();
  const enableRef = useRef(false);
  const removeListenersRef = useRef<{ [key: string]: () => void } | null>(null);

  useEffect(() => {
    if (cy) {
      const onCyCreateEdge = (e: EventObject, ...params: any) => {
        const [sourceNode, targetNode, addedEdge]: [
          sourceNode: NodeSingular,
          targetNode: NodeSingular,
          addedEdge: EdgeSingular
        ] = params;
        if (onCreateLink) {
          const source = getExternalElement(sourceNode) as ExternalNode;
          const target = getExternalElement(targetNode) as ExternalNode | ExternalLink;
          cy.remove(addedEdge);
          onCreateLink(source, target);
        }
      };

      const onCyCreateLinkCancelled = (e: EventObject, sourceNode: NodeSingular) => {
        if (onCreateLinkCancelled) {
          onCreateLinkCancelled(getExternalElement(sourceNode) as ExternalNode, e.position);
        }
      };

      cy.on('ehcomplete', onCyCreateEdge);
      cy.on('ehcancel', onCyCreateLinkCancelled);

      return () => {
        cy.off('ehcomplete', onCyCreateEdge);
        cy.off('ehcancel', onCyCreateLinkCancelled);
      };
    }
  }, [cy, getExternalElement, onCreateLinkCancelled, onCreateLink]);

  const edgeHandlesOptions: EdgeHandlesOptions = {
    canConnect: (sourceNode: NodeSingular, targetNode: NodeSingular) => {
      // Avoid linking to data source
      if (targetNode.data('type') in dataSourceVertexTypes) {
        return false;
      }
      // Avoid linking to data source edge
      return !(targetNode.hasClass('aux-node') && targetNode.data('edgeId').startsWith(dataSourceEdgeType));
    },
    snap: true,
    snapThreshold: 20,
    noEdgeEventsInDraw: false,
    disableBrowserGestures: false,
  };

  const setDrawMode = (enable: boolean) => {
    if (cy) {
      const eh = ehRef.current || cy.edgehandles(edgeHandlesOptions);
      ehRef.current = eh;

      if (enableRef.current === enable) {
        return;
      }
      enableRef.current = enable;
      if (enable) {
        cy.edges().addClass('edge-handles');
        cy.nodes('.aux-node').addClass('aux-node-handles');
        const removeListeners = createHandle(eh);
        if (removeListeners) {
          removeListenersRef.current = { removeListeners };
        }
      } else {
        popperRef.current?.state.elements.popper.remove();
        popperRef.current?.destroy();
        cy.edges().removeClass('edge-handles');
        cy.nodes('.aux-node').removeClass('aux-node-handles');
        removeListenersRef.current?.removeListeners();
        removeListenersRef.current = null;
      }
    }
  };

  const createHandle = (eh: EdgeHandlesInstance) => {
    if (!cy || !eh || popperRef.current) {
      return;
    }
    let popperNode: NodeSingular | undefined;
    let popper: Instance | undefined;
    let popperDiv: HTMLElement | undefined;
    let started = false;

    const start = (event: MouseEvent) => {
      // @ts-ignore
      eh.start(popperNode);
    };

    const stop = () => {
      eh.stop();
    };

    const setActiveBgOpacity = (opacity: number) => {
      cy.style()
        .selector('core')
        .style({
          // @ts-ignore
          'active-bg-opacity': opacity,
        })
        .update();
    };

    const setHandleOn = (node: NodeSingular) => {
      if (started || !enableRef.current) {
        return;
      }

      removeHandle(); // rm old handle

      popperNode = node;
      const width = Math.floor(node.renderedOuterWidth() - 1);
      const height = Math.floor(node.renderedOuterHeight() - 1);
      const isDataSource = node.data('type') in dataSourceVertexTypes;
      const borderWidth = isDataSource ? 1 : 0.6;

      popper = node.popper({
        content: () => {
          popperDiv = document.createElement('div');
          popperDiv.style['width'] = `${width}px`;
          popperDiv.style['height'] = `${height}px`;
          popperDiv.style['background'] = 'transparent';
          if (!isDataSource) {
            popperDiv.style['borderRadius'] = '50%';
            popperDiv.style['cursor'] = 'ne-resize';
          } else {
            popperDiv.style['borderRadius'] = '10%';
            popperDiv.style['cursor'] = 'crosshair';
          }
          popperDiv.style['border'] = `rgba(128,128,128,0.5) ${width * borderWidth}px solid`;
          popperDiv.addEventListener('mousedown', start);
          popperDiv.addEventListener('mouseleave', removeHandle);

          const childDiv = document.createElement('div');
          childDiv.style['width'] = `${width}px`;
          childDiv.style['height'] = `${height}px`;
          childDiv.style['background'] = 'transparent';
          if (!isDataSource) {
            childDiv.style['borderRadius'] = '50%';
          } else {
            childDiv.style['borderRadius'] = '10%';
          }
          childDiv.style['cursor'] = 'move';
          popperDiv.appendChild(childDiv);

          cy?.container()?.appendChild(popperDiv);
          return popperDiv;
        },
        popper: {
          placement: 'top',
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, (-(width + height) / 2) * (borderWidth + 1)],
              },
            },
            {
              name: 'preventOverflow',
              enabled: false,
            },
          ],
          strategy: 'absolute',
        },
      });
      popperRef.current = popper;

      const remove = () => {
        node.off('position');
        node.off('remove');
        removeHandle();
        popperDiv?.remove();
      };

      node.on('remove', remove);
      node.on('position', () => {
        popper?.update();
      });
    };

    const removeHandle = () => {
      if (popperDiv) {
        ReactDOM.unmountComponentAtNode(popperDiv);
        popperDiv.remove();
        popperDiv = undefined;
      }

      if (popper) {
        popper.state.elements.popper.remove();
        popper.destroy();
        popper = undefined;
      }

      popperRef.current = undefined;
      popperNode = undefined;
    };

    const mouseover = (e: EventObject) => {
      const node = e.target;
      const exNode = {
        type: node.data('type'),
        id: node.data('nodeID'),
        others: node.data('__others'),
      };
      if (!showEdgeHandler || showEdgeHandler(exNode as ExternalNode)) {
        setTimeout(() => {
          setHandleOn(node);
        });
      }
    };

    const grab = () => {
      stop();
      removeHandle();
    };

    const zoomPan = () => {
      removeHandle();
    };

    const mouseup = () => {
      stop();
    };

    const ehstart = () => {
      setActiveBgOpacity(0);
      started = true;
      showGhostNode &&
        setTimeout(() => {
          cy.elements('.eh-ghost-node').style({
            width: 80,
            height: 80,
            backgroundColor: '#fff',
            borderRadius: '50%',
            borderWidth: 3,
            borderStyle: 'dashed',
            borderColor: '#ccc',
            fontSize: 0,
            opacity: 1,
          });
        }, 200);
    };

    const ehstop = () => {
      setActiveBgOpacity(0.15);
      started = false;
    };

    const ehhoverover = () => {
      showGhostNode &&
        cy.elements('.eh-ghost-node').style({
          width: 0,
          height: 0,
          opacity: 0,
        });
    };

    const ehhoverout = () => {
      showGhostNode &&
        cy.elements('.eh-ghost-node').style({
          width: 80,
          height: 80,
          backgroundColor: '#fff',
          borderRadius: '50%',
          borderWidth: 3,
          borderStyle: 'dashed',
          borderColor: '#ccc',
          fontSize: 0,
          opacity: 1,
        });
    };

    const removeListeners = () => {
      window.removeEventListener('mouseup', mouseup);
      cy.off('mouseover', 'node', mouseover);
      cy.off('grab', 'node', grab);
      cy.off('zoom pan', zoomPan);
      cy.off('ehstart', ehstart);
      cy.off('ehstop', ehstop);
      cy.off('ehhoverover', ehhoverover);
      cy.off('ehhoverout', ehhoverout);
      removeHandle();
    };

    window.addEventListener('mouseup', mouseup);
    cy.on('mouseover', 'node', mouseover);
    cy.on('grab', 'node', grab);
    cy.on('zoom pan', zoomPan);
    cy.on('ehstart', ehstart);
    cy.on('ehstop', ehstop);
    cy.on('ehhoverover', ehhoverover);
    cy.on('ehhoverout', ehhoverout);

    return removeListeners;
  };

  return {
    setDrawMode,
  };
};
