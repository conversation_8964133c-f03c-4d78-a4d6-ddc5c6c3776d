import { useEffect } from 'react';
import { Core, EventObject } from 'cytoscape';
import { CytoscapeExtensions } from '../type';
import { Props } from '..';
import { useDataTransform } from './useDataTransform';

export const useGraphClickEvents = (cy: (Core & CytoscapeExtensions) | null, props: Props) => {
  const { onClick, onClickDown, onDoubleClick, onSelect, onSelectNodes, onSelectEdges, onMouseOver, onMouseOut } =
    props;

  const { getExternalElement, getExternalNodes, getExternalLinks, getExternalGraph } = useDataTransform(props);

  useEffect(() => {
    if (cy) {
      const onCyClick = (e: EventObject) => {
        if (onClick) {
          onClick(getExternalElement(e.target), e.position);
        }
      };
      const onCyClickDown = (e: EventObject) => {
        if (onClickDown) {
          onClickDown(getExternalElement(e.target));
        }
      };
      const onCyDoubleClick = (e: EventObject) => {
        if (onDoubleClick) {
          onDoubleClick(getExternalElement(e.target), e.position);
        }
      };
      const onCySelectNodes = (e: EventObject) => {
        if (onSelectNodes) {
          onSelectNodes(getExternalNodes(e.cy.elements('node:selected')));
        }
      };
      const onCySelectEdges = (e: EventObject) => {
        if (onSelectEdges) {
          onSelectEdges(getExternalLinks(e.cy.elements('edge:selected')));
        }
      };
      const onCySelect = (e: EventObject) => {
        if (onSelect) {
          onSelect(getExternalGraph(e.cy.elements(':selected')));
        }
      };
      const onCyMouseOver = (e: EventObject) => {
        if (onMouseOver) {
          const element = getExternalElement(e.target);
          if (element) {
            onMouseOver(element);
          }
        }
      };

      const onCyMouseOut = (e: EventObject) => {
        if (onMouseOut) {
          const element = getExternalElement(e.target);
          if (element) {
            onMouseOut(element);
          }
        }
      };

      cy.on('tap', onCyClick);
      cy.on('tapstart', onCyClickDown);
      cy.on('dbltap', onCyDoubleClick);
      cy.on('select unselect', 'node', onCySelectNodes);
      cy.on('select unselect', 'edge', onCySelectEdges);
      cy.on('select unselect', onCySelect);
      cy.on('mouseover', onCyMouseOver);
      cy.on('mouseout', onCyMouseOut);

      return () => {
        cy.off('tap', onCyClick);
        cy.off('tapstart', onCyClickDown);
        cy.off('dbltap', onCyDoubleClick);
        cy.off('select unselect', 'node', onCySelectNodes);
        cy.off('select unselect', 'edge', onCySelectEdges);
        cy.off('select unselect', onCySelect);
        cy.off('mouseover', onCyMouseOver);
        cy.off('mouseout', onCyMouseOut);
      };
    }
  }, [
    cy,
    getExternalElement,
    getExternalGraph,
    getExternalLinks,
    getExternalNodes,
    onClick,
    onClickDown,
    onDoubleClick,
    onSelect,
    onSelectEdges,
    onSelectNodes,
    onMouseOver,
    onMouseOut,
  ]);
};
