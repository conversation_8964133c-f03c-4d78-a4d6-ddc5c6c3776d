import { useEffect } from 'react';
import { ExternalNode } from '@tigergraph/models/gvis/insights';
import { Core, NodeCollection } from 'cytoscape';
import { CytoscapeExtensions, GraphPositions } from '../type';
import { Props } from '..';
import { getCytocapeNodes } from './util';

export const useGraphPositionChange = (cy: (Core & CytoscapeExtensions) | null, props: Props): GraphPositions => {
  const { onPositionChange } = props;

  const getPositions = (nodes: NodeCollection): [string, number, number][] => {
    const positions: [string, number, number][] = [];
    nodes.forEach((node) => {
      const position = node.position();
      // Skip the parent node position when highlighting a node with label
      if (node.data().type) {
        positions.push([node.data().id, position.x, position.y]);
      }
    });
    return positions;
  };

  useEffect(() => {
    if (cy) {
      const onCyPositionChange = () => {
        if (onPositionChange) {
          onPositionChange();
        }
      };
      cy.on('dragfreeon', onCyPositionChange);
      return () => {
        cy.off('dragfreeon', onCyPositionChange);
      };
    }
  }, [cy, onPositionChange]);

  const graphPositions = (nodes?: ExternalNode[]) => {
    if (cy) {
      if (nodes) {
        const cyNodes = getCytocapeNodes(cy, nodes);
        if (cyNodes) {
          return getPositions(cyNodes);
        }
      }
      return getPositions(cy.nodes());
    }
    return [];
  };

  const lockNodes = (nodes?: ExternalNode[]) => {
    if (cy) {
      if (nodes) {
        getCytocapeNodes(cy, nodes)?.lock();
      } else {
        cy.elements().lock();
      }
    }
  };

  const unlockNodes = (nodes?: ExternalNode[]) => {
    if (cy) {
      if (nodes) {
        getCytocapeNodes(cy, nodes)?.unlock();
      } else {
        cy.elements().unlock();
      }
    }
  };

  const setPositions = (positions: [string, number, number][]) => {
    if (cy) {
      cy.batch(() => {
        positions.forEach(p => {
          const node = cy.elements(`node[id='${p[0]}']`);
          node?.position({
            x: p[1],
            y: p[2],
          });
        });
      });
    }
  };

  return {
    graphPositions,
    lockNodes,
    unlockNodes,
    setPositions,
  };
};
