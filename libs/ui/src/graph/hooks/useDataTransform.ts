import { useCallback } from 'react';
import { ExternalGraph, ExternalNode, ExternalLink, getLinkID, getNodeID } from '@tigergraph/models/gvis/insights';
import { NodeSingular, CollectionReturnValue, EdgeSingular } from 'cytoscape';
import { Props } from '..';

export const useDataTransform = (props: Props) => {
  const { graph } = props;

  const getExternalElement = useCallback(
    (evtTarget: NodeSingular | EdgeSingular): ExternalNode | ExternalLink | undefined => {
      try {
        if (evtTarget.hasClass('aux-node')) {
          return graph.links.find((link) => getLinkID(link) === evtTarget.data('edgeId'));
        } else if (evtTarget.isNode()) {
          return graph.nodes.find((node) => `${getNodeID(node)}` === evtTarget.id());
        } else if (evtTarget.isEdge()) {
          return graph.links.find((link) => getLinkID(link) === evtTarget.id());
        }
      } catch (error) {
        // when click background, the evtTarget is Core
        return undefined;
      }
    },
    [graph]
  );

  const getExternalNodes = useCallback(
    (collection: CollectionReturnValue): ExternalNode[] => {
      const nodeMap: Record<string, ExternalNode> = {};
      graph.nodes.forEach((node) => {
        nodeMap[`${getNodeID(node)}`] = node;
      });

      return collection.reduce((prev, n) => {
        if (n.isNode() && n.id() in nodeMap) {
          prev.push(nodeMap[n.id()]);
        }
        return prev;
      }, [] as ExternalNode[]);
    },
    [graph]
  );

  const getExternalLinks = useCallback(
    (collection: CollectionReturnValue): ExternalLink[] => {
      const linkMap: Record<string, ExternalLink> = {};
      graph.links.forEach((link) => {
        linkMap[getLinkID(link)] = link;
      });

      return collection.reduce((prev, l) => {
        if (l.isEdge() && l.id() in linkMap) {
          prev.push(linkMap[l.id()]);
        }
        return prev;
      }, [] as ExternalLink[]);
    },
    [graph]
  );

  const getExternalGraph = useCallback(
    (collection: CollectionReturnValue): ExternalGraph => {
      return {
        nodes: getExternalNodes(collection),
        links: getExternalLinks(collection),
      };
    },
    [getExternalLinks, getExternalNodes]
  );

  return {
    getExternalElement,
    getExternalNodes,
    getExternalLinks,
    getExternalGraph,
  };
};
