import { ExternalNode, getNodeID } from '@tigergraph/models/gvis/insights';
import { Core } from 'cytoscape';
import { CytoscapeExtensions, GraphHighlightedLabel } from '../type';
import { getCytocapeNode } from './util';

export const useGraphHighlightedLabel = (cy: (Core & CytoscapeExtensions) | null): GraphHighlightedLabel => {
  const highlightLabel = (node: ExternalNode, label: string) => {
    if (cy) {
      const element = getCytocapeNode(cy, node);
      if (element) {
        const parent = element.parent();
        if (parent?.data('label') === label) {
          return;
        }
        removeHighlightedLabel(node);
        cy.add({
          data: { id: `__parent_${getNodeID(node)}`, nodeID: `__parent_${getNodeID(node)}`, label },
        });
        // Move the node to the parent node
        element.move({ parent: `__parent_${getNodeID(node)}` });
      }
    }
  };

  const removeHighlightedLabel = (node: ExternalNode) => {
    if (cy) {
      const element = getCytocapeNode(cy, node);
      if (element) {
        const parent = element.parent();
        // Move the element out of the parent node
        element.move({ parent: null });
        // Remove the parent node
        cy.remove(parent);
      }
    }
  };

  return { highlightLabel, removeHighlightedLabel };
};
