import { Core, ExportStringOptions } from 'cytoscape';
import { CytoscapeExtensions, GraphExport } from '../type';

export const useGraphExport = (cy: (Core & CytoscapeExtensions) | null): GraphExport => {

  // Export base64uri
  const exportPng = (width?: number, height?: number) => {
    if (cy) {
      const options: ExportStringOptions = {
        bg: 'white',
      };
      if (width) {
        options['maxWidth'] = width;
      }
      if (height) {
        options['maxHeight'] = height;
      }
      return cy.png(options);
    }
    return '';
  };

  return {
    exportPng,
  };
};
