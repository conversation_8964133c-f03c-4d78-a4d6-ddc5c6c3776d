import { ExternalGraph, ExternalNode, ExternalLink, getLinkID, getNodeID } from '@tigergraph/models/gvis/insights';
import { Core, CollectionReturnValue } from 'cytoscape';
import { CytoscapeExtensions } from '../type';

export const getCytocapeNode = (
  cy: (Core & CytoscapeExtensions) | null,
  node: ExternalNode
): CollectionReturnValue | undefined => {
  if (cy) {
    const element = cy.getElementById(`${getNodeID(node)}`);
    return element;
  }
};

export const getCytocapeNodes = (
  cy: (Core & CytoscapeExtensions) | null,
  nodes: ExternalNode[]
): CollectionReturnValue | undefined => {
  if (cy) {
    let elements = cy.collection();
    for (let node of nodes) {
      elements = elements.add(`node[id='${getNodeID(node)}']`);
    }
    return elements;
  }
};

export const getCytocapeLinks = (
  cy: (Core & CytoscapeExtensions) | null,
  links: ExternalLink[]
): CollectionReturnValue | undefined => {
  if (cy) {
    let elements = cy.collection();
    for (let link of links) {
      elements = elements.add(`edge[id='${getLinkID(link)}']`);
    }
    return elements;
  }
};

export const getIdsFromExternalNodes = (nodes: ExternalNode[]): string[] => {
  return nodes.map((n) => `${getNodeID(n)}`);
};

export const getIdsFromExternalLinks = (links: ExternalLink[]): string[] => {
  return links.map((l) => getLinkID(l));
};

export const getIdsFromExternalGraph = (graph: ExternalGraph): string[] => {
  return [...getIdsFromExternalNodes(graph.nodes), ...getIdsFromExternalLinks(graph.links)];
};
