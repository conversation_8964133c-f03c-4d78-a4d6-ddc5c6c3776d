import { ExternalGraph, ExternalNode, ExternalLink } from '@tigergraph/models/gvis/insights';
import { Core, CollectionReturnValue } from 'cytoscape';
import { CytoscapeExtensions, GraphSelections } from '../type';
import { Props } from '..';
import { useDataTransform } from './useDataTransform';
import { getCytocapeNodes, getCytocapeLinks } from './util';

export const useGraphSelection = (cy: (Core & CytoscapeExtensions) | null, props: Props): GraphSelections => {
  const { getExternalNodes, getExternalLinks, getExternalGraph } = useDataTransform(props);

  const selectElements = (graph: ExternalGraph) => {
    if (cy) {
      const cyNodes = getCytocapeNodes(cy, graph.nodes);
      const cyLinks = getCytocapeLinks(cy, graph.links);
      cyNodes?.select();
      cyLinks?.select();
    }
  };

  const selectNodes = (nodes: ExternalNode[]) => {
    if (cy) {
      const cyNodes = getCytocapeNodes(cy, nodes);
      cyNodes?.select();
    }
  };

  const selectNodesByTypes = (nodeTypes: string[]) => {
    if (cy) {
      let eles: CollectionReturnValue = cy.collection();
      for (let type of nodeTypes) {
        const elements = cy.nodes(`[type = "${type}"]`);
        eles = eles.union(elements);
      }
      eles.select();
    }
  };

  const selectEdges = (links: ExternalLink[]) => {
    if (cy) {
      const cyLinks = getCytocapeLinks(cy, links);
      cyLinks?.select();
    }
  };

  const selectEdgesByTypes = (edgeTypes: string[]) => {
    if (cy) {
      let eles: CollectionReturnValue = cy.collection();
      for (let type of edgeTypes) {
        const elements = cy.edges(`[type = "${type}"]`);
        eles = eles.union(elements);
      }
      eles.select();
    }
  };

  const unselectElements = (graph?: ExternalGraph) => {
    if (cy) {
      if (graph) {
        const cyNodes = getCytocapeNodes(cy, graph.nodes);
        const cyLinks = getCytocapeLinks(cy, graph.links);
        cyNodes?.unselect();
        cyLinks?.unselect();
      } else {
        cy.elements().unselect();
      }
    }
  };

  const unselectNodes = (nodes?: ExternalNode[]) => {
    if (cy) {
      if (nodes) {
        const cyNodes = getCytocapeNodes(cy, nodes);
        cyNodes?.unselect();
      } else {
        cy.nodes().unselect()
      }
    }
  };

  const unselectEdges = (links?: ExternalLink[]) => {
    if (cy) {
      if (links) {
        const cyLinks = getCytocapeLinks(cy, links);
        cyLinks?.unselect();
      } else {
        cy.edges().unselect();
      }
    }
  };

  const selectedElements = (): ExternalGraph => {
    if (cy) {
      return getExternalGraph(cy.elements(':selected'));
    }
    return { nodes: [], links: [] };
  };

  const selectedNodes = (): ExternalNode[] => {
    if (cy) {
      return getExternalNodes(cy.elements('node:selected'));
    }
    return [];
  };

  const selectedEdges = (): ExternalLink[] => {
    if (cy) {
      return getExternalLinks(cy.elements('edge:selected'));
    }
    return [];
  };

  const getNodes = (): ExternalNode[] => {
    if (cy) {
      return getExternalNodes(cy.elements('node'));
    }
    return [];
  };

  const getNodesByTypes = (types: string[]): ExternalNode[] => {
    if (cy) {
      let elements = cy.collection();
      types.forEach(type => {
        const nodes = cy.elements(`node[type='${type}']`);
        elements = elements.add(nodes);
      });
      return getExternalNodes(elements);
    }
    return [];
  };

  const getLinks = (): ExternalLink[] => {
    if (cy) {
      return getExternalLinks(cy.elements('edge'));
    }
    return [];
  };

  const getLinksByTypes = (types: string[]): ExternalLink[] => {
    if (cy) {
      let elements = cy.collection();
      types.forEach(type => {
        const links = cy.elements(`edge[type='${type}']`);
        elements = elements.add(links);
      });
      return getExternalLinks(elements);
    }
    return [];
  };

  return {
    selectElements,
    selectNodes,
    selectNodesByTypes,
    selectEdges,
    selectEdgesByTypes,
    unselectElements,
    unselectNodes,
    unselectEdges,
    selectedElements,
    selectedNodes,
    selectedEdges,
    getNodes,
    getNodesByTypes,
    getLinks,
    getLinksByTypes,
  };
};
