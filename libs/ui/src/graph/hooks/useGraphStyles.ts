import { ExternalNode, ExternalLink, getNodeID, getLinkID } from '@tigergraph/models/gvis/insights';
import { Core } from 'cytoscape';
import { CytoscapeExtensions, GraphStyles } from '../type';

export const useGraphStyles = (cy: (Core & CytoscapeExtensions) | null): GraphStyles => {

  const setNodeStyle = (node: ExternalNode, style: Record<string, string | number | Function>) => {
    if (cy) {
      try {
        const cyNode = cy.elements(`node[id='${getNodeID(node)}']`);
        cyNode?.style(style);
      } catch (e) {
        console.error(e);
      }
    }
  };

  const setNodesStyle = (data: { node: ExternalNode, style: Record<string, string | number | Function> }[]) => {
    if (cy) {
      cy.batch(() => {
        data.forEach(({ node, style }) => {
          setNodeStyle(node, style);
        });
      });
    }
  };

  const setNodesStyleByType = (type: string, style: Record<string, string | number | Function>) => {
    if (cy) {
      cy.batch(() => {
        try {
          const eles = cy.elements(`node[type='${type}']`);
          eles?.style(style);
        } catch (e) {
          console.error(e);
        }
      });
    }
  };

  const setNodesStyleByTypes = (data: { type: string, style: Record<string, string | number | Function> }[]) => {
    if (cy) {
      cy.batch(() => {
        data.forEach(({ type, style }) => {
          setNodesStyleByType(type, style);
        });
      });
    }
  };

  const setLinkStyle = (link: ExternalLink, style: Record<string, string | number | Function>) => {
    if (cy) {
      try {
        const cyLink = cy.elements(`edge[id='${getLinkID(link)}']`);
        cyLink?.style(style);
      } catch (e) {
        console.error(e);
      }
    }
  };

  const setLinksStyle = (data: { link: ExternalLink, style: Record<string, string | number | Function> }[]) => {
    if (cy) {
      cy.batch(() => {
        data.forEach(({ link, style }) => {
          setLinkStyle(link, style);
        });
      });
    }
  };

  const setLinksStyleByType = (type: string, style: Record<string, string | number | Function>) => {
    if (cy) {
      cy.batch(() => {
        try {
          const eles = cy.elements(`edge[type='${type}']`);
          eles?.style(style);
        } catch (e) {
          console.error(e);
        }
      });
    }
  };

  const setLinksStyleByTypes = (data: { type: string, style: Record<string, string | number | Function> }[]) => {
    if (cy) {
      cy.batch(() => {
        data.forEach(({ type, style }) => {
          setLinksStyleByType(type, style);
        });
      });
    }
  };

  return {
    setNodeStyle,
    setNodesStyle,
    setNodesStyleByType,
    setNodesStyleByTypes,
    setLinkStyle,
    setLinksStyle,
    setLinksStyleByType,
    setLinksStyleByTypes,
  };
};
