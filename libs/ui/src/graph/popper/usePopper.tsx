import { Core } from 'cytoscape';
import { CytoscapeExtensions, GraphIcons, GraphMessages } from '../type';
import { useGraphMessagePopper } from './useGraphMessagePopper';
import { useGraphIconPopper } from './useGraphIconPopper';
import { Props } from '..';

export const usePopper = (cy: (Core & CytoscapeExtensions) | null, props: Props): GraphMessages & GraphIcons => {
  const graphMessagePopper = useGraphMessagePopper(cy);
  const graphIconPopper = useGraphIconPopper(cy, props);

  return {
    ...graphMessagePopper,
    ...graphIconPopper,
  };
};
