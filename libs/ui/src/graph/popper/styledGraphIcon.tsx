import React from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { GRAPH_ICON } from './useGraphIconPopper';
import { ReactComponent as GlobalIcon } from '../icons/global-icon.svg';
import { ReactComponent as DeleteIcon } from '../icons/cross-circle.svg';

export interface StyledGraphIconProps {
  icon: (typeof GRAPH_ICON)[keyof typeof GRAPH_ICON];
  onClick?: () => void;
}

const StyledGraphIcon = ({ icon, onClick }: StyledGraphIconProps) => {
  const [css, theme] = useStyletron();

  const buildIcon = () => {
    if (icon === GRAPH_ICON.delete) {
      return <DeleteIcon fill={theme.colors.primary1000} style={{ height: '20px', width: '20px' }} />;
    } else if (icon === GRAPH_ICON.global) {
      return (
        <GlobalIcon
          fill={theme.colors.primary1000}
          stroke="#fff"
          style={{ height: '20px', width: '20px' }}
          viewBox="0 0 85 85"
        />
      );
    }
  };

  return (
    <div
      className={css({
        transform: 'translateY(50%)',
        cursor: icon === GRAPH_ICON.delete ? 'pointer' : 'default',
        width: '20px',
        height: '20px',
      })}
      onClick={onClick}
    >
      {buildIcon()}
    </div>
  );
};

export default StyledGraphIcon;
