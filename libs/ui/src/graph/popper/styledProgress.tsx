import React from 'react';
import { ProgressBar, ProgressBarProps } from 'baseui/progress-bar';

const StyledProgress = (props: ProgressBarProps) => {

  return (
    //@ts-ignore
    <ProgressBar
      successValue={100}
      overrides={{
        Root: {
          style: {
            width: '80px',
          },
        },
        Bar: {
          style: {
            height: '10px',
          },
        },
        BarContainer: {
          style: {
            marginTop: '0',
            marginBottom: '0',
          },
        },
        BarProgress: {
          style: ({ $theme }) => ({
            backgroundColor: $theme.colors.secondary800,
          })
        },
        InfiniteBar: {
          style: {
            height: '10px',
          },
        },
      }}
      {...props}
    />
  );
};

export default StyledProgress;
