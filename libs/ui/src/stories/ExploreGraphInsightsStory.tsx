import React, { useMemo, useRef, useState } from 'react';
import { Core } from 'cytoscape';
import { parseGraph } from '@tigergraph/models/gvis/insights';

import { CytoscapeExtensions, Schema, GraphRef, SettingType } from '../graph/type';
import Graph from '../graph';

import patternJson from './data/patter1.json';
import { getSchema } from './util';

export const ExploreGraphInsightsStory = () => {
  const graphRef = useRef<GraphRef>(null);
  const cyRef = useRef<(Core & CytoscapeExtensions) | null>(null);

  let schema: Schema = useMemo(() => {
    return getSchema();
  }, []);

  const [settings, setSettings] = useState<SettingType>({
    layout: 'Force',
    edgeLength: 200,
    showUndoRedo: true,
    actionsByType: {
      BankAccount: [
        {
          url: 'http://www.google.com',
          text: 'google',
          urlName: 'google',
          params: [],
        },
      ],
    },
    rulesByType: {
      // check conditional styling is working expected.
      MoneyTransfer: [
        {
          fieldType: 'number',
          fieldName: 'amount',
          condition: 'range',
          conditionValue: '',
          conditionStartValue: 10,
          conditionEndValue: 10000,
          styleKey: 'node-radius',
          styleType: 'numeric',
          styleLabel: 'Vertex size',
          styleValue: '',
          palateName: '',
          styleStartValue: '1',
          styleStartLabel: '',
          styleEndValue: '2',
          styleEndLabel: '',
        },
      ],
    },
  });

  const [graph, setGraph] = useState(() => {
    let graph = parseGraph(patternJson);
    return graph;
  });

  return (
    <div style={{ height: 'calc(100vh - 2em)' }}>
      <Graph
        ref={graphRef}
        parentRef={cyRef}
        schema={schema}
        graph={graph}
        id="explore-insights"
        graphName="NoCodeAntiFraud"
        onGraphChange={setGraph}
        settings={settings}
        onSettingUpdate={(key, value) => {
          setSettings({
            ...settings,
            [key]: value,
          });
        }}
      />
    </div>
  );
};
