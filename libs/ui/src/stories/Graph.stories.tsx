import React from 'react';
import { Meta } from '@storybook/react';
import Provider from '@tigergraph/app-ui-lib/Provider';
import { QueryClient, QueryClientProvider } from 'react-query';

import Graph from '../graph';
import { StyledToasterContainer } from '../styledToasterContainer';

export { SchemaEditStory as SchemaEdit } from './SchemaEditStory';
export { SchemaViewStory as SchemaView } from './SchemaViewStory';
export { ExploreGraphInsightsStory as ExploreGraphInsights } from './ExploreGraphInsightsStory';
export { ExploreGraphGraphStudioStory as ExploreGraphGraphStudio } from './ExploreGraphGraphStudioStory';
export { ExploreGraphLarge } from './ExploreGraphLarge';
export { ExploreGraphMedium } from './ExploreGraphMedium';

const client = new QueryClient({});

export default {
  title: 'Graph',
  component: Graph,
  decorators: [
    (Story) => {
      return (
        <Provider>
          <QueryClientProvider client={client}>
            <Story />
            <StyledToasterContainer />
          </QueryClientProvider>
        </Provider>
      );
    },
  ],
} as Meta;
