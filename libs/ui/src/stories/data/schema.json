{"error": false, "message": "", "results": {"EdgeTypes": [{"Attributes": [{"AttributeName": "send_time", "AttributeType": {"Name": "DATETIME"}}, {"AttributeName": "amount", "AttributeType": {"Name": "DOUBLE"}}], "Config": {}, "FromVertexTypeName": "BankAccount", "IsDirected": true, "IsLocal": true, "Name": "SEND", "ToVertexTypeName": "MoneyTransfer"}, {"Attributes": [{"AttributeName": "receive_time", "AttributeType": {"Name": "DATETIME"}}, {"AttributeName": "amount", "AttributeType": {"Name": "DOUBLE"}}], "Config": {"REVERSE_EDGE": "reverse_RECEIVE"}, "FromVertexTypeName": "MoneyTransfer", "IsDirected": true, "IsLocal": true, "Name": "RECEIVE", "ToVertexTypeName": "BankAccount"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_ISSUED_BY"}, "FromVertexTypeName": "BankAccount", "IsDirected": true, "IsLocal": true, "Name": "ISSUED_BY", "ToVertexTypeName": "FinancialInstitute"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_FROM_IP"}, "FromVertexTypeName": "Purchase", "IsDirected": true, "IsLocal": true, "Name": "FROM_IP", "ToVertexTypeName": "IP"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_WITH_LOGIN"}, "FromVertexTypeName": "Purchase", "IsDirected": true, "IsLocal": true, "Name": "WITH_LOGIN", "ToVertexTypeName": "<PERSON><PERSON>"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_DELIVERED_AT"}, "FromVertexTypeName": "Purchase", "IsDirected": true, "IsLocal": true, "Name": "DELIVERED_AT", "ToVertexTypeName": "Address"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_PAID_BY_CARD"}, "FromVertexTypeName": "Purchase", "IsDirected": true, "IsLocal": true, "Name": "PAID_BY_CARD", "ToVertexTypeName": "CreditCard"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_LOCATED_IN"}, "EdgePairs": [{"From": "Address", "To": "State"}, {"From": "IP", "To": "State"}], "FromVertexTypeName": "*", "IsDirected": true, "IsLocal": true, "Name": "LOCATED_IN", "ToVertexTypeName": "State"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_FOR_SHOP"}, "FromVertexTypeName": "<PERSON><PERSON>", "IsDirected": true, "IsLocal": true, "Name": "FOR_SHOP", "ToVertexTypeName": "Shop"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_HAS_SSN"}, "FromVertexTypeName": "Person", "IsDirected": true, "IsLocal": true, "Name": "HAS_SSN", "ToVertexTypeName": "SSN"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_HAS_CREDIT_CARD"}, "FromVertexTypeName": "Person", "IsDirected": true, "IsLocal": true, "Name": "HAS_CREDIT_CARD", "ToVertexTypeName": "CreditCard"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_HAS_BANK_ACCOUNT"}, "FromVertexTypeName": "Person", "IsDirected": true, "IsLocal": true, "Name": "HAS_BANK_ACCOUNT", "ToVertexTypeName": "BankAccount"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_HAS_UNSECURED_LOAN"}, "FromVertexTypeName": "Person", "IsDirected": true, "IsLocal": true, "Name": "HAS_UNSECURED_LOAN", "ToVertexTypeName": "UnsecuredLoan"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_HAS_PHONE_NUMBER"}, "FromVertexTypeName": "Person", "IsDirected": true, "IsLocal": true, "Name": "HAS_PHONE_NUMBER", "ToVertexTypeName": "PhoneNumber"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_HAS_ADDRESS"}, "FromVertexTypeName": "Person", "IsDirected": true, "IsLocal": true, "Name": "HAS_ADDRESS", "ToVertexTypeName": "Address"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_HAS_ID"}, "FromVertexTypeName": "Person", "IsDirected": true, "IsLocal": true, "Name": "HAS_ID", "ToVertexTypeName": "ID"}, {"Attributes": [], "Config": {"REVERSE_EDGE": "reverse_WITH_PHONE"}, "FromVertexTypeName": "MoneyTransfer", "IsDirected": true, "IsLocal": true, "Name": "WITH_PHONE", "ToVertexTypeName": "PhoneNumber"}], "GraphName": "NoCode<PERSON>nti<PERSON><PERSON>ud", "VertexTypes": [{"Attributes": [{"AttributeName": "open_time", "AttributeType": {"Name": "DATETIME"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "BankAccount", "PrimaryId": {"AttributeName": "account_number", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "applied_time", "AttributeType": {"Name": "DATETIME"}}, {"AttributeName": "credit_line", "AttributeType": {"Name": "DOUBLE"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "CreditCard", "PrimaryId": {"AttributeName": "credit_card_number", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "SSN", "PrimaryId": {"AttributeName": "ssn", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "id_type", "AttributeType": {"Name": "STRING"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "ID", "PrimaryId": {"AttributeName": "id_number", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "zipcode", "AttributeType": {"Name": "STRING"}}, {"AttributeName": "latitude", "AttributeType": {"Name": "DOUBLE"}}, {"AttributeName": "longitude", "AttributeType": {"Name": "DOUBLE"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "Address", "PrimaryId": {"AttributeName": "address", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "applied_time", "AttributeType": {"Name": "DATETIME"}}, {"AttributeName": "amount", "AttributeType": {"Name": "DOUBLE"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "UnsecuredLoan", "PrimaryId": {"AttributeName": "loan_number", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "PhoneNumber", "PrimaryId": {"AttributeName": "number", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "amount", "AttributeType": {"Name": "DOUBLE"}}, {"AttributeName": "transfer_time", "AttributeType": {"Name": "DATETIME"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "MoneyTransfer", "PrimaryId": {"AttributeName": "id", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": false}}, {"Attributes": [], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "FinancialInstitute", "PrimaryId": {"AttributeName": "name", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "purchase_time", "AttributeType": {"Name": "DATETIME"}}, {"AttributeName": "purchase_amount", "AttributeType": {"Name": "DOUBLE"}}, {"AttributeName": "purchase_time_epoch", "AttributeType": {"Name": "INT"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "Purchase", "PrimaryId": {"AttributeName": "id", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "latitude", "AttributeType": {"Name": "DOUBLE"}}, {"AttributeName": "longitude", "AttributeType": {"Name": "DOUBLE"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "IP", "PrimaryId": {"AttributeName": "ip_address", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "latitude", "AttributeType": {"Name": "DOUBLE"}}, {"AttributeName": "longitude", "AttributeType": {"Name": "DOUBLE"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "State", "PrimaryId": {"AttributeName": "state", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "<PERSON><PERSON>", "PrimaryId": {"AttributeName": "login_as", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "Shop", "PrimaryId": {"AttributeName": "name", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}, {"Attributes": [{"AttributeName": "age", "AttributeType": {"Name": "UINT"}}, {"AttributeName": "gender", "AttributeType": {"Name": "STRING"}}, {"AttributeName": "annual_salary", "AttributeType": {"Name": "DOUBLE"}}], "Config": {"PRIMARY_ID_AS_ATTRIBUTE": true, "STATS": "OUTDEGREE_BY_EDGETYPE"}, "IsLocal": true, "Name": "Person", "PrimaryId": {"AttributeName": "name", "AttributeType": {"Name": "STRING"}, "PrimaryIdAsAttribute": true}}]}}