import React, { useMemo, useRef, useState } from 'react';
import { Core } from 'cytoscape';
import { parseGraph } from '@tigergraph/models/gvis/insights';

import { CytoscapeExtensions, Schema, GraphRef, SettingType } from '../graph/type';
import Graph from '../graph';

import mediumGraph from './data/mediumGraph.json';
import { getPerfSchema } from './util';

export const ExploreGraphMedium = () => {
  const graphRef = useRef<GraphRef>(null);
  const cyRef = useRef<(Core & CytoscapeExtensions) | null>(null);

  let schema: Schema = useMemo(() => {
    return getPerfSchema();
  }, []);

  const [settings, setSettings] = useState<SettingType>({
    layout: 'Force',
  });

  const [graph, setGraph] = useState(() => {
    let graph = parseGraph(mediumGraph);
    return graph;
  });

  return (
    <div style={{ height: 'calc(100vh - 2em)' }}>
      <Graph
        ref={graphRef}
        parentRef={cyRef}
        schema={schema}
        graph={graph}
        id="medium-graph"
        graphName="MyGraph"
        onGraphChange={setGraph}
        settings={settings}
        onSettingUpdate={(key, value) => {
          setSettings({
            ...settings,
            [key]: value,
          });
        }}
        // Skip the onExpandNode method
        onDoubleClick={(item) => console.log(item)}
      />
    </div>
  );
};
