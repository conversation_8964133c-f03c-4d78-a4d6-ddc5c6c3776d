import React from 'react';
import { ReactNode } from 'react';
import { useStyletron } from 'baseui';
import { PLACEMENT, StatefulPopover } from 'baseui/popover';
import IconButton from './iconButton';
import { MdEdit } from 'react-icons/md';
import ConfirmButtons from './confirmButtons';
import { expand } from 'inline-style-expand-shorthand';

type Props = {
  popover: ReactNode | ((args: { close: () => void }) => ReactNode);
  label?: ReactNode;
  onConfirm?: () => void;
  onCancel?: () => void;
  onOpen?: () => void;
};

export default function IconPopover(props: Props) {
  const [css, theme] = useStyletron();
  const { popover, label, onConfirm, onCancel, onOpen } = props;

  return (
    <StatefulPopover
      dismissOnEsc={false}
      dismissOnClickOutside={false}
      ignoreBoundary={true}
      popperOptions={{
        modifiers: {
          preventOverflow: {
            enabled: true,
          },
        },
      }}
      content={({ close }) => (
        <div
          className={css({
            display: 'flex',
            flexDirection: 'column',
            rowGap: '8px',
          })}
        >
          {typeof popover === 'function' ? popover({ close }) : popover}
          {onConfirm && (
            <ConfirmButtons
              onConfirm={() => {
                close();
                onConfirm();
              }}
              onCancel={() => {
                close();
                onCancel && onCancel();
              }}
            />
          )}
        </div>
      )}
      placement={PLACEMENT.left}
      onOpen={() => onOpen && onOpen()}
      returnFocus
      autoFocus
      overrides={{
        Body: {
          style: {
            ...expand({
              padding: '8px',
              borderRadius: '5px',
            }),
            backgroundColor: '#fff',
            width: '320px',
            boxSizing: 'border-box',
          },
        },
        Inner: {
          style: {
            backgroundColor: '#fff',
            // flex: 1,
          },
        },
      }}
    >
      {label ? (
        label
      ) : (
        <IconButton
          $style={{
            color: theme.colors.accent,
          }}
        >
          <MdEdit size={16} />
        </IconButton>
      )}
    </StatefulPopover>
  );
}
