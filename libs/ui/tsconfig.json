{
  "compilerOptions": {
    /* Visit https://aka.ms/tsconfig.json to read more about this file */

    "target": "es2018",
    "lib": ["dom", "dom.iterable", "esnext"],
    "jsx": "react",

    "module": "esnext",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,

    "declaration": true,
    "declarationDir": "dist",

    "strict": true,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    "skipLibCheck": true,

    "pretty": true,
    "allowJs": true
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "node_modules/**", "**/*.test.ts", "**/*.test.tsx", "src/stories"]
}
