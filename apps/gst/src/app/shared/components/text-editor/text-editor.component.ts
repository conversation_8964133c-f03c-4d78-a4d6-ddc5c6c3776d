import {
  OnInit,
  AfterViewInit, Component, ElementRef, EventEmitter,
  forwardRef, Input, OnChanges, OnDestroy, Output, SimpleChanges, ViewChild
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { merge } from 'lodash';
import { Subject, fromEvent, timer } from 'rxjs';
import { debounceTime, filter, map, takeUntil } from 'rxjs/operators';

// Import codemirror addons.
import 'codemirror/addon/edit/closebrackets';
import 'codemirror/addon/fold/brace-fold';
import 'codemirror/addon/fold/comment-fold';
import 'codemirror/addon/fold/foldcode';
import 'codemirror/addon/fold/foldgutter';
import 'codemirror/addon/lint/lint';
import 'codemirror/addon/hint/show-hint';
import 'codemirror/addon/mode/simple';
import 'codemirror/addon/selection/active-line';
import 'codemirror/keymap/sublime';
import 'codemirror/mode/javascript/javascript';
//
import * as CodeMirror from 'codemirror';
import { EditorChange, EditorConfiguration, EditorFromTextArea } from 'codemirror';

import { TextEditorService } from './text-editor.service';

export const TEXT_EDITOR_VALUE_ACCESSOR: any = {
  provide: NG_VALUE_ACCESSOR,
  // tslint:disable-next-line:no-use-before-declare
  useExisting: forwardRef(() => TextEditorComponent),
  multi: true
};

/**
 * Change event object that is emitted when text editor's value changes.
 *
 * @export
 * @class TextEditorEvent
 */
export class TextEditorValueChange {
  constructor(public source: EditorFromTextArea, public value: string) { }
}

/**
 * Component to render the text editor interface.
 * Usage example:
 *  <app-text-editor [config]="config" (change)="onChange($event)"></app-text-editor>
 *  if you want to display a long string in one line. plz aad this style
 *      app-text-editor {
          overflow-x: scroll;
        }

 *
 * @export
 * @class TextEditorComponent
 * @implements {ControlValueAccessor}
 * @implements {AfterViewInit}
 * @implements {OnDestroy}
 */
@Component({
  selector: 'app-text-editor',
  templateUrl: './text-editor.component.html',
  styleUrls: ['./text-editor.component.scss'],
  providers: [
    TEXT_EDITOR_VALUE_ACCESSOR,
    TextEditorService
  ]
})
export class TextEditorComponent implements ControlValueAccessor, OnInit, AfterViewInit, OnDestroy, OnChanges {
  @ViewChild('editorContainer', { static: true }) editorContainer: ElementRef;

  @Input() config: EditorConfiguration;
  @Input() canEdit = true;

  @Output() change = new EventEmitter<TextEditorValueChange>();

  get value(): string {
    return this._value;
  }

  set value(value: string) {
    if (value !== this._value) {
      this._value = value;
      this.setViewPortMargin();
      this.onChange(value);
    }
  }

  private _value = '';
  private textEditor: EditorFromTextArea;
  private defaultViewportMargin: any;

  private destroyed = new Subject<void>();

  private onChange: (value: any) => void = () => {};
  private onTouched: () => void = () => {};

  constructor(private cmptService: TextEditorService) { }

  // Search result count is not correct when a word is selected.
  // Refer: https://github.com/codemirror/CodeMirror/issues/6395
  // Intercept ctrl/cmd + F and remove word selection.
  onKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'f' && event.metaKey) {
      // @types/codemirror need to upgrade to include unSelection() method.
      // But this require typescript >= 3
      (<any>this.textEditor.getDoc()).undoSelection();
    }
  }

  ngOnInit() {
    document.addEventListener('keydown', this.onKeyDown);
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.canEdit && !changes.canEdit.firstChange) {
      this.textEditor.setOption('tabindex', this.canEdit ? 0 : 1);
    }
  }

  ngAfterViewInit() {
    const defaultConfig = this.cmptService.getConfig();
    if (!this.config) {
      this.config = defaultConfig;
    } else {
      this.config = merge(defaultConfig, this.config);
    }

    this.textEditor = CodeMirror.fromTextArea(
      this.editorContainer.nativeElement,
      this.config
    );
    // NOTE: must call this right after initialization or bad things will happen.
    this.textEditor.refresh();

    this.defaultViewportMargin = this.textEditor.getOption('viewportMargin');
    this.setViewPortMargin();

    this.setAddons();
    this.textEditor.setSize('100%', '100%');
    this.textEditor.setValue(this._value);
    this.handleTextEditorEvent();
  }

  ngOnDestroy() {
    this.destroyed.next();
    this.destroyed.complete();

    document.removeEventListener('keydown', this.onKeyDown);
  }

  /**
   * Update codemirror option.
   *
   * @param {string} option
   * @param {*} value
   * @memberof TextEditorComponent
   */
  updateOption(option: string, value: any) {
    this.textEditor.setOption(<keyof EditorConfiguration>option, value);
  }

  /**
   * Call codemirror to perform lint.
   *
   * @memberof TextEditorComponent
   */
  performLint() {
    (<any>this.textEditor).performLint();
  }

  /**
   * Trigger codemirror to load hidden line.
   *
   * @memberof TextEditorComponent
   */
  triggerLoad() {
    const scrollInfo = this.textEditor.getScrollInfo();
    // Perform a scroll to trigger codemirror render event.
    timer()
      .pipe(takeUntil(this.destroyed))
      .subscribe(() => this.textEditor.scrollTo(scrollInfo.left, scrollInfo.top));
  }

  /**
   * Sets the select's value. Part of the ControlValueAccessor interface
   * required to integrate with Angular's core forms API.
   *
   * @param {string} value
   * @memberof TextEditorComponent
   */
  writeValue(value: string) {
    this._value = value || '';
    if (this.textEditor) {
      this.setViewPortMargin();
      this.textEditor.setValue(this._value);
      this.textEditor.getDoc().clearHistory();
    }
  }

  /**
   * Saves a callback function to be invoked when the select's value
   * changes from user input. Part of the ControlValueAccessor interface
   * required to integrate with Angular's core forms API.
   *
   * @param {(value: any) => void} fn
   * @memberof TextEditorComponent
   */
  registerOnChange(fn: (value: any) => void) {
    this.onChange = fn;
  }

  /**
   * Saves a callback function to be invoked when the select is blurred
   * by the user. Part of the ControlValueAccessor interface required
   * to integrate with Angular's core forms API.
   *
   * @param {() => {}} fn
   * @memberof TextEditorComponent
   */
  registerOnTouched(fn: () => {}) {
    this.onTouched = fn;
  }

  /**
   * Handler for text editor event.
   *
   * @private
   * @memberof TextEditorComponent
   */
  private handleTextEditorEvent() {
    fromEvent(this.textEditor, 'change')
      .pipe(
        takeUntil(this.destroyed),
        debounceTime(1000),
      )
      .subscribe(() => {
        this.value = this.textEditor.getValue();
        this.onTouched();
        this.change.emit(new TextEditorValueChange(this.textEditor, this.value));
      });

    fromEvent<(EditorFromTextArea | EditorChange)[]>(this.textEditor, 'inputRead')
      .pipe(
        takeUntil(this.destroyed),
        debounceTime(1000),
        map(values => <EditorFromTextArea>values[0]),
        filter(editor => !editor.state.completionActive)
      )
      .subscribe(editor => (<any>editor).showHint({ completeSingle: false }));
  }

  /**
   * Set codemirror addons.
   *
   * @private
   * @memberof TextEditorComponent
   */
  private setAddons() {
    this.textEditor.setOption('autoCloseBrackets', true);
    this.textEditor.setOption('lint', true);
    this.textEditor.setOption(<keyof EditorConfiguration>'lintOnChange', false);
    this.textEditor.setOption(<keyof EditorConfiguration>'matchBrackets', true);
    this.textEditor.setOption('styleActiveLine', true);
    this.textEditor.setOption('foldGutter', {
      rangeFinder: new (<any>CodeMirror).fold.combine(
        (<any>CodeMirror).fold.brace,
        (<any>CodeMirror).fold.comment
      )
    });
    this.textEditor.on('beforeChange', (cm, change) => {
      if (change.origin === 'paste') {
        const newText = change.text.map(line =>
          line.replace(/\t/g, ' '.repeat(this.config.tabSize))
        );
        change.update(null, null, newText);
      }
    });
  }

  private setViewPortMargin() {
    if (!this.textEditor) {
      return;
    }

    // Want to search EVERYTHING, you need to render everything, and pay the speed cost.
    // Refer: https://github.com/codemirror/CodeMirror/issues/4491
    // If value's length is too large, fallback to default setting.
    if (this._value.length > 100 * 1024) {
      this.textEditor.setOption('viewportMargin', this.defaultViewportMargin);
    } else {
      this.textEditor.setOption('viewportMargin', Infinity);
    }
  }
}
