import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { eventStream } from './sse-client';
import { parseJSON } from '@tigergraph/models';


/**
 * Service to make Http request and return the response from Server-Sent Events(SSE).

 * It acts like HttpClient from the user perspective. At the moment,
 * usage of this service is only advisable for long request.
 *
 * @export
 * @class HttpSourceService
 */
@Injectable({
  providedIn: 'root'
})
export class HttpSourceService {
  get<T>(url: string, init?: RequestInit): Observable<T> {
    return fetchSSE<T>(url, 'get', init);
  }

  post<T>(url: string, init?: RequestInit): Observable<T> {
    return fetchSSE<T>(url, 'post', init);
  }

  put<T>(url: string, init?: RequestInit): Observable<T> {
    return fetchSSE<T>(url, 'put', init);
  }

  delete<T>(url: string, init?: RequestInit): Observable<T> {
    return fetchSSE<T>(url, 'delete', init);
  }
}

function fetchSSE<T>(url: string, method: string, init?: RequestInit): Observable<T> {
  if (!init) {
    init = {};
  }
  if (!init.headers) {
    init.headers = {};
  }
  init.headers['Request-Type'] = 'long';
  init.headers['Accept'] = 'text/event-stream';
  init.method = method;
  init.credentials = 'include';

  const ret = new Observable<T>(observer => void (async () => {
    const baseURL = sessionStorage.getItem('BASEURL') || window.location.origin;
    const promise = fetch(`${baseURL + url}`, init);
    let error = false
    const eventsPromise = promise.then((res) => {
      if (!res.ok) {
        error = true;
        res.json().then((data) => {
          observer.error(data.message || 'Failed to get data, please check network connection and service status.');
        }).catch(() => {
          observer.error('Failed to get data, please check network connection and service status.');
        });
        return;
      }

      return eventStream(res.body);
    });
    eventsPromise.then(async (events) => {
      if (error) {
        return;
      }

      try {
        for await (const event of events) {
          if (observer.closed) {
            return;
          }
          const data: {
            error: boolean;
            message: string;
            results: T;
          } = parseJSON(event.data);
          if (data.error) {
            console.log(data.message);
            observer.error(data.message);
            break;
          }
          observer.next(data.results);
        }
        observer.complete();
      } catch (e) {
        observer.error(e);
      }
    });
  })());
  return ret;
}
