import { NO_ERRORS_SCHEMA } from '@angular/core';
import {
  async, discardPeriodicTasks, fakeAsync, tick,
  ComponentFixture, TestBed
} from '@angular/core/testing';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateModule } from '@ngx-translate/core';
import { of, throwError } from 'rxjs';

import { AuthService, CacheService, Logger, MessageBus } from '@app/core/services';
import { SchemaViewerComponent } from '@app/shared/components/schema-viewer';
import { QueryMeta } from '@tigergraph/models/query';
import { QuestionFormBuilderService } from '@app/shared/services';
import { FormatValidator, GSQLPrivilege } from '@tigergraph/models/utils';

import { QueryEditorComponent } from './query-editor.component';
import { QueryEditorService, ToolbarButtons } from './query-editor.service';
import { QueryEditorLogicService } from './shared';
import { HttpParams } from '@angular/common/http';
import { PostMessageService } from '@app/shared/services/post-message.service';
import { FormControl, FormGroup } from '@angular/forms';

class MockIntConstant {
  className = 'IntConstant';
  value = 55;

  toJson() {
    return {
      type: this.className,
      value: this.value
    };
  }
}

class MockSchemaViewer extends SchemaViewerComponent {
  renderChart() { }
}

class MockVisualEditorComponent {
  setSchema() { }
  highlight() { }
  addData() { }
  switchGraph() { }
  clearData() { }
  getGraphSize() { }
  confirmApplyExploration() { }
  get isReady() { return of({}); }
}

class MockQueryEditorComponent {
  handleSave() { }
  performLint() { }
  triggerLoad() { }
  updateOption() { }
}

class MockDynamicFormComponent {
  form = {
    value: { name: 'abc' }
  };
}

class MockQueryEditorLogicService {
  queriesMeta = new Map();
  get formRunConfig() {
    return {
      timeOut: 16,
      runMemory: 0,
      timeOutLimit: true,
      memoryLimit: false
    };
  }
  updateQueriesMeta() { }
  getQueryMeta() { }
  getHeaders() { return {}; }
  updateQueryMetaDraft() { }
  updateQueryContentAtFrontEndSide() { }
  getUnsavedQueries() { }
  getFrontEndQueryCode() { }
  getServerQueryCode() { }
  getSaveAsQueryName() { }
}

class MockQueryEditorService {
  installQuery() { }
  addQueryDraftToGSQL() { }
  createQueryDraft() { }
  updateQueryDraft() { }
  deleteQueryDraft() { }
  deleteQuery() { }
  runQuery() { }
  getGraphUpdateTagForQuery() { }
  getParamsForInterpretedQuery() { }
  runQueryInInterpretedMode() { }
  createConfigForm() {}
  checkIfPassedCodeCheck() { }
  buildQuestions() { }
  getVertexParamInputs() { }
  clearCodeCheckError() { }
  buildParamsTemplate() { }

  getReplicationFactor() {
    return of(1);
  }

  getGSQLTypeNames() {
    return of({});
  }

  getQueryMetaList() {
    return of([]);
  }

  getPortNumber() {
    return of(9000);
  }

  getRunQueryTimeout() {
    return of(16);
  }

  getRunQueryMemoryLimit() {
    return of(0);
  }

  getUserUploadedIconNames() {
    return of([]);
  }

  checkCode() {
    const json = {
      errors: [],
      warnings: []
    };
    return of(json);
  }

  getSchema() {
    const json = {
      GraphName: 'graph_name',
      VertexTypes: [
        {
          Name: 'v1',
          PrimaryId: {
            AttributeName: 'attr',
            AttributeType: { Name: 'INT' }
          },
          Attributes: [],
          Config: {}
        }
      ],
      EdgeTypes: [
        {
          Name: 'e1',
          FromVertexTypeName: 'v1',
          ToVertexTypeName: 'v1',
          IsDirected: true,
          Config: {},
          Attributes: []
        }
      ]
    };
    return of(json);
  }

  getSchemaStyle() {
    const json = {
      vertexStyles: {},
      edgeStyles: {}
    };
    return of(json);
  }

  getQueryToolbarConfig() {
    const toolbarTop = [
      { key: 'viewChange' },
      { key: 'save' },
      { key: 'saveAs' },
      { key: 'install' },
      { key: 'console' },
      { key: 'run', child: {key: 'subRun'} },
      { key: 'deleteQuery' },
      { key: 'showEndpoint' },
      { key: 'downloadGsql' },
      { key: 'deleteDraft' },
      { key: 'help' },
    ];

    return {
      top: toolbarTop
    };
  }

  getResultToolbarConfig() {
    const toolbarLeft = [
      { key: 'viewChange' },
      { key: 'schema' },
      { key: 'graph' },
      { key: 'json' },
      { key: 'table' },
      { key: 'log' }
    ];

    return {
      left: toolbarLeft
    };
  }

  getResultToolbarConfigReflow() {
    const toolbarLeft = [
      { key: 'schema' },
      { key: 'graph' },
      { key: 'json' },
      { key: 'table' },
      { key: 'log' }
    ];

    return {
      left: toolbarLeft
    };
  }

  getQueryTemplates() {
    return of([]);
  }

  installQueryTemplates() {
    return of([]);
  }

  generateDefaultQueryContent() {}
  logPageView() {}
  logRunQuery() {}
  logSaveQuery() {}
  logInstallQuery() {}
  logInstallAllQueries() {}
  logInstallScienceLibrary() {}
}

class MockQuestionFormBuilderService {
  buildQuestions() { }
  buildOptions() { }
  buildParamsForInterpretedMode() { }
  buildParamsForInstalledMode() { }
  getVertexParamInputs() { }
}

describe('QueryEditorComponent', () => {
  let component: QueryEditorComponent;
  let fixture: ComponentFixture<QueryEditorComponent>;
  let logicService: QueryEditorLogicService;
  let cmptService: QueryEditorService;
  let graphVisualizer: MockVisualEditorComponent;
  let queryEditor: MockQueryEditorComponent;
  let jsonViewer: MockQueryEditorComponent;
  let dialog: MatDialog;
  let snackBar: MatSnackBar;
  let questionFormBuilder: QuestionFormBuilderService;
  let authService: AuthService;
  let cacheService: CacheService;
  let postMessageService: PostMessageService;
  let spyGetItem: jasmine.Spy;
  let spySetItem: jasmine.Spy;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [
        TranslateModule.forRoot()
      ],
      declarations: [QueryEditorComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        {
          provide: MatDialog,
          useFactory: () => ({
            open: () => { }
          })
        },
        {
          provide: MatSnackBar,
          useFactory: () => ({
            open: () => { }
          })
        },
        {
          provide: AuthService,
          useFactory: () => ({
            getUserRole: () => 'superuser',
            getCurrentGraph: () => 'graph_name',
            hasPrivilege: () => true
          })
        },
        {
          provide: PostMessageService,
          useFactory: () => ({
            isInIframe: () => false,
            postCanDeactivate: (canDeactivate: boolean) => false,
          })
        },
        {
          provide: Logger,
          useFactory: () => ({
            warn: () => { }
          })
        },
        {
          provide: MessageBus,
          useFactory: () => ({
            from: () => of(),
            to: () => { },
            resetChannel: () => { }
          })
        },
        {
          provide: CacheService,
          useFactory: () => ({
            getItem: () => { },
            setItem: () => { },
            removeItem: () => { }
          })
        },
        {
          provide: QuestionFormBuilderService,
          useClass: MockQuestionFormBuilderService
        }
      ]
    })
      .overrideComponent(QueryEditorComponent, {
        set: {
          providers: [
            {
              provide: QueryEditorService,
              useClass: MockQueryEditorService
            },
            {
              provide: QueryEditorLogicService,
              useClass: MockQueryEditorLogicService
            }
          ]
        }
      })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(QueryEditorComponent);
    component = fixture.componentInstance;
    dialog = TestBed.get(MatDialog);
    component.schemaContainer = new MockSchemaViewer(<any>new MessageBus(), dialog);
    graphVisualizer = component.graphVisualizer = <any>new MockVisualEditorComponent();
    queryEditor = component.queryEditor = <any>new MockQueryEditorComponent();
    spyOn((<any>component).unsavedQueriesSubject, 'subscribe').and.returnValue([]);
    fixture.detectChanges();
  });

  beforeEach(() => {
    /**
     * Define this after change detection because this component is rendered by *ngIf.
     * And by default it is not rendered.
     */
    jsonViewer = component.jsonViewer = <any>new MockQueryEditorComponent();

    logicService = fixture.debugElement.injector.get(QueryEditorLogicService);
    cmptService = fixture.debugElement.injector.get(QueryEditorService);
    questionFormBuilder = fixture.debugElement.injector.get(QuestionFormBuilderService);
    authService = fixture.debugElement.injector.get(AuthService);
    cacheService = fixture.debugElement.injector.get(CacheService);
    dialog = TestBed.get(MatDialog);
    snackBar = TestBed.get(MatSnackBar);
    postMessageService = TestBed.get(PostMessageService);
    spyGetItem = spyOn(cacheService, 'getItem');
    spySetItem = spyOn(cacheService, 'setItem');
    component.runConfigurationForm = new FormGroup({
      timeOut: new FormControl(
        10
      ),
      runMemory: new FormControl(
        0
      ),
      timeOutLimit: new FormControl(false),
      memoryLimit: new FormControl(false)
    });
    component.query = <any>{ queryName: 'queryName', syntax: 'GSQL' };
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  describe('should get schema on initialization', () => {
    beforeEach(() => {
      spyOn(<any>component, 'getSchemaStyle');
    });

    it('with existing schema', () => {
      spyOn(cmptService, 'getSchema').and.callThrough();
      component.ngOnInit();
      expect((<any>component).getSchemaStyle).toHaveBeenCalled();
    });

    it('with no graph schema', () => {
      spyOn(cmptService, 'getSchema').and.returnValue(of(undefined));
      component.ngOnInit();
      expect((<any>component).getSchemaStyle).not.toHaveBeenCalled();
    });

    it('with failure', () => {
      spyOn(cmptService, 'getSchema').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(<any>component, 'handleError').and.callThrough();
      component.ngOnInit();

      expect((<any>component).getSchemaStyle).not.toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should get schema style and apply it to the schema', () => {
    beforeEach(() => {
      spyOn(component.schema, 'applyGraphStyle');
      spyOn(graphVisualizer, 'switchGraph');
    });

    it('with existing style', () => {
      spyOn(cmptService, 'getSchemaStyle').and.callThrough();
      component.ngOnInit();

      expect(component.schema.applyGraphStyle).toHaveBeenCalled();
      expect(graphVisualizer.switchGraph).toHaveBeenCalled();
    });

    it('with no style', () => {
      spyOn(cmptService, 'getSchemaStyle').and.returnValue(of(undefined));
      component.ngOnInit();

      expect(component.schema.applyGraphStyle).not.toHaveBeenCalled();
      expect(graphVisualizer.switchGraph).toHaveBeenCalled();
    });

    it('with failure', () => {
      spyOn(<any>cmptService, 'getSchemaStyle').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(<any>component, 'handleError').and.callThrough();
      component.ngOnInit();

      expect(component.schema.applyGraphStyle).not.toHaveBeenCalled();
      expect(graphVisualizer.switchGraph).not.toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  it('should still try to apply previous graph result from cache when schema has changed', () => {
    spyOn(cmptService, 'getSchema').and.callThrough();
    spyGetItem.and.returnValue({ GraphName: 'graph_name', VertexTypes: [], EdgeTypes: [] });
    spyOn(cacheService, 'removeItem');
    spyOn(graphVisualizer, 'confirmApplyExploration');
    component.ngOnInit();

    expect(cacheService.removeItem).toHaveBeenCalledTimes(1);
    expect(cacheService.setItem).toHaveBeenCalledTimes(2);
    expect(graphVisualizer.confirmApplyExploration).toHaveBeenCalled();
  });

  describe('should apply previous result', () => {
    beforeEach(() => {
      spyOn(graphVisualizer, 'confirmApplyExploration');
      spyOn(<any>component, 'changeQueryResultView');
    });

    it('with no previous saved graph chart result', () => {
      spyGetItem.and.returnValue(undefined);
      component.ngOnInit();

      expect(graphVisualizer.confirmApplyExploration).not.toHaveBeenCalled();
      expect((<any>component).changeQueryResultView).not.toHaveBeenCalled();
    });

    it('with previous saved graph chart result', () => {
      const result = {
        latestGraph: { nodes: [{ id: 'v', type: 'v1' }], links: [] },
        latestSelections: { nodes: [], links: [] },
        latestLayout: 'tree'
      };
      spyGetItem.and.callFake(function(arg) {
        if (arg === 'QueryEditorVisualResult_graph_name') {
          return result;
        } else {
          return undefined;
        }
      });
      component.ngOnInit();

      expect(graphVisualizer.confirmApplyExploration).toHaveBeenCalledWith({
        schema: undefined,
        data: {
          explorationResult: result
        }
      });
      expect((<any>component).changeQueryResultView).not.toHaveBeenCalled();
    });
  });

  describe('should notify loading status', () => {
    beforeEach(() => {
      spyOn(component.schema, 'applyGraphStyle');
      spyOn(graphVisualizer, 'switchGraph');
    });

    it('if udt list is not ready, loading status is still on', () => {
      cmptService.getSchemaStyle('graph_name');
      spyOnProperty(graphVisualizer, 'isReady', 'get').and.returnValue(undefined);
      component.ngOnInit();

      expect((<any>component).isLoading).toBeTruthy();
    });

    it('if udt list is ready, loading status should be off', () => {
      cmptService.getSchemaStyle('graph_name');
      component.ngOnInit();

      expect((<any>component).isLoading).toBeFalsy();
    });
  });

  it('should be in graph view by default', () => {
    (<any>component.resultToolbarConfig.left[1]).color = 'accent';
    (<any>component.resultToolbarConfig.left[2]).color = '';
    (<any>component.resultToolbarConfig.left[3]).color = '';
  });

  describe('should update query toolbar status', () => {
    beforeEach(() => {
      component.queryToolbarConfig = cmptService.getQueryToolbarConfig();
    });

    it('with no query meta', () => {
      (<any>component).updateQueryToolbarDisableStatus(undefined);

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with query not saved', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue(['q1']);
      (<any>component).updateQueryToolbarDisableStatus({ queryName: 'q1' });

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with query installed and can write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      spyOn(authService, 'hasPrivilege').and.callFake((privilege) => {
        return privilege === GSQLPrivilege.WriteQuery;
      });
      const queryMeta = {
        queryName: 'q1',
        installed: true,
        enabled: true,
        draftCode: '',
        graphUpdate: false
      };
      spyOn(logicService.queriesMeta, 'get').and.returnValue(queryMeta);

      (<any>component).updateQueryToolbarDisableStatus(queryMeta);
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with ReadOnly_query installed and user cannot write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      spyOn(authService, 'hasPrivilege').and.returnValue(false);
      const queryMeta = {
        queryName: 'q1',
        installed: true,
        enabled: true,
        draftCode: '',
        graphUpdate: false
      };
      spyOn(logicService.queriesMeta, 'get').and.returnValue(queryMeta);

      (<any>component).updateQueryToolbarDisableStatus(queryMeta);
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with ReadOnly_query installed and user can write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      const queryMeta = {
        queryName: 'q1',
        installed: true,
        enabled: true,
        originalCode: '',
        draftCode: '',
        graphUpdate: false
      };
      spyOn(logicService.queriesMeta, 'get').and.returnValue(queryMeta);

      (<any>component).updateQueryToolbarDisableStatus(queryMeta);
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with ReadOnly_query saved and user cannot write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      spyOn(authService, 'hasPrivilege').and.callFake((privilege) => {
        if (privilege === GSQLPrivilege.ReadData) {
          return true;
        } else if (privilege === GSQLPrivilege.WriteQuery) {
          return false;
        }
      });

      (<any>component).updateQueryToolbarDisableStatus(
        {
          queryName: 'q1',
          installed: false,
          enabled: true,
          draftCode: 'draft',
          graphUpdate: false
        });
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with ReadOnly_query saved and user can write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);

      (<any>component).updateQueryToolbarDisableStatus(
        {
          queryName: 'q1',
          installed: false,
          enabled: true,
          originalCode: '',
          draftCode: 'draft',
          graphUpdate: false
        });
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeTruthy();
    });

    it('with DML_query installed and user cannot write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      spyOn(authService, 'hasPrivilege').and.returnValue(false);
      const queryMeta = {
        queryName: 'q1',
        installed: true,
        enabled: true,
        draftCode: '',
        graphUpdate: true
      };
      spyOn(logicService.queriesMeta, 'get').and.returnValue(queryMeta);

      (<any>component).updateQueryToolbarDisableStatus(queryMeta);
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with DML_query installed and user can write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      const queryMeta = {
        queryName: 'q1',
        installed: true,
        enabled: true,
        draftCode: '',
        graphUpdate: true
      };
      spyOn(logicService.queriesMeta, 'get').and.returnValue(queryMeta);

      (<any>component).updateQueryToolbarDisableStatus(queryMeta);
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with DML_query saved and user cannot write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      spyOn(authService, 'hasPrivilege').and.callFake((privilege) => {
        if (privilege === GSQLPrivilege.WriteQuery) {
          return false;
        }
      });

      (<any>component).updateQueryToolbarDisableStatus(
        {
          queryName: 'q1',
          installed: false,
          enabled: true,
          draftCode: 'draft',
          graphUpdate: true
        });
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });

    it('with DML_query saved and user can write query', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);

      (<any>component).updateQueryToolbarDisableStatus(
        {
          queryName: 'q1',
          installed: false,
          enabled: true,
          originalCode: '',
          draftCode: 'draft',
          graphUpdate: true
        });
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeTruthy();
    });

    it('with query added from gsql but not installed and user has all privileges', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      const queryMeta = { queryName: 'q1', draftCode: '' };
      logicService.queriesMeta.set('q1', <any>queryMeta);

      component.queryName = 'q1';
      (<any>component).updateQueryToolbarDisableStatus(queryMeta);
      (<any>component).updateQueryToolbarDisableStatusBasedOnUserPrivilege();

      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Save]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.SaveAs]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Publish]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.Run]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteQuery]).disabled).toBeFalsy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.ShowEndpoint]).disabled).toBeTruthy();
      expect((<any>component.queryToolbarConfig.top[ToolbarButtons.DeleteDraft]).disabled).toBeTruthy();
    });
  });

  describe('should sync query list', () => {
    const queriesMeta = [
      { name: 'q3' },
      { name: 'q1' },
      { name: 'q2' },
      { name: 'q4' }
    ];

    beforeEach(() => {
      logicService.queriesMeta.set('q1', <any>{
        draftCode: 'abc',
        installing: true,
        installed: false
      });
      logicService.queriesMeta.set('q2', <any>{
        draftCode: '',
        installing: false,
        installed: false
      });
      logicService.queriesMeta.set('q3', <any>{
        draftCode: '',
        installing: false,
        installed: true
      });
      logicService.queriesMeta.set('q4', <any>{
        draftCode: 'abc',
        installing: false,
        installed: false
      });

      spyOn(component, 'syncQueryList').and.callThrough();
    });

    it('with success', fakeAsync(() => {
      spyOn(cmptService, 'getQueryMetaList').and.returnValue(of(queriesMeta));
      spyOn(component, 'filterQueryList');

      component.syncQueryList();

      expect(component.syncQueryList).toHaveBeenCalledTimes(1);
      discardPeriodicTasks();
    }));

    it('with failure', fakeAsync(() => {
      const logger = TestBed.get(Logger);
      spyOn(cmptService, 'getQueryMetaList').and.returnValues(
        throwError({ error: 'error', statusText: 'Error' }),
        of(queriesMeta)
      );
      spyOn(logger, 'warn');

      component.syncQueryList();

      expect(component.syncQueryList).toHaveBeenCalledTimes(1);
      expect(logger.warn).toHaveBeenCalledTimes(1);
      discardPeriodicTasks();
    }));
  });

  describe('should handle code change event', () => {
    beforeEach(() => {
      spyOn(<any>component, 'updateQueryContentAtFrontEndSide').and.callThrough();
      spyOn(cmptService, 'clearCodeCheckError');
      spyOn(queryEditor, 'performLint');
    });

    it('with empty code', () => {
      component.onCodeChange(<any>{ value: undefined });

      expect((<any>component).updateQueryContentAtFrontEndSide).not.toHaveBeenCalled();
      expect(cmptService.clearCodeCheckError).not.toHaveBeenCalled();
      expect(queryEditor.performLint).not.toHaveBeenCalled();
    });

    it('with non-empty query name', () => {
      (<any>component).currentCode.subscribe(value => expect(value).toBe('abc'));

      component.queryName = 'abc';
      component.onCodeChange(<any>{ value: 'abc' });

      expect((<any>component).updateQueryContentAtFrontEndSide).toHaveBeenCalled();
      expect(cmptService.clearCodeCheckError).not.toHaveBeenCalled();
      expect(queryEditor.performLint).not.toHaveBeenCalled();
    });

    it('with empty query name', () => {
      component.queryName = '';
      component.onCodeChange(<any>{ value: 'abc' });

      expect((<any>component).updateQueryContentAtFrontEndSide).not.toHaveBeenCalled();
      expect(cmptService.clearCodeCheckError).toHaveBeenCalled();
      expect(queryEditor.performLint).toHaveBeenCalled();
    });
  });

  describe('should check code on code change', () => {
    let codeSpy: jasmine.Spy;

    beforeEach(() => {
      codeSpy = spyOn(cmptService, 'checkCode');
      spyOn(queryEditor, 'performLint');
    });

    it('with success and code errors', fakeAsync(() => {
      codeSpy.and.returnValue(of({
        errors: [
          { message: 'error', startLine: 0, startColumn: 0 }
        ],
        warnings: [
          { message: 'warning', startLine: 0, startColumn: 0 }
        ]
      }));
      (<any>component).currentCode.next('abc');
      tick(501);

      expect(queryEditor.performLint).toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('(1, 0) Error: error\n(1, 0) Warning: warning\n');
      discardPeriodicTasks();
    }));

    it('with success and empty errors', fakeAsync(() => {
      codeSpy.and.returnValue(of({
        errors: [],
        warnings: []
      }));
      (<any>component).currentCode.next('abc');
      tick(501);

      expect(queryEditor.performLint).toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('No log to show');
      discardPeriodicTasks();
    }));

    it('with success and no errors', fakeAsync(() => {
      codeSpy.and.returnValue(of(undefined));
      (<any>component).currentCode.next('abc');
      tick(501);

      expect(queryEditor.performLint).toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('No log to show');
      discardPeriodicTasks();
    }));

    it('with failure', fakeAsync(() => {
      spyOn(<any>component, 'handleError').and.callThrough();
      codeSpy.and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      (<any>component).currentCode.next('abc');
      tick(501);

      expect(queryEditor.performLint).not.toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('No log to show');
      expect((<any>component).handleError).toHaveBeenCalled();
      discardPeriodicTasks();
    }));
  });

  describe('should check for changes on window close', () => {
    it('with changes', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue(['q1']);
      spyOn(postMessageService, 'isInIframe').and.returnValue(false);
      expect((<any>component).onBeforeUnload()).toBeFalsy();
    });

    it('with no changes', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      spyOn(postMessageService, 'isInIframe').and.returnValue(false);
      expect((<any>component).onBeforeUnload()).toBeTruthy();
    });
  });

  describe('should check for changes on navigation', () => {
    const dialogRef = {
      afterClosed: () => { }
    };

    beforeEach(() => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);
    });

    it('with changes and user select "Leave"', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue(['q1']);
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(1));
      component.canDeactivate(false).subscribe(value => expect(value).toBeTruthy());
    });

    it('with changes and user select "Cancel"', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue(['q1']);
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(0));
      component.canDeactivate(false).subscribe(value => expect(value).toBeFalsy());
    });

    it('with no changes', () => {
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      component.canDeactivate(false).subscribe(value => expect(value).toBeTruthy());
    });
  });

  it('should split view when "view change" button clicked on query toolbar', () => {
    spyOn(queryEditor, 'triggerLoad');

    component.onQueryInteraction({ key: 'viewChange' });
    expect(component.showTopRight).toBe(true);
    expect(component.showTopLeft).toBe(false);
    expect(component.showBottom).toBe(false);
    expect((<any>component.queryToolbarConfig.top[0]).icon).toBe('fullscreen_exit');
    expect((<any>component.queryToolbarConfig.top[0]).tooltip).toBe('Collapse');
    expect(queryEditor.triggerLoad).toHaveBeenCalled();

    component.onQueryInteraction({ key: 'viewChange' });
    expect(component.showTopLeft).toBe(true);
    expect(component.showTopRight).toBe(true);
    expect(component.showBottom).toBe(true);
    expect((<any>component.queryToolbarConfig.top[0]).icon).toBe('fullscreen');
    expect((<any>component.queryToolbarConfig.top[0]).tooltip).toBe('Expand');
  });

  it('should split view when "view change" button clicked on result toolbar', () => {
    component.onResultInteraction({ key: 'viewChange' });
    expect(component.showTopRight).toBe(false);
    expect(component.showTopLeft).toBe(false);
    expect(component.showBottom).toBe(true);
    expect((<any>component.resultToolbarConfig.left[0]).icon).toBe('fullscreen_exit');
    expect((<any>component.resultToolbarConfig.left[0]).tooltip).toBe('Collapse');

    component.onResultInteraction({ key: 'viewChange' });
    expect(component.showTopLeft).toBe(true);
    expect(component.showTopRight).toBe(true);
    expect(component.showBottom).toBe(true);
    expect((<any>component.resultToolbarConfig.left[0]).icon).toBe('fullscreen');
    expect((<any>component.resultToolbarConfig.left[0]).tooltip).toBe('Expand');

    // For when currently in json viewer.
    spyOn(jsonViewer, 'triggerLoad');
    spyOn(component, 'inJsonView').and.returnValue(true);

    component.onResultInteraction({ key: 'viewChange' });
    component.onResultInteraction({ key: 'viewChange' });
    expect(jsonViewer.triggerLoad).toHaveBeenCalled();
  });

  it('should change query result view', () => {
    component.onResultInteraction({ key: 'graph' });
    (<any>component.resultToolbarConfig.left[1]).color = 'accent';
    (<any>component.resultToolbarConfig.left[2]).color = '';
    (<any>component.resultToolbarConfig.left[3]).color = '';

    component.onResultInteraction({ key: 'json' });
    (<any>component.resultToolbarConfig.left[1]).color = '';
    (<any>component.resultToolbarConfig.left[2]).color = 'accent';
    (<any>component.resultToolbarConfig.left[3]).color = '';

    component.onResultInteraction({ key: 'log' });
    (<any>component.resultToolbarConfig.left[1]).color = '';
    (<any>component.resultToolbarConfig.left[2]).color = '';
    (<any>component.resultToolbarConfig.left[3]).color = 'accent';
  });

  it('should open add query window', () => {
    spyOn(dialog, 'open');
    component.openAddNewQueryDialog();
    expect(dialog.open).toHaveBeenCalled();
    expect((<any>component).isAddingNewQuery).toBeTruthy();
  });

  it('should open save query as window', () => {
    spyOn(dialog, 'open');
    component.openSaveQueryAsDialog();
    expect(dialog.open).toHaveBeenCalled();
    expect((<any>component).isSavingQueryAs).toBeTruthy();
  });

  describe('should close add query window', () => {
    it('with open loading window', () => {
      (<any>component).popupWindowRef = {
        close: () => { },
        afterClosed: () => of(1),
      };
      spyOn((<any>component).popupWindowRef, 'close');

      component.closeAddOrSaveQueryDialog();
      expect((<any>component).popupWindowRef.close).toHaveBeenCalled();
    });

    it('with no open loading window', () => {
      (<any>component).popupWindowRef = undefined;
      component.closeAddOrSaveQueryDialog();
    });
  });

  describe('should add new query', () => {
    let saveSpy: jasmine.Spy;

    beforeEach(() => {
      (<any>component).openAddNewQueryDialog();
      component.newQueryName = 'q1';
      component.closeAddOrSaveQueryDialog();

      spyOn(<any>component, 'handleError').and.callThrough();
      saveSpy = spyOn(cmptService, 'createQueryDraft');
      spyOn(component, 'syncQueryList').and.returnValue(of({}));
      spyOn(component, 'showQuery');
      spyOn(cmptService, 'getGSQLTypeNames').and.returnValue(of({
        1: [],
        graph_name: []
      }));
    });

    it('with non-valid query name', () => {
      spyOn(FormatValidator, 'isName').and.returnValue({ success: false });
      component.addQuery();
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('with duplicated query name', () => {
      spyOn(FormatValidator, 'isName').and.returnValue({ success: true });
      logicService.queriesMeta.set('q1', <any>{});
      component.addQuery();
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('with success', () => {
      spyOn(FormatValidator, 'isName').and.returnValue({ success: true });
      saveSpy.and.returnValue(of({}));
      component.addQuery();

      expect(cmptService.createQueryDraft).toHaveBeenCalled();
      expect(component.syncQueryList).toHaveBeenCalledWith('q1');
    });

    it('with failure', () => {
      // Handle the case where popup window is opened.
      (<any>component).popupWindowRef = {
        close: () => { },
        afterClosed: () => of(1),
      };
      spyOn(FormatValidator, 'isName').and.returnValue({ success: true });
      saveSpy.and.returnValue(throwError({ error: 'error', statusText: 'error' }));
      component.addQuery();

      expect(cmptService.createQueryDraft).toHaveBeenCalled();
      expect(component.syncQueryList).not.toHaveBeenCalled();
      expect(component.showQuery).not.toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should show query code', () => {
    beforeEach(() => {
      spyOn(cmptService, 'clearCodeCheckError');
      spyOn(queryEditor, 'performLint');
      spyOn(queryEditor, 'updateOption');
      spyOn(<any>component, 'updateQueryToolbarDisableStatus');
      spyOn(<any>component, 'shouldRunInInterpretedMode').and.returnValue(false);
    });

    it('with existing query', () => {
      logicService.queriesMeta.set('q1', <any>{});
      component.showQuery('q1');

      expect(cmptService.clearCodeCheckError).toHaveBeenCalled();
      expect(queryEditor.performLint).toHaveBeenCalled();
      expect(queryEditor.updateOption).toHaveBeenCalledTimes(3);
      expect((<any>component).updateQueryToolbarDisableStatus).toHaveBeenCalled();
    });

    it('with non-existing query', () => {
      spyOn(<any>component, 'handleError').and.callThrough();
      component.showQuery('q1');

      expect(cmptService.clearCodeCheckError).toHaveBeenCalled();
      expect(queryEditor.performLint).toHaveBeenCalled();
      expect(queryEditor.updateOption).not.toHaveBeenCalled();
      expect((<any>component).updateQueryToolbarDisableStatus).not.toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should save query', () => {
    beforeEach(() => {
      spyOn(snackBar, 'open');
    });

    it('with no selected query', () => {
      component.queryName = undefined;

      component.onQueryInteraction({ key: 'save' });
      expect(snackBar.open).not.toHaveBeenCalled();
    });

    it('with selected query', () => {
      spyOn((<any>component), 'handleSave');

      component.queryName = 'q1';
      component.onQueryInteraction({ key: 'save' });

      expect((<any>component).handleSave).toHaveBeenCalled();
    });

    it('with success', () => {
      spyOn(cmptService, 'updateQueryDraft').and.returnValue(of({}));
      spyOn((<any>component), 'showQueryIsSavedSnackBar');
      spyOn((<any>component), 'updateQueryToolbarAndButton');
      spyOn(logicService, 'getQueryMeta').and.returnValue({});
      spyOn(logicService, 'updateQueryMetaDraft');
      spyOn((<any>component), 'updateQueryContentAtFrontEndSide');
      spyOn(<any>component, 'queryNameInCodeIsValid').and.returnValue(true);

      component.queryName = 'q1';
      (<any>component).handleSave();

      expect(cmptService.updateQueryDraft).toHaveBeenCalled();
      expect((<any>component).showQueryIsSavedSnackBar).toHaveBeenCalled();
      expect((<any>component).updateQueryToolbarAndButton).toHaveBeenCalledWith({});
      expect(logicService.updateQueryMetaDraft).toHaveBeenCalled();
      expect((<any>component).updateQueryContentAtFrontEndSide).toHaveBeenCalled();
    });

    it('with failure', () => {
      spyOn(cmptService, 'updateQueryDraft').and.returnValue(
        throwError({ error: 'error', status: 400, statusText: 'Error' })
      );
      spyOn((<any>component), 'showQueryIsSavedSnackBar');
      spyOn((<any>component), 'updateQueryToolbarAndButton');
      spyOn(logicService, 'getQueryMeta').and.returnValue({});
      spyOn(logicService, 'updateQueryMetaDraft');
      spyOn(logicService, 'updateQueryContentAtFrontEndSide');
      spyOn(<any>component, 'queryNameInCodeIsValid').and.returnValue(true);

      component.queryName = 'q1';
      (<any>component).handleSave();

      expect(cmptService.updateQueryDraft).toHaveBeenCalled();
      expect((<any>component).showQueryIsSavedSnackBar).not.toHaveBeenCalled();
      expect((<any>component).updateQueryToolbarAndButton).not.toHaveBeenCalledWith({});
      expect(logicService.updateQueryMetaDraft).not.toHaveBeenCalled();
      expect(logicService.updateQueryContentAtFrontEndSide).not.toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('error');
    });
  });

  describe('should save query as a new query under a new name', () => {
    let saveSpy: jasmine.Spy;

    beforeEach(() => {
      component.queryName = 'myQuery';
      spyOn(logicService, 'getSaveAsQueryName').and.returnValue('myQuery_1');
      component.openSaveQueryAsDialog();
      spyOn(<any>component, 'handleError').and.callThrough();
      saveSpy = spyOn(cmptService, 'createQueryDraft');
      spyOn(component, 'syncQueryList').and.returnValue(of({}));
      spyOn(component, 'showQuery');
      spyOn(cmptService, 'getGSQLTypeNames').and.returnValue(of({
        1: [],
        graph_name: []
      }));
      spyOn(snackBar, 'open');
    });

    it('with default query name', () => {
      spyOn(FormatValidator, 'isName').and.returnValue({ success: true });
      expect(component.newQueryName).toBe('myQuery_1');
    });

    it('with non-valid query name', () => {
      spyOn(FormatValidator, 'isName').and.returnValue({ success: false });
      component.addQuery();
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('with duplicated query name', () => {
      spyOn(FormatValidator, 'isName').and.returnValue({ success: true });
      logicService.queriesMeta.set('myQuery_1', <any>{});
      component.addQuery();
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('with success', () => {
      saveSpy.and.returnValue(of({}));
      spyOn((<any>component), 'closeAddOrSaveQueryDialog');
      spyOn((<any>component), 'showQueryIsSavedSnackBar');
      spyOn(FormatValidator, 'isName').and.returnValue({ success: true });
      spyOn((<any>component), 'updateQueryToolbarAndButton');
      spyOn((<any>component), 'updateQueryContentAtFrontEndSide');

      (<any>component).handleSaveQueryAs();

      expect(cmptService.createQueryDraft).toHaveBeenCalled();
      expect(component.closeAddOrSaveQueryDialog).toHaveBeenCalled();
      expect((<any>component).showQueryIsSavedSnackBar).toHaveBeenCalledWith('myQuery_1');
      expect(component.syncQueryList).toHaveBeenCalledWith('myQuery_1');
      expect((<any>component).updateQueryContentAtFrontEndSide).toHaveBeenCalled();
      expect(component.newQueryName).toEqual('');
    });

    it('with failure', () => {
      // Handle the case where popup window is opened.
      (<any>component).popupWindowRef = {
        close: () => { },
        afterClosed: () => of(1),
      };
      spyOn(FormatValidator, 'isName').and.returnValue({ success: true });
      saveSpy.and.returnValue(throwError({ error: 'error', statusText: 'error' }));

      (<any>component).handleSaveQueryAs();

      expect(cmptService.createQueryDraft).toHaveBeenCalled();
      expect(component.syncQueryList).not.toHaveBeenCalled();
      expect(component.showQuery).not.toHaveBeenCalled();
      expect(snackBar.open).not.toHaveBeenCalled();
      expect((<any>component).handleError).toHaveBeenCalled();
    });

    it('replace query name in query content', () => {
      component.queryContent = 'CREATE DISTRIBUTED QUERY myQuery() { }';
      let expectedQueryContent = 'CREATE DISTRIBUTED QUERY myQuery_1() { }';
      let newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);

      component.queryContent = 'CREATE QUERY myQuery() { \n // QUERY myQuery( \n // QUERY myQuery }' +
        '\n CREATE QUERY myQuery () {}';
      expectedQueryContent = 'CREATE QUERY myQuery_1() { \n // QUERY myQuery_1( \n // QUERY myQuery }' +
        '\n CREATE QUERY myQuery_1 () {}';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);

      component.queryContent = '\t CREATE \n DISTRIBUTED\n   QUERY \t\n myQuery() { }';
      expectedQueryContent = '\t CREATE \n DISTRIBUTED\n   QUERY \t\n myQuery_1() { }';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);

      component.queryContent = 'aaa CREATE DISTRIBUTED QUERY myQuery() { }';
      expectedQueryContent = 'aaa CREATE DISTRIBUTED QUERY myQuery_1() { }';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);

      component.queryContent = '// comment \nCREATE DISTRIBUTED QUERY myQuery() { }';
      expectedQueryContent = '// comment \nCREATE DISTRIBUTED QUERY myQuery_1() { }';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);

      component.queryContent = '/* comment */ CREATE DISTRIBUTED QUERY myQuery() { }';
      expectedQueryContent = '/* comment */ CREATE DISTRIBUTED QUERY myQuery_1() { }';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);

      component.queryContent = '// CREATE DISTRIBUTED QUERY myQuery() { }';
      expectedQueryContent = '// CREATE DISTRIBUTED QUERY myQuery_1() { }';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);

      component.queryContent = '/* CREATE DISTRIBUTED QUERY myQuery() { } */';
      expectedQueryContent = '/* CREATE DISTRIBUTED QUERY myQuery_1() { } */';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);

      component.queryContent = 'QUERY myQuery() { }';
      expectedQueryContent = 'QUERY myQuery_1() { }';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(expectedQueryContent);
    });

    it('keep query content as is', () => {
      component.queryContent = 'CREATE QUERY DISTRIBUTED  myQuery() { }';
      let newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(component.queryContent);

      component.queryContent = 'CREATE QUERY () { }';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(component.queryContent);

      component.queryContent = 'CREATE QUERY myQuery { }';
      newQueryContent = (<any>component).getQueryContentUnderNewName();
      expect(newQueryContent).toEqual(component.queryContent);
    });
  });

  it('should show "query is saved" snack bar', () => {
    spyOn(snackBar, 'open');

    (<any>component).showQueryIsSavedSnackBar();
    expect(snackBar.open).toHaveBeenCalled();
  });

  describe('should handle pre-install query', () => {
    beforeEach(() => {
      component.queryName = 'q1';

      spyOn(snackBar, 'open');
      spyOn(<any>component, 'handleError').and.callThrough();
      spyOn(<any>component, 'installQuery');
      spyOn(<any>component, 'noMatchReplace').and.returnValue(false);
    });

    it('with non-existing query', () => {
      component.onQueryInteraction({ key: 'install' });
      expect((<any>component).handleError).toHaveBeenCalled();
      expect((<any>component).installQuery).not.toHaveBeenCalled();
    });

    it('with unsaved query', () => {
      logicService.queriesMeta.set('q1', <any>{});
      spyOn(logicService, 'getUnsavedQueries').and.returnValue(['q1']);
      component.onQueryInteraction({ key: 'install' });

      expect((<any>component).handleError).toHaveBeenCalled();
      expect((<any>component).installQuery).not.toHaveBeenCalled();
    });

    it('with already installed query', () => {
      logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: true, enabled: true });
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      component.onQueryInteraction({ key: 'install' });

      expect((<any>component).handleError).toHaveBeenCalled();
      expect((<any>component).installQuery).not.toHaveBeenCalled();
    });

    it('with code check success and there are code errors', () => {
      logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: false });
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      spyOn(cmptService, 'checkCode').and.returnValue(of({ errors: [{}] }));
      component.onQueryInteraction({ key: 'install' });

      expect((<any>component).handleError).toHaveBeenCalled();
      expect((<any>component).installQuery).not.toHaveBeenCalled();
    });

    describe('with code check success and there is no code error', () => {
      beforeEach(() => {
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        spyOn(cmptService, 'checkCode').and.returnValue(of({ errors: [] }));
      });

      it('and there is draft code and add query success', () => {
        logicService.queriesMeta.set('q1', <any>{ draftCode: 'abc', installed: false });
        const result = {
          success: [{
            name: 'q1',
            errorMessage: ''
          }],
          failed: []
        };
        spyOn(cmptService, 'addQueryDraftToGSQL').and.returnValue(of(result));
        spyOn(<any>component, 'queryNameInCodeIsValid').and.returnValue(true);
        component.onQueryInteraction({ key: 'install' });

        expect((<any>component).installQuery).toHaveBeenCalled();
      });

      it('and there is draft code and add query fail', () => {
        logicService.queriesMeta.set('q1', <any>{ draftCode: 'abc', installed: false });
        spyOn(cmptService, 'addQueryDraftToGSQL').and.returnValue(
          throwError({ error: 'error', status: 500, statusText: 'Error' })
        );
        spyOn(<any>component, 'queryNameInCodeIsValid').and.returnValue(true);
        component.onQueryInteraction({ key: 'install' });

        expect((<any>component).installQuery).not.toHaveBeenCalled();
        expect(component.logContent.getValue()).toBe('error');
      });

      it('and there is no draft code', () => {
        logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: false });
        component.onQueryInteraction({ key: 'install' });

        expect((<any>component).installQuery).not.toHaveBeenCalled();
        expect(component.installQueryWindow).toBeTruthy();
      });
    });

    it('with code check failure', () => {
      logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: false });
      spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
      spyOn(cmptService, 'checkCode').and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      component.onQueryInteraction({ key: 'install' });

      expect((<any>component).handleError).toHaveBeenCalled();
      expect((<any>component).installQuery).not.toHaveBeenCalled();
    });
  });

  describe('should install query', () => {
    let installSpy: jasmine.Spy;

    beforeEach(() => {
      installSpy = spyOn(cmptService, 'installQuery');
      spyOn(snackBar, 'open');
      spyOn(component, 'syncQueryList').and.returnValue(of({}));
    });

    xit('with success', () => {
      installSpy.and.returnValue(of({ success: [], failed: [] }));
      (<any>component).installQuery([]);

      expect(snackBar.open).toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('Query install successfully');
    });

    xit('with failure', () => {
      installSpy.and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
      spyOn(<any>component, 'handleError').and.callThrough();
      (<any>component).installQuery([]);

      expect(snackBar.open).not.toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('No log to show');
      expect((<any>component).handleError).toHaveBeenCalled();
    });
  });

  describe('should install all queries', () => {
    const queriesCanBeInstalled = {
      queriesToBeInstalled: ['q1'],
      queriesToBeAddedToGSQL: ['q1']
    };

    beforeEach(() => {
      spyOn((<any>component), 'getQueriesHavingWrongName').and.returnValue([]);
      spyOn((<any>component), 'limitDisplayQueryNameList').and.returnValues(['q1'], ['q1']);
      spyOn(logicService, 'getServerQueryCode').and.returnValue(['abc']);
      spyOn(cmptService, 'addQueryDraftToGSQL').and.returnValue(of());
      spyOn(<any>component, 'noMatchReplace').and.returnValue(false);
    });

    it('with success', () => {
      spyOn(cmptService, 'checkIfPassedCodeCheck').and.returnValue(of([true]));

      (<any>component).installAllQueries(queriesCanBeInstalled);

      expect(cmptService.checkIfPassedCodeCheck).toHaveBeenCalled();
      expect(cmptService.addQueryDraftToGSQL).toHaveBeenCalled();
    });

    it('with code check failure', () => {
      spyOn(cmptService, 'checkIfPassedCodeCheck').and.returnValue(of([false]));

      (<any>component).installAllQueries(queriesCanBeInstalled);

      expect(cmptService.checkIfPassedCodeCheck).toHaveBeenCalled();
      expect(cmptService.addQueryDraftToGSQL).not.toHaveBeenCalled();
    });
  });

  describe('should run query', () => {
    let runSpy: jasmine.Spy;
    let checkCodeSpy: jasmine.Spy;
    let interpretedParamSpy: jasmine.Spy;
    let interpretedRunSpy: jasmine.Spy;
    let queryMeta;

    beforeEach(() => {
      component.queryName = 'q1';
      queryMeta = {
        queryName: 'q1',
        installed: false,
        enabled: true,
        graphUpdate: false,
        callerQueries: [],
        installing: false,
        params: [],
        originalCode: '',
        draftCode: ''
      };

      logicService.runConfiguration = {
        timeOut: {
          value: 16,
          defaultValue: 16,
          limit: true
        },
        runMemory: {
          value: 0,
          defaultValue: 0,
          limit: true
        },
        GSQLReplica: {
          value: 0,
          defaultValue: 0,
          limit: false
        }
      };

      spyOn(<any>component, 'handleError').and.callThrough();
      runSpy = spyOn(cmptService, 'runQuery');
      checkCodeSpy = spyOn(cmptService, 'checkCode');
      interpretedParamSpy = spyOn(cmptService, 'getParamsForInterpretedQuery');
      interpretedRunSpy = spyOn(cmptService, 'runQueryInInterpretedMode');
      spyOn(<any>component, 'handleQueryResponse');
    });

    describe('without params', () => {
      it('with non-existing query', () => {
        component.onQueryInteraction({ key: 'run' });
        expect(cmptService.runQuery).not.toHaveBeenCalled();
      });

      it('with unsaved query', () => {
        logicService.queriesMeta.set('q1', <any>{});
        spyOn(logicService, 'getUnsavedQueries').and.returnValue(['q1']);
        component.onQueryInteraction({ key: 'run' });

        expect((<any>component).handleError).toHaveBeenCalled();
        expect(cmptService.runQuery).not.toHaveBeenCalled();
      });

      it('with non-installed query which is added from gsql', () => {
        logicService.queriesMeta.set('q1', queryMeta);
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        checkCodeSpy.and.returnValue(of({ errors: [] }));
        interpretedParamSpy.and.returnValue(of({ params: [] }));
        spyOn(logicService, 'getServerQueryCode').and.returnValue('abc');
        interpretedRunSpy.and.returnValue(of({}));

        component.onQueryInteraction({ key: 'run' });

        expect((<any>component).handleError).not.toHaveBeenCalled();
        expect(cmptService.getParamsForInterpretedQuery).toHaveBeenCalledWith('abc', 'graph_name');
        expect(cmptService.runQuery).not.toHaveBeenCalled();
        expect(cmptService.runQueryInInterpretedMode).toHaveBeenCalledWith(
          'abc',
          new HttpParams().set('graph', 'graph_name'),
          {}
        );
        expect((<any>component).handleQueryResponse).toHaveBeenCalled();
      });

      it('with non-installed but saved query', () => {
        const queryCode = 'CREATE DISTRIBUTED QUERY test() { }';
        queryMeta.draftCode = queryCode;
        logicService.queriesMeta.set('q1', queryMeta);
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        checkCodeSpy.and.returnValue(of({ errors: [] }));
        interpretedParamSpy.and.returnValue(of({ params: [] }));
        spyOn(logicService, 'getFrontEndQueryCode').and.returnValue(queryCode);
        interpretedRunSpy.and.returnValue(of({}));

        component.onQueryInteraction({ key: 'run' });

        expect((<any>component).handleError).not.toHaveBeenCalled();
        expect(cmptService.getParamsForInterpretedQuery).toHaveBeenCalled();
        expect(cmptService.runQuery).not.toHaveBeenCalled();
        expect(cmptService.runQueryInInterpretedMode).toHaveBeenCalledWith(
          'INTERPRET QUERY () { }',
          new HttpParams().set('graph', 'graph_name'),
          {}
        );
        expect((<any>component).handleQueryResponse).toHaveBeenCalled();
      });

      it('in interpreted mode and check code fail', () => {
        logicService.queriesMeta.set('q1', queryMeta);
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        spyOn(logicService, 'getServerQueryCode').and.returnValue('abc');
        checkCodeSpy.and.returnValue(of({ errors: [{}] }));
        component.onQueryInteraction({ key: 'run' });

        expect(cmptService.runQuery).not.toHaveBeenCalled();
        expect((<any>component).handleQueryResponse).not.toHaveBeenCalled();
        expect((<any>component).handleError).toHaveBeenCalled();
      });

      it('in interpreted mode and run fail', () => {
        logicService.queriesMeta.set('q1', queryMeta);
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        checkCodeSpy.and.returnValue(of({ errors: [] }));
        interpretedParamSpy.and.returnValue(of({ params: [] }));
        spyOn(logicService, 'getServerQueryCode').and.returnValue('abc');
        interpretedRunSpy.and.returnValue(throwError({ error: 'error', statusText: 'Error' }));

        component.onQueryInteraction({ key: 'run' });

        expect(cmptService.getParamsForInterpretedQuery).toHaveBeenCalled();
        expect(cmptService.runQuery).not.toHaveBeenCalled();
        expect(cmptService.runQueryInInterpretedMode).toHaveBeenCalled();
        expect((<any>component).handleQueryResponse).toHaveBeenCalled();
        expect((<any>component).handleError).toHaveBeenCalled();
      });

      it('with no params query and success', () => {
        logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: true, params: [] });
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        runSpy.and.returnValue(of({}));
        component.onQueryInteraction({ key: 'run' });

        expect(cmptService.runQuery).toHaveBeenCalled();
        expect((<any>component).handleQueryResponse).toHaveBeenCalled();
      });

      it('with no params query and failure', () => {
        logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: true, params: [] });
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        runSpy.and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
        component.onQueryInteraction({ key: 'run' });

        expect(cmptService.runQuery).toHaveBeenCalled();
        expect((<any>component).handleQueryResponse).toHaveBeenCalled();
        expect((<any>component).handleError).toHaveBeenCalled();
      });

      it('with params query', () => {
        spyOn(questionFormBuilder, 'buildQuestions');

        logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: true, params: [{}] });
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        component.onQueryInteraction({ key: 'run' });

        expect(cmptService.runQuery).not.toHaveBeenCalled();
        expect((<any>component).handleQueryResponse).not.toHaveBeenCalled();
        expect(questionFormBuilder.buildQuestions).toHaveBeenCalled();
      });
    });

    describe('with params', () => {
      it('with non-existing query', () => {
        component.runQueryWithParams();
        expect(cmptService.runQuery).not.toHaveBeenCalled();
      });

      it('with unsaved query', () => {
        logicService.queriesMeta.set('q1', <any>{});
        spyOn(logicService, 'getUnsavedQueries').and.returnValue(['q1']);
        component.runQueryWithParams();

        expect((<any>component).handleError).toHaveBeenCalled();
        expect(cmptService.runQuery).not.toHaveBeenCalled();
      });

      it('with non-installed running in interpreted mode', () => {
        logicService.queriesMeta.set('q1', queryMeta);
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        spyOn(<any>component, 'shouldRunInInterpretedMode').and.returnValue(true);
        interpretedRunSpy.and.returnValue(of({}));
        spyOn(graphVisualizer, 'highlight');
        const params = new HttpParams();
        spyOn(questionFormBuilder, 'buildParamsForInterpretedMode').and.returnValue(params);

        (<any>component).interpretedQueryMeta = queryMeta;
        (<any>component).interpretedQueryContent = 'abc';
        component.dynamicForm = <any>new MockDynamicFormComponent;
        component.runQueryWithParams();

        expect((<any>component).handleError).not.toHaveBeenCalled();
        expect(cmptService.runQuery).not.toHaveBeenCalled();
        expect(cmptService.runQueryInInterpretedMode).toHaveBeenCalledWith(
          'abc',
          params.append('graph', 'graph_name'),
          {},
        );
      });

      it('with success', () => {
        spyOn(graphVisualizer, 'highlight');

        component.dynamicForm = <any>new MockDynamicFormComponent;
        logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: true, params: [] });
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        spyOn(questionFormBuilder, 'buildParamsForInstalledMode').and.returnValue({});
        runSpy.and.returnValue(of({}));
        component.runQueryWithParams();

        expect(cmptService.runQuery).toHaveBeenCalled();
        expect((<any>component).handleQueryResponse).toHaveBeenCalled();
        expect(graphVisualizer.highlight).toHaveBeenCalled();
      });

      it('with no params query and failure', () => {
        spyOn(graphVisualizer, 'highlight');

        component.dynamicForm = <any>new MockDynamicFormComponent;
        logicService.queriesMeta.set('q1', <any>{ draftCode: '', installed: true, params: [] });
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        spyOn(questionFormBuilder, 'buildParamsForInstalledMode').and.returnValue({});
        runSpy.and.returnValue(throwError({ error: 'error', statusText: 'Error' }));
        component.runQueryWithParams();

        expect(cmptService.runQuery).toHaveBeenCalled();
        expect((<any>component).handleQueryResponse).toHaveBeenCalled();
        expect(graphVisualizer.highlight).not.toHaveBeenCalled();
        expect((<any>component).handleError).toHaveBeenCalled();
      });

      it('with interpreted mode and failure', () => {
        logicService.queriesMeta.set('q1', queryMeta);
        spyOn(logicService, 'getUnsavedQueries').and.returnValue([]);
        spyOn(<any>component, 'shouldRunInInterpretedMode').and.returnValue(true);
        spyOn(questionFormBuilder, 'buildParamsForInterpretedMode').and.returnValue(new HttpParams());
        interpretedRunSpy.and.returnValue(throwError( { error: 'error', statusText: 'Error' }));
        spyOn(graphVisualizer, 'highlight');

        (<any>component).interpretedQueryMeta = queryMeta;
        (<any>component).interpretedQueryContent = 'abc';
        component.dynamicForm = <any>new MockDynamicFormComponent;
        component.runQueryWithParams();

        expect(cmptService.runQuery).not.toHaveBeenCalled();
        expect(cmptService.runQueryInInterpretedMode).toHaveBeenCalled();
        expect((<any>component).handleQueryResponse).toHaveBeenCalled();
        expect(graphVisualizer.highlight).not.toHaveBeenCalled();
        expect((<any>component).handleError).toHaveBeenCalled();
      });
    });
  });

  describe('should handle query response', () => {
    beforeEach(() => {
      spyOn(graphVisualizer, 'clearData');
    });

    it('with graph structure', () => {
      spyOn(graphVisualizer, 'getGraphSize').and.returnValue([1, 1]);
      (<any>component).handleQueryResponse({});

      expect(graphVisualizer.clearData).toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('Query run successfully');
      expect(component.jsonContent).toBe('{}');
      (<any>component.resultToolbarConfig.left[1]).color = 'accent';
      (<any>component.resultToolbarConfig.left[2]).color = '';
      (<any>component.resultToolbarConfig.left[3]).color = '';
    });

    it('with no graph structure', () => {
      spyOn(graphVisualizer, 'getGraphSize').and.returnValue([0, 0]);
      (<any>component).handleQueryResponse({ 'a': 1 });

      expect(graphVisualizer.clearData).toHaveBeenCalled();
      expect(component.logContent.getValue()).toBe('Query run successfully');
      expect(component.jsonContent).not.toBe('{}');
      (<any>component.resultToolbarConfig.left[1]).color = '';
      (<any>component.resultToolbarConfig.left[2]).color = 'accent';
      (<any>component.resultToolbarConfig.left[3]).color = '';
    });
  });

  describe('should delete query', () => {
    let deleteSpy: jasmine.Spy;
    const dialogRef = {
      afterClosed: () => { }
    };

    beforeEach(() => {
      spyOn(dialog, 'open').and.returnValue(dialogRef);
      deleteSpy = spyOn(cmptService, 'deleteQuery');
      spyOn(snackBar, 'open');
      spyOn(queryEditor, 'updateOption');
      spyOn(component, 'syncQueryList').and.returnValue(of({}));
    });

    it('should disable delete query and popup warning since it has caller queries', () => {
      spyOn(component, 'closeParamPanel');
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(1));
      deleteSpy.and.returnValue(of({}));
      logicService.queriesMeta.set('q1', <any>{ installed: true, enabled: true, callerQueries: ['q2'] });
      component.queryName = 'q1';

      component.onQueryInteraction({ key: 'deleteQuery' });

      expect(snackBar.open).toHaveBeenCalled();
      expect(component.queryName).toBe('');
      expect(component.queryContent).toBe('# Choose a query from the list on the left.');
      expect(queryEditor.updateOption).toHaveBeenCalledTimes(2);
      expect(component.syncQueryList).toHaveBeenCalled();
    });

    it('with user select "Continue" and success', () => {
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(1));
      deleteSpy.and.returnValue(of({}));
      component.onQueryInteraction({ key: 'deleteQuery' });

      expect(snackBar.open).toHaveBeenCalled();
      expect(component.queryName).toBe('');
      expect(component.queryContent).toBe('# Choose a query from the list on the left.');
      expect(queryEditor.updateOption).toHaveBeenCalledTimes(2);
      expect(component.syncQueryList).toHaveBeenCalled();
    });

    it('with user select "Continue" and failure', () => {
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(1));
      deleteSpy.and.returnValue(throwError({ error: 'error', statusText: 'error' }));
      component.onQueryInteraction({ key: 'deleteQuery' });

      expect(snackBar.open).not.toHaveBeenCalled();
      expect(queryEditor.updateOption).not.toHaveBeenCalled();
      expect(component.syncQueryList).not.toHaveBeenCalled();
    });

    it('with user select "Cancel"', () => {
      spyOn(dialogRef, 'afterClosed').and.returnValue(of(0));
      component.onQueryInteraction({ key: 'deleteQuery' });
      expect(cmptService.deleteQuery).not.toHaveBeenCalled();
    });
  });

  it('should show query endpoint', () => {
    logicService.queriesMeta.set('q1', <any>{ installed: true, params: [] });
    component.queryName = 'q1';
    spyOn(cmptService, 'buildParamsTemplate').and.returnValue('');
    (<any>component).popupWindowRef = {
      close: () => { },
      afterClosed: () => of(1),
    };
    component.onQueryInteraction({ key: 'showEndpoint' });

    expect((<any>component).popupWindowRef).toBeDefined();
  });

  it('should save graph chart result to cache', () => {
    const result = {
      latestGraph: { nodes: [], links: [] },
      latestSelections: { nodes: [], links: [] },
      latestLayout: 'force'
    };
    component.onQueryResultUpdated(result);
    expect(cacheService.setItem).toHaveBeenCalledWith('QueryEditorVisualResult_graph_name', result);
  });

  it('should get correct icon for query list', () => {
    const queryMetaInstalling: QueryMeta = {
      queryName: 'myQuery',
      installed: true,
      enabled: false,
      graphUpdate: false,
      callerQueries: [],
      installing: true,
      isHidden: false,
      isACLSpecified: false,
      params: [],
      originalCode: 'CREATE DISTRIBUTED QUERY myQuery() { }',
      draftCode: 'CREATE DISTRIBUTED QUERY myQuery() { # Choose a query from the list on the left. }'
    };
    const queryMetaDisabled: QueryMeta = {
      queryName: 'myQuery',
      installed: true,
      enabled: false,
      graphUpdate: false,
      callerQueries: [],
      installing: false,
      isHidden: false,
      isACLSpecified: false,
      params: [],
      originalCode: 'CREATE DISTRIBUTED QUERY myQuery() { }',
      draftCode: ''
    };
    const queryMetaInstalledAndSameCode: QueryMeta = {
      queryName: 'myQuery',
      installed: true,
      enabled: true,
      graphUpdate: false,
      callerQueries: [],
      installing: false,
      isHidden: false,
      isACLSpecified: false,
      params: [],
      originalCode: 'CREATE DISTRIBUTED QUERY myQuery() { }',
      draftCode: 'CREATE DISTRIBUTED QUERY myQuery() { }'
    };
    const queryMetaInstalledAndDiffCode: QueryMeta = {
      queryName: 'myQuery',
      installed: true,
      enabled: true,
      graphUpdate: false,
      callerQueries: [],
      installing: false,
      isHidden: false,
      isACLSpecified: false,
      params: [],
      originalCode: 'CREATE DISTRIBUTED QUERY myQuery() { }',
      draftCode: 'CREATE DISTRIBUTED QUERY myQuery() { # Choose a query from the list on the left. }'
    };
    const queryMetaInstalled: QueryMeta = {
      queryName: 'myQuery',
      installed: true,
      enabled: true,
      graphUpdate: false,
      callerQueries: [],
      installing: false,
      isHidden: false,
      isACLSpecified: false,
      params: [],
      originalCode: 'CREATE DISTRIBUTED QUERY myQuery() { }',
      draftCode: ''
    };

    expect(component.queryStatus(queryMetaInstalling)).toBe('installing');
    expect(component.queryStatus(queryMetaDisabled)).toBe('disabled');
    expect(component.queryStatus(queryMetaInstalledAndSameCode)).toBe('installed');
    expect(component.queryStatus(queryMetaInstalledAndDiffCode)).toBe('draft');
    expect(component.queryStatus(queryMetaInstalled)).toBe('installed');
  });
});
