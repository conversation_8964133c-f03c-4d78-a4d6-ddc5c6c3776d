import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AuthService, StatCollector } from '@app/core/services';
import { InformantMetricLogic, InformantMetricType } from '@tigergraph/models/informant';
import { groupBy, pickBy } from 'lodash';
import { map } from 'rxjs/operators';

interface Host {
  Hostname: string;
  ID: string;
  Region: string;
}

export const AllNodes = 'All';
export const AllGraphs = 'All';

interface CurrentQueryInfo {
  elapsedTime: number;
  expirationTime: string;
  requestid: string;
  startTime: string;
  url: string;
  user: string;
  node: string;
}

export interface HistoryQueriesInfo {
  [queryName: string]: HistoryQueriesDetailInfo[];
}

export interface HistoryQueriesDetailInfo {
  detail: {
    QPS: number;
    average_latency: number;
    max_latency: number;
    min_latency: number;
    timeout: number;
  };
  what: string;
  when: number;
  where: string;
  who: string;
}

export interface AbortQueryResponse {
  aborted_queries: AbortQueryInfo[];
  unknown_requestid?: string[];
}

interface AbortQueryInfo {
  requestid: string;
  url: string;
}


const configURL = '/api/config';
const currentQueriesURL = '/api/restpp/showprocesslistall';
const HostListKey = 'System.HostList';
const MaxConcurrentQueriesKey = 'RESTPP.WorkLoadManager.MaxConcurrentQueries';
const AbortCurrentQueryURL = '/api/restpp/abortquery';

@Injectable({
  providedIn: 'root'
})
export class QueriesService {
  queryRegex: RegExp;

  constructor(
    private httpClient: HttpClient,
    private statController: StatCollector,
    private authService: AuthService
  ) {
    this.buildQueryRegex();
  }

  getNodeList() {
    return this.httpClient.get<{
      [HostListKey]: Host[]
    }>(configURL, {
      params: new HttpParams().set('key', HostListKey)
    }).pipe(
      map(hosts => hosts[HostListKey].map(
        host => host.ID
      ))
    );
  }

  getMaxConcurrentQueries() {
    return this.httpClient.get<{
      [MaxConcurrentQueriesKey]: number
    }>(configURL, {
      params: new HttpParams().set('key', MaxConcurrentQueriesKey)
    }).pipe(
      map(max => max[MaxConcurrentQueriesKey])
    );
  }

  getCurrentQueries(node: string) {
    return this.httpClient.get<CurrentQueryInfo[]>(currentQueriesURL, {
      params: new HttpParams().set('node', node)
    }).pipe(
      map(currentQueries => {
        currentQueries.forEach(currentQuery => currentQuery.node = node);
        return currentQueries;
      })
    );
  }

  getHistoryQueriesData(startTime: number, endTime: number) {
    return this.statController.getInformantMetric(InformantMetricType.QPS, startTime, endTime)
      .pipe(
        map(data => InformantMetricLogic.convertQPS(data)),
        // Group the data by query and location.
        // E.g.: [] => { 'q1 - restpp1': [], 'q1 - restpp2': [] }
        map(data => groupBy(data, (value) => `${value.who} - ${value.where}`)),
        // Retain only gsql queries and user's queries.
        map(groups => pickBy(groups, (_, key) => this.queryRegex.test(key))),
        // Fill empty qps data so the UI can display them
        map(groups => {
          Object.keys(groups).forEach(key => {
            const records = groups[key];
            if (!records.length) {
              return;
            }
            // Insert empty qps data before the first record
            let time = records[0].when;
            const scanInterval = 60;
            while (time - scanInterval >= startTime) {
              records.unshift(this.getEmtpyQPSRecord(time - scanInterval, records[0].where, records[0].who));
              time -= scanInterval;
            }
            // Insert empty qps to middle
            for (let i = 0; i < records.length - 1; i++) {
              while (records[i + 1].when - records[i].when >= 2 * scanInterval) {
                records.splice(i + 1, 0, this.getEmtpyQPSRecord(records[i].when + scanInterval, records[0].where, records[0].who));
              }
            }

            // Insert empty qps data after the last record
            time = records[records.length - 1].when;
            endTime -= 60;
            while (time + scanInterval <= endTime) {
              records.push(this.getEmtpyQPSRecord(time + scanInterval, records[0].where, records[0].who));
              time += scanInterval;
            }
          });
          return groups;
        })
      );
  }

  getEmtpyQPSRecord(when: number, where: string, who: string) {
    return {
      detail: {
        QPS: 0,
        average_latency: 0,
        max_latency: 0,
        min_latency: 0,
        timeout: 0
      },
      what: InformantMetricType.QPS,
      when,
      where,
      who,
    };
  }

  abortCurrentQuery(graph: string, requestID: string) {
    return this.httpClient.get<AbortQueryResponse[]>(`${AbortCurrentQueryURL}/${graph}`, {
      params: new HttpParams().set('requestid', requestID)
    });
  }

  /**
   * Build regular expression for query filtering.
   * Regex: \/query\/(g1|g2|g3|...)\/.+
   */
  private buildQueryRegex() {
    let pattern = '\/query';
    const graphList = this.authService.getGraphList();
    const graphNames = graphList.join('|');
    pattern += graphNames.length > 0 ? `\/(${graphNames})` : '';
    pattern += '\/.+';
    this.queryRegex = new RegExp(pattern, 'i');
  }
}
