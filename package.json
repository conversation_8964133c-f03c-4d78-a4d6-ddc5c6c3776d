{"name": "tools", "private": true, "scripts": {"setup": "yarn setup:models && yarn setup:ui && yarn setup:insights && yarn setup:gst && yarn setup:gap && yarn setup:gshell && yarn setup:graphql", "update-models": "cd libs/models && yarn yalc:update && cd ../.. && yarn upgrade:ui && yarn upgrade:insights && yarn upgrade:gst && yarn upgrade:gap && yarn upgrade:gshell && yarn upgrade:graphql && yarn upgrade:home", "update-ui": "cd libs/ui && yarn yalc:update && cd ../.. && yarn upgrade:insights && yarn upgrade:gst", "setup:models": "cd libs/models && yarn && yarn build && cd ../..", "setup:ui": "cd libs/ui && yarn && yarn build && cd ../..", "setup:insights": "cd apps/insights && yarn && cd ../..", "setup:gst": "cd apps/gst && yarn && yarn yalc:upgrade && cd ../..", "setup:gap": "cd apps/gap && yarn && yarn yalc:upgrade && cd ../..", "setup:gshell": "cd apps/gshell && yarn && yarn yalc:upgrade && cd ../..", "setup:graphql": "cd apps/graphql && yarn && yarn yalc:upgrade && cd ../..", "setup:home": "cd apps/home && yarn && cd ../..", "upgrade:ui": "cd libs/ui && yarn yalc:upgrade && cd ../..", "upgrade:insights": "cd apps/insights && yarn yalc:upgrade && cd ../..", "upgrade:gst": "cd apps/gst && yarn yalc:upgrade && cd ../..", "upgrade:gap": "cd apps/gap && yarn yalc:upgrade && cd ../..", "upgrade:gshell": "cd apps/gshell && yarn yalc:upgrade && cd ../..", "upgrade:graphql": "cd apps/graphql && yarn yalc:upgrade && cd ../..", "upgrade:home": "cd apps/home && yarn yalc:upgrade && cd ../.."}}