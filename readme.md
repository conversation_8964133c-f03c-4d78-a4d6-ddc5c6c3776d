## setup

```
yarn setup
```


## update library code

update code in `libs/models` or `libs/ui`, then run `yarn update-models` or `yarn update-ui`, it should take effect without restart dev server.

## technical detail for monorepo build

### why yalc

1. npm|yarn link have many issue
2. monorepo tools like lena is too obtrusive(need to unify lots of fundamental package like node/typescript/angular/react)
3. use tool like `yalc`

please refer [yalc](https://github.com/wclr/yalc) for detail instruction.

### why need to run `npm version patch` and `yarn upgrade`

`angular/create-react-app` dev server have persistent cache based on `babel-loader`

we could

1. disable cache (which need to tweak each tools's configuration)
2. or clear cache after run upgrade package
3. rely on `npm version patch` to increase patch version and run `yarn upgrade` to update `node_modules` and `yarn.lock` which will invalidate `babel-loader` cache

[How to update yarn.lock on version bumps](https://github.com/wclr/yalc/issues/191)
